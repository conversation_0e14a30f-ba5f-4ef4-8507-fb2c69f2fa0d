import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */
import componentsRouter from './modules/components'
import chartsRouter from './modules/charts'
import tableRouter from './modules/table'
import nestedRouter from './modules/nested'

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/home',
    component: () => import('@/views/home/<USER>'),
    hidden: true
  },
  {
    path: '/bid-index',
    component: () => import('@/views/home/<USER>'),
    hidden: true
  },
  {
    path: '/notice-index',
    component: () => import('@/views/home/<USER>'),
    hidden: true
  },
  {
    path: '/open-hall',
    component: () => import('@/views/home/<USER>'),
    hidden: true,
    meta: { title: '开标大厅', icon: 'user', noCache: true }
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { id: 1, title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },
  // {
  //   path: '/documentation',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/documentation/index'),
  //       name: 'Documentation',
  //       meta: { title: 'Documentation', icon: 'documentation', affix: true }
  //     }
  //   ]
  // },
  // {
  //   path: '/guide',
  //   component: Layout,
  //   redirect: '/guide/index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/guide/index'),
  //       name: 'Guide',
  //       meta: { title: 'Guide', icon: 'guide', noCache: true }
  //     }
  //   ]
  // },
  {
    path: '/profile',
    component: Layout,
    redirect: '/profile/index',
    hidden: true,
    children: [
      {
        path: 'index',
        component: () => import('@/views/profile/index'),
        name: 'Profile',
        meta: { title: 'Profile', icon: 'user', noCache: true }
      }
    ]
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  {
    path: '/center',
    component: Layout,
    redirect: '/center/personal-info',
    alwaysShow: true, // will always show the root menu
    name: 'Center',
    meta: {
      id: 2,
      title: '个人中心',
      icon: 'el-icon-user',
      roles: [1, 2, 3, 4] // you can set roles in root nav
    },
    children: [
      {

        path: 'personal-info',
        component: () => import('@/views/center/personal-info/index'),
        name: 'PersonalInfo',
        meta: {
          id: 3,
          title: '个人信息',
          roles: [1, 2, 3, 4]
        }
      },
      {

        path: 'member',
        component: () => import('@/views/center/member/index'),
        name: 'Member',
        meta: {
          id: 4,
          title: '公司成员',
          roles: [2, 3, 4]
        }
      },
      {
        path: 'companies',
        component: () => import('@/views/center/companies/index'),
        name: 'Companies',
        meta: {
          id: 5,
          title: '公司档案',
          roles: [3, 4]
        }
      },
      {

        path: 'pay-record',
        component: () => import('@/views/center/pay-record/index'),
        name: 'PayRecord',
        meta: {
          id: 6,
          title: '缴费记录',
          roles: [1]
        }
      }
    ]
  },
  {
    path: '/bidding',
    component: Layout,
    redirect: '/bidding/project-info',
    alwaysShow: true, // will always show the root menu
    name: 'Bid',
    meta: {
      id: 7,
      title: '招标管理',
      icon: 'el-icon-folder',
      roles: [2, 3] // you can set roles in root nav
    },
    children: [
      {

        path: 'project-info',
        component: () => import('@/views/bidding/project-info/index'),
        name: 'ProjectInfo',
        meta: {
          id: 8,
          title: '项目信息',
          roles: [3]
        }
      },
      {
        path: 'bid-project',
        component: () => import('@/views/bidding/bid-project/index'),
        name: 'BidProject',
        meta: {
          id: 9,
          title: '招标项目',
          roles: [3]
        }
      },
      {
        path: 'bid-announcement',
        component: () => import('@/views/bidding/bid-announcement/index'),
        name: 'BidAnnouncement',
        meta: {
          id: 10,
          title: '招标公告',
          roles: [3]
        }
      },
      {
        path: 'bid-half-announcement',
        component: () => import('@/views/bidding/bid-half-announcement/index'),
        name: 'BidHalfAnnouncement',
        meta: {
          id: 11,
          title: '半流程招投标公告',
          roles: [3]
        }
      },
      {
        path: 'bidder',
        component: () => import('@/views/bidding/bidder/index'),
        name: 'Bidder',
        meta: {
          id: 12,
          title: '招标人库',
          roles: [3]
        }
      },
      {
        path: 'bid-doc-review',
        component: () => import('@/views/bidding/bid-doc-review/index'),
        name: 'BidDocReview',
        meta: {
          id: 13,
          title: '招标文件',
          roles: [3]
        }
      },
      {
        path: 'super-bid-doc-review',
        component: () => import('@/views/bidding/super-bid-doc-review/index'),
        name: 'SuperBidDocReview',
        meta: {
          id: 14,
          title: '招标文件审核',
          roles: [2]
        }
      },
      {
        path: 'super-extract',
        component: () => import('@/views/bidding/super-extract/index'),
        name: 'SuperExtract',
        meta: {
          id: 15,
          title: '平台专家抽取审核',
          roles: [2]
        }
      },
      {
        path: 'bid-project-manage',
        component: () => import('@/views/bid-project-manage/index'),
        name: 'Company',
        meta: { id: 16, title: '招标项目管理', roles: [2] }
      },
      {
        path: 'bid-announcement-manage',
        component: () => import('@/views/bid-announcement-manage/index'),
        name: 'Announcement',
        meta: { id: 17, title: '招标公告管理', roles: [2] }
      },
      {
        path: 'bid-half-announcement-manage',
        component: () => import('@/views/bid-half-announcement/index'),
        name: 'HalfAnnouncement',
        meta: { id: 18, title: '半流程招投标公告管理', roles: [2] }
      },
      {
        path: 'project-manage',
        component: () => import('@/views/project-manage/index'),
        name: 'Company',
        meta: { id: 19, title: '项目管理', roles: [2] }
      },
      // {
      //   path: 'bid-segment',
      //   component: () => import('@/views/bidding/bid-segment/index'),
      //   name: 'BidSegment',
      //   meta: {
      //     title: '标段信息',
      //     roles: ['admin']
      //   }
      // }
    ]
  },
  {
    path: '/tender',
    component: Layout,
    redirect: '/tender/notice-review',
    alwaysShow: true, // will always show the root menu
    name: 'Tender',
    meta: {
      id: 20,
      title: '投标管理',
      icon: 'el-icon-s-cooperation',
      roles: [2, 3, 4] // you can set roles in root nav
    },
    children: [
      {

        path: 'notice-review',
        component: () => import('@/views/tender/notice-review/index'),
        name: 'NoticeReview',
        meta: {
          id: 21,
          title: '中标未中标通知书',
          roles: [2, 3, 4]
        }
      },
      {
        path: 'registrat-invitat',
        component: () => import('@/views/tender/registrat-invitat/index'),
        name: 'RegistratInvitat',
        meta: {
          id: 22,
          title: '报名和邀请',
          roles: [2, 4]
        }
      },
      {
        path: 'bid-track',
        component: () => import('@/views/tender/bid-track/index'),
        name: 'BidTrack',
        meta: {
          id: 23,
          title: '招标项目跟踪',
          roles: [2, 4]
        }
      }
    ]
  },
  {
    path: '/hall',
    component: Layout,
    redirect: '/bid/hall',
    alwaysShow: true, // will always show the root menu
    name: 'Hall',
    meta: {
      id: 24,
      title: '开标大厅',
      icon: 'el-icon-menu',
      roles: [2, 3, 4] // you can set roles in root nav
    },
    children: [
      {
        path: '/hall/invite',
        component: () => import('@/views/hall/invite/index'),
        name: 'InviteHall',
        meta: {
          id: 25,
          title: '招标人大厅',
          roles: [2, 3]
        }
      },
      {
        path: '/hall/bidder',
        component: () => import('@/views/hall/bidder/index'),
        name: 'BidderHall',
        meta: {
          id: 26,
          title: '投标人大厅',
          roles: [2, 4]
        }
      },

      // {
      //   path: '/hall/expert',
      //   component: () => import('@/views/hall/expert/index'),
      //   name: 'ExpertHall',
      //   meta: {
      //     id: 27,
      //     title: '评标大厅',
      //     roles: [2, 5]
      //   }
      // },
    ]
  },
  {
    path: '/expert',
    component: Layout,
    redirect: '/expert/page',
    alwaysShow: true, // will always show the root menu
    name: 'Expert',
    meta: {
      id: 28,
      title: '专家库',
      icon: 'el-icon-suitcase',
      roles: [2, 3] // you can set roles in root nav
    },
    children: [
      {
        path: 'expert-info',
        component: () => import('@/views/expert/manage/index'),
        name: 'Manage',
        meta: {
          id: 29,
          title: '平台专家库管理',
          roles: [3]
        }
      },
      {
        path: 'audit',
        component: () => import('@/views/bidding/extract/index'),
        name: 'Audit',
        meta: {
          id: 30,
          title: '专家抽取审核',
          roles: [3]
        }
      },
      {
        path: 'expert-lib',
        component: () => import('@/views/expert-lib/index'),
        name: 'Company',
        meta: { id: 31, title: '平台专家库管理', roles: [2], icon: 'tab' }
      },
    ]
  },
  {
    path: '/company',
    component: Layout,
    children: [
      {
        path: 'company',
        component: () => import('@/views/company/index'),
        name: 'Company',
        meta: { id: 32, title: '企业管理', roles: [2], icon: 'el-icon-house' }
      }
    ]
  },
  {
    path: '/experthall',
    component: Layout,
    meta: {
      id: 27,
      roles: [2, 5]
    },
    children: [
      {
        path: '/hall/expert',
        component: () => import('@/views/hall/expert/index'),
        name: 'ExpertHall',
        meta: {
          id: 27,
          title: '评标大厅',
          roles: [2, 5],
          icon: 'el-icon-s-order'
        }
      },
    ]
  },

  {
    path: '/expert-info',
    component: Layout,
    children: [
      {
        path: 'expert-info',
        component: () => import('@/views/expert-info/index'),
        name: 'ExpertInfo',
        meta: { id: 33, title: '个人信息', roles: [5], icon: 'el-icon-female' }
      }
    ]
  },
  {
    path: '/expert-files',
    component: Layout,
    children: [
      {
        path: 'expert-files',
        component: () => import('@/views/expert-files/index'),
        name: 'ExpertInfo',
        meta: { id: 34, title: '专家档案', roles: [5], icon: 'el-icon-notebook-1' }
      }
    ]
  },
  {
    path: '/expert-project',
    component: Layout,
    children: [
      {
        path: 'expert-project',
        component: () => import('@/views/expert-project/index'),
        name: 'ExpertInfo',
        meta: { id: 35, title: '项目管理', roles: [5], icon: 'el-icon-set-up' }
      }
    ]
  },
  {
    path: '/expert-invite',
    component: Layout,
    children: [
      {
        path: 'expert-invite',
        component: () => import('@/views/expert-invite/index'),
        name: 'ExpertInfo',
        meta: { id: 36, title: '我的邀请', roles: [5], icon: 'el-icon-s-promotion' }
      }
    ]
  },
  {
    path: '/audit',
    component: Layout,
    redirect: '/audit/bidder',
    alwaysShow: true, // will always show the root menu
    name: 'AuditManage',
    meta: {
      id: 40,
      title: '审核管理',
      icon: 'el-icon-s-check',
      roles: [3] // you can set roles in root nav
    },
    children: [
      {
        path: '/audit/bidder',
        component: () => import('@/views/audit/bidder'),
        name: 'BidderAudit',
        meta: {
          id: 41,
          title: '投标人审核',
          roles: [3]
        }
      },
      {
        path: '/audit/bid-notice/audit',
        component: () => import('@/views/audit/bid-notice/index'),
        name: 'BidNoticeAudit',
        meta: {
          id: 42,
          title: '招标公告内部审核',
          roles: [3]
        }
      },
      {
        path: '/audit/half-bid-notice/audit',
        component: () => import('@/views/audit/half-bid-notice/index'),
        name: 'BidNoticeAudit',
        meta: {
          id: 43,
          title: '半流程招标公告内部审核',
          roles: [3]
        }
      }
    ]
  },

  {
    path: '/permission',
    component: Layout,
    redirect: '/permission/page',
    alwaysShow: true, // will always show the root menu
    name: 'Permission',
    meta: {
      id: 37,
      title: '权限管理',
      icon: 'lock',
      roles: [1, 2, 3, 4] // you can set roles in root nav
    },
    children: [
      {
        path: 'role',
        component: () => import('@/views/permission/role'),
        name: 'RolePermission',
        meta: {
          id: 38,
          title: '角色管理',
          roles: [1, 2, 3, 4]
        }
      }
    ]
  },
  {
    path: '/manage',
    component: Layout,
    children: [
      {
        path: 'manage',
        component: () => import('@/views/manage/index'),
        name: 'Manage',
        meta: { id: 39, title: '首页公告管理', icon: 'el-icon-chat-line-square' }
      }
    ]
  },
  // {
  //   path: '/icon',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/icons/index'),
  //       name: 'Icons',
  //       meta: { title: 'Icons', icon: 'icon', noCache: true }
  //     }
  //   ]
  // },

  /** when your routing map is too long, you can split it into small modules **/
  // componentsRouter,
  // chartsRouter,
  // nestedRouter,
  // tableRouter,

  // {
  //   path: '/example',
  //   component: Layout,
  //   redirect: '/example/list',
  //   name: 'Example',
  //   meta: {
  //     title: 'Example',
  //     icon: 'el-icon-s-help'
  //   },
  //   children: [
  //     {
  //       path: 'create',
  //       component: () => import('@/views/example/create'),
  //       name: 'CreateArticle',
  //       meta: { title: 'Create Article', icon: 'edit' }
  //     },
  //     {
  //       path: 'edit/:id(\\d+)',
  //       component: () => import('@/views/example/edit'),
  //       name: 'EditArticle',
  //       meta: { title: 'Edit Article', noCache: true, activeMenu: '/example/list' },
  //       hidden: true
  //     },
  //     {
  //       path: 'list',
  //       component: () => import('@/views/example/list'),
  //       name: 'ArticleList',
  //       meta: { title: 'Article List', icon: 'list' }
  //     }
  //   ]
  // },

  // {
  //   path: '/tab',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/tab/index'),
  //       name: 'Tab',
  //       meta: { title: 'Tab', icon: 'tab' }
  //     }
  //   ]
  // },

  // {
  //   path: '/error',
  //   component: Layout,
  //   redirect: 'noRedirect',
  //   name: 'ErrorPages',
  //   meta: {
  //     title: 'Error Pages',
  //     icon: '404'
  //   },
  //   children: [
  //     {
  //       path: '401',
  //       component: () => import('@/views/error-page/401'),
  //       name: 'Page401',
  //       meta: { title: '401', noCache: true }
  //     },
  //     {
  //       path: '404',
  //       component: () => import('@/views/error-page/404'),
  //       name: 'Page404',
  //       meta: { title: '404', noCache: true }
  //     }
  //   ]
  // },

  // {
  //   path: '/error-log',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'log',
  //       component: () => import('@/views/error-log/index'),
  //       name: 'ErrorLog',
  //       meta: { title: 'Error Log', icon: 'bug' }
  //     }
  //   ]
  // },

  // {
  //   path: '/excel',
  //   component: Layout,
  //   redirect: '/excel/export-excel',
  //   name: 'Excel',
  //   meta: {
  //     title: 'Excel',
  //     icon: 'excel'
  //   },
  //   children: [
  //     {
  //       path: 'export-excel',
  //       component: () => import('@/views/excel/export-excel'),
  //       name: 'ExportExcel',
  //       meta: { title: 'Export Excel' }
  //     },
  //     {
  //       path: 'export-selected-excel',
  //       component: () => import('@/views/excel/select-excel'),
  //       name: 'SelectExcel',
  //       meta: { title: 'Export Selected' }
  //     },
  //     {
  //       path: 'export-merge-header',
  //       component: () => import('@/views/excel/merge-header'),
  //       name: 'MergeHeader',
  //       meta: { title: 'Merge Header' }
  //     },
  //     {
  //       path: 'upload-excel',
  //       component: () => import('@/views/excel/upload-excel'),
  //       name: 'UploadExcel',
  //       meta: { title: 'Upload Excel' }
  //     }
  //   ]
  // },
  // {
  //   path: '/zip',
  //   component: Layout,
  //   redirect: '/zip/download',
  //   alwaysShow: true,
  //   name: 'Zip',
  //   meta: { title: 'Zip', icon: 'zip' },
  //   children: [
  //     {
  //       path: 'download',
  //       component: () => import('@/views/zip/index'),
  //       name: 'ExportZip',
  //       meta: { title: 'Export Zip' }
  //     }
  //   ]
  // },
  // {
  //   path: '/pdf',
  //   component: Layout,
  //   redirect: '/pdf/index',
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/pdf/index'),
  //       name: 'PDF',
  //       meta: { title: 'PDF', icon: 'pdf' }
  //     }
  //   ]
  // },
  // {
  //   path: '/pdf/download',
  //   component: () => import('@/views/pdf/download'),
  //   hidden: true
  // },
  // {
  //   path: '/theme',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/theme/index'),
  //       name: 'Theme',
  //       meta: { title: 'Theme', icon: 'theme' }
  //     }
  //   ]
  // },
  // {
  //   path: '/clipboard',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       component: () => import('@/views/clipboard/index'),
  //       name: 'ClipboardDemo',
  //       meta: { title: 'Clipboard', icon: 'clipboard' }
  //     }
  //   ]
  // },
  // {
  //   path: 'external-link',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'https://github.com/PanJiaChen/vue-element-admin',
  //       meta: { title: 'External Link', icon: 'link' }
  //     }
  //   ]
  // },
  // 404 page must be placed at the end !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
