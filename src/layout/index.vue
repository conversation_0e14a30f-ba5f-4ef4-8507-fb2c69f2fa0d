<template>
  <div :class="classObj" class="app-wrapper">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar @pwd="handleShowPwd" />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
    </div>
    <passwor-form
      :visible="visible"
      @save="handleSavePwd"
      @close="handleClose"
    ></passwor-form>
  </div>
</template>

<script>
import { AppMain, Navbar, Settings, Sidebar, TagsView } from "./components";
import PassworForm from "./password.vue";
import ResizeMixin from "./mixin/ResizeHandler";
import { mapState } from "vuex";
import { updatePassword } from "@/api/user";

export default {
  name: "Layout",
  components: {
    AppMain,
    Navbar,
    Settings,
    Sidebar,
    TagsView,
    PassworForm,
  },
  mixins: [ResizeMixin],
  data() {
    return {
      visible: false,
    };
  },
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader,
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === "mobile",
      };
    },
  },
  methods: {
    handleClose() {
      this.visible = false;
    },
    handleSavePwd(data) {
      let formData = { ...data };
      formData.user_id = this.$store.getters.user.id;
      updatePassword(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    async message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        // this.$router.push({ path: "/bidding/bid-doc-review", query: {} });
        this.visible = false;
        await this.$store.dispatch("user/logout");
        this.$router.push(`/login?redirect=${this.$route.fullPath}`);
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    handleShowPwd() {
      console.log("teste");

      this.visible = true;
    },
    handleClickOutside() {
      this.$store.dispatch("app/closeSideBar", { withoutAnimation: false });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/mixin.scss";
@import "~@/styles/variables.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
