<template>
  <div>
    <el-dialog
      title="公告详情"
      width="80%"
      v-if="visible"
      :visible.sync="visible"
      :before-close="handleClose"
      :modal="false"
    >
      <div class="content-wrapper">
        <div class="announcement">
          <h2 style="text-align: center">
            {{ fileData.bidding_project.bidding_project_name }}
          </h2>
          <div class="details">
            <p>发布时间：{{ fileData.publish_date }}</p>
            <p>查看次数：{{ fileData.num }}</p>
            <p>
              招标/采购项目编号：{{
                fileData.bidding_project.bidding_project_code
              }}
            </p>
            <!-- <p>所属行业：{{}}</p> -->
            <p>业主单位：{{ fileData.bidding_project.owner_unit }}</p>
            <p>
              招标项目地址：{{
                fileData.bidding_project.bidding_project_location
              }}
            </p>
            <p>
              招标组织形式：{{ fileData.bidding_project.organizational_form }}
            </p>
          </div>
        </div>
        <h3 style="text-align: center">
          {{ fileData.title }}
        </h3>
        <div id="content"></div>
        <div id="file">
          附件:<a
            style="margin-right: 10px"
            v-for="item in fileData.attachments"
            :key="item.name"
            :href="'http://bidding.senmoio.cn/' + item.path"
            >{{ item.name }}</a
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "ProjectForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    fileData: {
      type: Object,
      default: () => {},
    },
  },
  created() {},
  data() {
    return {
      loading: false,
    };
  },
  mounted() {
    this.$nextTick(() => {
      var element = document.createElement("div");
      // 设置innerHTML
      element.innerHTML = this.fileData.content;
      document.getElementById("content").appendChild(element);
    });
  },
  methods: {
    handleClose(done) {
      this.$emit("close");
    },
  },
};
</script>

<style scoped>
#content {
  margin-top: 20px;
  margin-bottom: 20px;
  width: 80%;
}
#file {
  width: 100%;
  background-color: #c6c6c6;
  height: 40px;
  line-height: 40px;
  padding: 00px 20px;
}
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
.status-list {
  display: flex;
}
.status-list > div {
  flex: 33%;
  text-align: center;
  border: 1px solid #f5f7fa;
  border-radius: 5px;
}
.el-button {
  width: 150px;
}
</style>
