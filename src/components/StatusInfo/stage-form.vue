<template>
  <div>
    <el-card class="box-card" style="margin-top: 10px">
      <div slot="header" class="clearfix">
        <span>{{ formData.name }}</span>
        <el-button
          v-if="iVisible"
          title="添加节点"
          style="margin-left: 10px"
          size="mini"
          type="primary"
          icon="el-icon-plus"
          @click="handleAddNode"
        ></el-button>
        <el-button
          v-if="iVisible"
          title="删除阶段"
          size="mini"
          type="danger"
          icon="el-icon-delete"
          @click="handleDelStage(formData.id)"
        ></el-button>
      </div>
      <el-card
        v-for="node in formData.nodes"
        :key="node.name"
        class="box-card"
        style="margin-top: 10px"
      >
        <div slot="header" class="clearfix">
          <span>{{ node.name }}</span>
          <el-button
            v-if="iVisible"
            title="删除节点"
            style="margin-left: 10px"
            size="mini"
            type="danger"
            icon="el-icon-delete"
            @click="handleDelNode(node.id)"
          ></el-button>
        </div>
        <p
          v-for="(item, index) in node.clauses"
          :key="item.name"
          class="text item"
        >
          <el-button type="text" @click="handleViewClauses(node, item.id)">
            {{ `【${index + 1}】` + item.name }}
            <span v-if="node.type == 2 || node.type == 5">{{
              "（0~" + item.score_range + "）"
            }}</span>
          </el-button>

          <i
            v-if="iVisible"
            class="el-icon-edit"
            style="margin-left: 10px; color: #448ef7; cursor: pointer"
            @click="handleView(node, item.id)"
          ></i>
          <i
            v-if="iVisible"
            class="el-icon-delete"
            style="margin-left: 10px; color: red; cursor: pointer"
            @click="handleDel(item.id)"
          ></i>
        </p>
        <div>
          <el-button
            v-if="iVisible"
            size="mini"
            type="primary"
            @click="handleAdd(node)"
            >添加条款</el-button
          >
        </div>
      </el-card>
    </el-card>
    <el-dialog
      title="节点表单"
      :visible.sync="nodeVisible"
      width="40%"
      append-to-body
      :before-close="handleCloseNode"
    >
      <el-form
        ref="nodeForm"
        :model="nodeForm"
        :rules="nodeRules"
        label-width="230px"
        status-icon
        size="mini"
      >
        <el-form-item label="节点名称:" prop="name">
          <el-input v-model="nodeForm.name"></el-input>
        </el-form-item>
        <el-form-item label="节点类型:" prop="type">
          <el-select v-model="nodeForm.type" placeholder="请选择">
            <el-option
              v-for="item in nodeTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="价格得分说明:" v-if="nodeForm.type === 5">
          <p>
            报价公式算法【在所有的有效投标报价中，以最低投标报价为基准价，
            其价格分为满分。其他投标人的报价分统一按下列公式计算:
            投标报价得分=(评标基准价/投标报价)x价格权值(30%)x100(四舍五入后保留小数点后两位)】
          </p>
        </el-form-item>
        <el-form-item label="节点属性:" prop="attributes">
          <el-checkbox-group v-model="nodeForm.attributes">
            <el-checkbox label="商务"></el-checkbox>
            <el-checkbox label="技术"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item
          v-if="nodeForm.type === 1"
          label="否决条件:"
          prop="rejection_condition"
        >
          <span>不满足项数量大于等于</span>
          <el-input-number
            :min="0"
            v-model="nodeForm.rejection_condition"
          ></el-input-number
          ><span>时，否决投标</span>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseNode">取 消</el-button>
        <el-button type="primary" @click="handleSaveNode">保存</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="条款表单"
      :visible.sync="visible"
      width="40%"
      append-to-body
      :before-close="handleClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="230px"
        status-icon
        size="mini"
      >
        <el-form-item label="条款名称:" prop="name">
          <el-input :disabled="isView" v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="条款描述:" prop="description">
          <el-input
            :disabled="isView"
            type="textarea"
            v-model="form.description"
          ></el-input>
        </el-form-item>
        <div v-if="curentNode.type == 2 || curentNode.type == 5">
          <el-form-item label="分值:" prop="score_range">
            <el-input-number
              :disabled="isView"
              :min="1"
              v-model="form.score_range"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="是否扣分项:" prop="is_deduction">
            <el-radio-group :disabled="isView" v-model="form.is_deduction">
              <el-radio :label="0">否</el-radio>
              <el-radio :label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <el-form-item label="是否客观项:" prop="is_objective">
          <el-radio-group :disabled="isView" v-model="form.is_objective">
            <el-radio :label="0">否</el-radio>
            <el-radio :label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{
          isView ? "关 闭" : "取 消"
        }}</el-button>
        <el-button v-if="!isView" type="primary" @click="handleSave"
          >保存</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getClausesById } from "@/api/bid-manage/stage";
export default {
  name: "StageForm",
  components: {},
  props: {
    formData: {
      type: Object,
      default: () => false,
    },
    iVisible: {
      type: Boolean,
      default: () => true,
    },
  },
  watch: {},
  created() {},
  data() {
    return {
      curentNode: {},
      nodeId: null,
      nodeRules: {
        name: [
          {
            required: true,
            message: "请输入节点名称",
            trigger: ["blur", "change"],
          },
        ],
        type: [
          {
            required: true,
            message: "请选择节点类型",
            trigger: ["blur", "change"],
          },
        ],
        attributes: [
          {
            required: true,
            message: "请选择节点属性",
            trigger: ["blur", "change"],
          },
        ],
        rejection_condition: [
          {
            required: true,
            message: "请输入否决条件",
            trigger: ["blur", "change"],
          },
        ],
      },
      rules: {
        name: [
          {
            required: true,
            message: "请输入条款名称",
            trigger: ["blur", "change"],
          },
        ],
        description: [
          {
            required: true,
            message: "请输入条款描述",
            trigger: ["blur", "change"],
          },
        ],
        score_range: [
          {
            required: true,
            message: "请输入分值",
            trigger: ["blur", "change"],
          },
        ],
        is_objective: [
          {
            required: true,
            message: "请选择是否客观项",
            trigger: ["blur", "change"],
          },
        ],
        is_deduction: [
          {
            required: true,
            message: "请选择是否扣分项",
            trigger: ["blur", "change"],
          },
        ],
      },
      loading: false,
      nodeVisible: false,
      visible: false,
      nodeForm: {
        name: "",
        type: null,
        attributes: [],
        rejection_condition: null,
      },
      form: {
        name: "",
        description: "",
        score_range: null,
        is_deduction: null,
        is_objective: null,
      },
      isView: true,
      nodeTypes: [
        {
          value: 1,
          label: "评审项",
        },
        {
          value: 2,
          label: "评分项",
        },
        // {
        //   value: 3,
        //   label: "分段打分",
        // },
        {
          value: 5,
          label: "价格得分",
        },
      ],
    };
  },

  mounted() {},
  methods: {
    handleViewClauses(node, id) {
      this.curentNode = node;
      getClausesById(id).then((res) => {
        this.visible = true;
        this.isView = true;
        this.form = { ...res.data };
      });
    },
    handleView(node, id) {
      this.nodeId = id;
      this.curentNode = node;
      getClausesById(id).then((res) => {
        this.visible = true;
        this.isView = false;
        this.form = { ...res.data };
      });
    },
    handleDel(id) {
      this.$emit("del", id);
    },
    handleAdd(node) {
      this.isView = false;
      this.visible = true;
      this.nodeId = node.id;
      this.curentNode = node;
      this.form = {};
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
    },
    handleClose() {
      this.visible = false;
      this.$refs.form.resetFields();
      this.$refs.form.clearValidate();
    },
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.bid_segment_stage_node_id = this.nodeId;
          this.$emit("save", this.form);
          this.visible = false;
        }
      });
    },
    handleAddNode() {
      this.nodeVisible = true;
      this.$nextTick(() => {
        this.$refs.nodeForm.resetFields();
        this.$refs.nodeForm.clearValidate();
      });
    },
    handleSaveNode() {
      this.$refs.nodeForm.validate((valid) => {
        if (valid) {
          this.nodeForm.bid_segment_stage_id = this.formData.id;
          this.$emit("saveNode", this.nodeForm);
          this.nodeVisible = false;
        }
      });
    },
    handleCloseNode() {
      this.$refs.nodeForm.resetFields();
      this.$refs.nodeForm.clearValidate();
      this.nodeVisible = false;
    },
    handleDelNode(id) {
      this.$emit("delNode", id);
    },
    handleDelStage(id) {
      this.$emit("delStage", id);
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
