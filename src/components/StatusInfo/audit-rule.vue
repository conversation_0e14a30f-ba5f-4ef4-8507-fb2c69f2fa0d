<template>
  <div>
    <el-dialog
      title="评标办法"
      width="90%"
      :visible.sync="visible"
      :modal="false"
      :before-close="handleCloseRule"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="项目信息" name="1">
          <el-form
            ref="form"
            :model="infoForm"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="title">
                  <el-input
                    disabled
                    v-model="infoForm.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="招标项目名称:" prop="bidding_project_name">
                  <el-input
                    disabled
                    v-model="infoForm.bidding_project_name"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="标段编号:" prop="bid_segment_code">
                  <el-input
                    disabled
                    v-model="infoForm.bid_segment_code"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="bid_segment_name">
                  <el-input
                    disabled
                    v-model="infoForm.bid_segment_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="评标条款" name="2">
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-form-item label="评标原则:" prop="bid_evaluation_rules">
              <el-radio-group disabled v-model="ruleForm.bid_evaluation_rules">
                <p style="line-height: 30px">
                  <el-radio :label="1"
                    >1.计算所有评委评分（选项1）（汇总全部评分后，计算评分均值）</el-radio
                  >
                </p>

                <!-- <p style="line-height: 30px">
                  <el-radio :label="2"
                    >2.去掉最低评分（选项2）（去掉N个最高M个最低分后，计算评分均值）</el-radio
                  >
                </p>
                <p style="line-height: 30px">
                  <el-radio :label="3"
                    >3.限定偏离范围（选项3）（分项评分偏离评分均值限定范围的将被剔除，剔除后取有效分值的平均值）</el-radio
                  >
                </p>
                <p style="line-height: 30px">
                  <el-radio :label="4"
                    >4.限定偏离范围（选项4）（分项评分偏离该项总分限定范围的将被剔除，剔除后取有效分值的平均值）</el-radio
                  >
                </p> -->
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div>
            <stage-form
              v-for="item in stageList"
              :key="item.id"
              :formData="item"
              :iVisible="false"
            ></stage-form>
          </div>
        </el-collapse-item>
      </el-collapse>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseRule">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import StageForm from "./stage-form.vue";
export default {
  name: "AuditRule",
  components: {
    StageForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    infoForm: {
      type: Object,
      default: () => {},
    },
    stageList: {
      type: Array,
      default: () => [],
    },
    ruleForm: {
      type: Object,
      default: () => {},
    },
  },
  created() {},
  data() {
    return {
      loading: false,
      collapse: ["1", "2"],
    };
  },
  mounted() {},
  methods: {
    handleCloseRule(done) {
      this.$emit("close");
    },
  },
};
</script>

<style scoped></style>
