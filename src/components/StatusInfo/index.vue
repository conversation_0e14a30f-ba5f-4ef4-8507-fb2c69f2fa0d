<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :before-close="handleClose"
      :modal="false"
    >
      <el-steps
        :active="infoForm.flow_status"
        finish-status="success"
        simple
        style="margin-top: 20px"
      >
        <el-step title="发标"></el-step>
        <el-step title="投标"></el-step>
        <el-step title="开标"></el-step>
        <el-step title="评标"></el-step>
        <el-step title="定标"></el-step>
      </el-steps>
      <div class="status-list">
        <div>
          <p>
            <el-button size="mini" type="primary" @click="handleFileManage()"
              >招标文件</el-button
            >
          </p>
          <p>
            <el-popover placement="right" title="" width="260" trigger="hover">
              <div>
                <p>开始时间：{{ fileData.document_obtain_start }}</p>
                <p>
                  已下载:<span style="font-size: 18px; padding: 0 10px">{{
                    fileData.down_count
                  }}</span>
                </p>
                <p>结束时间：{{ fileData.document_obtain_deadline }}</p>
              </div>
              <el-button slot="reference" size="mini" type="primary"
                >招标文件下载</el-button
              >
            </el-popover>
          </p>
          <p>
            <el-button size="mini" type="primary" @click="handleSetOpen"
              >开标一览表</el-button
            >
          </p>
          <p>
            <el-button size="mini" type="primary" @click="handleShowRule"
              >评标办法</el-button
            >
          </p>
          <p>
            <el-button size="mini" type="primary" @click="handleShowFile"
              >招标文件澄清</el-button
            >
          </p>
        </div>
        <div>
          <p>
            <el-popover placement="right" title="" width="280" trigger="hover">
              <div>
                <p>开始时间：{{ fileData.document_obtain_start }}</p>
                <p style="display: flex">
                  已递交<span style="font-size: 18px; padding: 0 5px"
                    >{{ fileData.submit_count }}/3</span
                  >
                  <span>需递交</span>
                  <el-button v-if="false" type="text" @click="handleShowList"
                    >查看详细</el-button
                  >
                </p>

                <p>结束时间：{{ fileData.document_submission_deadline }}</p>
              </div>
              <el-button slot="reference" size="mini" type="primary"
                >投标文件递交</el-button
              >
            </el-popover>
          </p>
        </div>
        <div>
          <p>
            <el-button size="mini" type="primary" @click="handleOpenHall"
              >开标大厅</el-button
            >
          </p>
        </div>
        <div>
          <p>
            <el-button size="mini" type="primary" @click="handleViewReport()"
              >评标管理</el-button
            >
          </p>
          <p>
            <el-button size="mini" type="primary" @click="handleView(5)"
              >废标通知</el-button
            >
          </p>
        </div>
        <div>
          <p>
            <el-button size="mini" type="primary" @click="handleView(3)"
              >中标候选人公示</el-button
            >
          </p>
          <p>
            <el-button size="mini" type="primary" @click="handleView(4)"
              >中标结果公告</el-button
            >
          </p>
        </div>
      </div>
      <add-form
        v-if="addShow"
        :visible="addShow"
        :editForm="editForm"
        :type="type"
        :isView="isView"
        :fileList="fileList"
        @save="handleSave"
        @submit="handleSubmit"
        @close="addShow = false"
      ></add-form>
      <view-form
        v-if="viewShow"
        :visible="viewShow"
        :editForm="viewForm"
        :fileList="fileList"
        @save="handleSaveHalf"
        @submit="handleSubmitHalf"
        @close="viewShow = false"
      ></view-form>
    </el-dialog>
    <el-dialog
      title="评标管理"
      width="80%"
      :visible.sync="reportShow"
      :modal="false"
    >
      <report-form
        v-if="reportShow"
        :fileData="reportForm"
        :bid_segment_id="infoForm.id"
        @show="handleShowRule"
        @back="reportShow = false"
      ></report-form>
    </el-dialog>
    <status-view
      v-if="statusView"
      :visible="statusView"
      :fileData="viewForm"
      @close="statusView = false"
    ></status-view>
    <el-dialog
      title="评标办法"
      width="90%"
      :visible.sync="bidRule"
      :modal="false"
      :before-close="handleCloseRule"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="项目信息" name="1">
          <el-form
            ref="form"
            :model="infoForm"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="title">
                  <el-input
                    disabled
                    v-model="infoForm.bidding_project.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="招标项目名称:" prop="bidding_project_name">
                  <el-input
                    disabled
                    v-model="infoForm.bidding_project.bidding_project_name"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="标段编号:" prop="title">
                  <el-input
                    disabled
                    v-model="infoForm.segment_number"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="segment_name">
                  <el-input
                    disabled
                    v-model="infoForm.segment_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="评标条款" name="2">
          <el-form
            ref="ruleForm"
            :model="ruleForm"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-form-item label="评标原则:" prop="bid_evaluation_rules">
              <el-radio-group v-model="ruleForm.bid_evaluation_rules">
                <p style="line-height: 30px">
                  <el-radio :label="1"
                    >1.计算所有评委评分（选项1）（汇总全部评分后，计算评分均值）</el-radio
                  >
                </p>

                <!-- <p style="line-height: 30px">
                  <el-radio :label="2"
                    >2.去掉最低评分（选项2）（去掉N个最高M个最低分后，计算评分均值）</el-radio
                  >
                </p>
                <p style="line-height: 30px">
                  <el-radio :label="3"
                    >3.限定偏离范围（选项3）（分项评分偏离评分均值限定范围的将被剔除，剔除后取有效分值的平均值）</el-radio
                  >
                </p>
                <p style="line-height: 30px">
                  <el-radio :label="4"
                    >4.限定偏离范围（选项4）（分项评分偏离该项总分限定范围的将被剔除，剔除后取有效分值的平均值）</el-radio
                  >
                </p> -->
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div>
            <stage-form
              v-for="item in stageList"
              :key="item.id"
              :formData="item"
              :iVisible="stateVisible"
              @saveNode="handleSaveNode"
              @save="handleSaveClauses"
              @delNode="handleDelNode"
              @delStage="handleDelStage"
              @del="handleDelClauses"
            ></stage-form>
          </div>
          <el-button
            v-if="stateVisible"
            type="primary"
            plain
            style="width: 100%; margin-top: 5px"
            @click="handleAddStage"
            >添加阶段</el-button
          >
        </el-collapse-item>
      </el-collapse>

      <span slot="footer" class="dialog-footer" v-if="!this.isAdmin()">
        <el-button @click="handleCloseRule">{{
          this.ruleForm.stage_rule_state === 2 ? "关 闭" : "取 消"
        }}</el-button>
        <el-button
          v-if="this.ruleForm.stage_rule_state !== 2"
          type="primary"
          @click="handleSaveRule(1)"
          >保存</el-button
        >
        <el-button
          v-if="this.ruleForm.stage_rule_state !== 2"
          type="primary"
          @click="handleSaveRule(2)"
          >完成</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="阶段表单"
      :visible.sync="stageVisible"
      width="40%"
      append-to-body
      :before-close="handleCloseStage"
    >
      <el-form
        ref="stageForm"
        :model="stageForm"
        :rules="stageRules"
        label-width="230px"
        status-icon
        size="mini"
      >
        <el-form-item label="阶段名称:" prop="name">
          <div style="display: flex">
            <el-input v-model="stageForm.name"></el-input>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="stageVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSaveStage">保存</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="招标文件澄清"
      :visible.sync="fileVisible"
      width="80%"
      append-to-body
      :before-close="handleCloseFile"
    >
      <file-form
        :segmentForm="segmentForm"
        :init="isInit"
        @back="handleBack"
      ></file-form>
    </el-dialog>
    <el-dialog
      title="开标一览表"
      :visible.sync="openVisible"
      width="40%"
      :modal="false"
      append-to-body
      :before-close="handleCloseOpen"
    >
      <el-form :model="openForm" ref="openForm" label-width="100px">
        <el-form-item
          v-for="(name, index) in openForm.field_names"
          :label="'字段' + (index + 1)"
          :key="index"
          :prop="'field_names.' + index"
          :rules="{
            required: true,
            message: '字段名称不能为空',
            trigger: 'blur',
          }"
        >
          <div style="display: flex">
            <el-input
              v-model="openForm.field_names[index]"
              :disabled="index === 0"
            ></el-input
            ><el-button
              v-if="index !== 0"
              @click.prevent="removeFieldName(index)"
              type="danger"
              >删除</el-button
            >
          </div>
        </el-form-item>
        <el-form-item>
          <el-button @click="addFieldName">新增字段</el-button>
          <el-button type="primary" @click="handleSubmitFieldNames()"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog
      title="递交详情"
      :visible.sync="companyListShow"
      width="40%"
      append-to-body
    >
      <el-table
        :data="fileData.registration_company"
        border=""
        style="width: 100%"
      >
        <el-table-column prop="company_name" label="公司名称" width="180">
        </el-table-column>
        <el-table-column prop="user_name" label="联系人" width="180">
        </el-table-column>
        <el-table-column prop="phone" label="电话"> </el-table-column>
      </el-table>
    </el-dialog>
    <!-- <view-info :fileShow="fileShow"></view-info> -->
  </div>
</template>

<script>
import {
  getBidHalfDetailBySegmentId,
  addBidHalfAnnouncements,
  updateBidHalfAnnouncements,
  getBidRule,
  updateBidRule,
  getOpenFieldNames,
  setOpenFieldNames,
} from "@/api/bid-manage/bid-project";
import {
  addBidDocumnet,
  getBidFileDetail,
  detailBidDocumnet,
  updateBidDocumnet,
} from "@/api/bid-manage/bid-doc";
import {
  getStageList,
  addStage,
  addNode,
  delNode,
  delStage,
  addClauses,
  delClauses,
  updateClauses,
} from "@/api/bid-manage/stage";
import { getExpertAuditReport } from "@/api/home/<USER>";
import { getToken } from "@/utils/auth";
import AddForm from "@/views/bidding/bid-doc-review/modules/add-form.vue";
import ViewForm from "@/views/bidding/bid-half-announcement/modules/add-form.vue";
import ReportForm from "@/views/home/<USER>";
import StatusView from "./status-view.vue";
import StageForm from "./stage-form.vue";
import FileForm from "@/views/tender/registrat-invitat/modules/file-form.vue";

import store from "@/store";
export default {
  name: "ProjectForm",
  components: {
    AddForm,
    ViewForm,
    ReportForm,
    StatusView,
    StageForm,
    FileForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    infoForm: {
      type: Object,
      default: () => {},
    },
    // isView: {
    //   type: Boolean,
    //   default: () => false,
    // },
  },
  created() {},
  data() {
    return {
      openVisible: false,
      fileVisible: false,
      stateVisible: false,
      openForm: {
        field_names: ["投标总价（￥ 元）"],
        bid_segment_id: "",
      },
      segmentForm: {},
      stageForm: {
        name: "",
      },
      stageVisible: false,
      stageList: [
        { id: 1, name: "阶段1" },
        { id: 2, name: "阶段2" },
      ],
      collapse: ["1", "2"],
      header: {
        Authorization: "Bearer " + getToken(),
      },
      ruleFileList: [],
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      ruleForm: {
        bid_evaluation_rules: 1,
      },
      bidRule: false,
      statusView: false,
      isView: false,
      fileShow: false,
      viewShow: false,
      title: "",
      reportShow: false,
      fileData: {},
      formData: {},
      editForm: {},
      viewForm: {},
      reportForm: {},
      active: 0,
      segment_id: "",
      addShow: false,
      viewShow: false,
      loading: false,
      type: "",
      isInit: false,
      stageRules: {
        name: [
          {
            required: true,
            message: "请输入阶段名称",
            trigger: ["blur", "change"],
          },
        ],
      },
      companyListShow: false,
    };
  },
  mounted() {
    this.title = `标段编号：${this.infoForm.segment_number} 标段名称：${this.infoForm.segment_name}`;
    this.handleBidDownload();
  },
  methods: {
    handleShowList() {
      this.companyListShow = true;
    },
    addFieldName() {
      this.openForm.field_names.push("");
    },
    removeFieldName(index) {
      if (this.openForm.field_names.length === 1) {
        this.$message({
          message: "请至少保留一个字段！",
          type: "warning",
        });
        return;
      }
      this.openForm.field_names.splice(index, 1);
    },
    handleCloseOpen() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.openVisible = false;
          //   done();
        })
        .catch((_) => {});
    },
    handleSetOpen() {
      this.openVisible = true;
      getOpenFieldNames(this.infoForm.id).then((res) => {
        this.openForm = res.data;
        if (this.openForm.field_names.length === 0) {
          this.openForm.field_names.push("投标总价（￥ 元）");
        }
        console.log(res);
      });
    },
    handleSubmitFieldNames() {
      this.$refs.openForm.validate((valid) => {
        if (valid) {
          this.openForm.bid_segment_id = this.infoForm.id;
          setOpenFieldNames(this.openForm).then((res) => {
            let message = res.message;
            if (res.status_code == 200) {
              this.$message({
                message,
                type: "success",
              });
              this.$refs.openForm.resetFields();
              this.openVisible = false;
            } else {
              this.$message({
                message,
                type: "error",
              });
            }
          });
        }
      });
    },
    handleBack() {
      this.fileVisible = false;
    },
    handleShowFile() {
      this.isInit = false;
      this.fileVisible = true;
      this.isInit = true;
      this.segmentForm = { ...this.infoForm };
    },
    getStageList() {
      let params = {
        bid_segment_id: this.infoForm.id,
      };
      getStageList(params).then((res) => {
        this.stageList = res.data;
      });
    },
    handleAddStage() {
      this.stageVisible = true;
      this.$refs.stageForm.resetFields();
    },
    handleSaveStage() {
      this.$refs.stageForm.validate((valid) => {
        if (valid) {
          this.stageForm.bid_segment_id = this.infoForm.id;
          addStage(this.stageForm).then((res) => {
            let message = res.message;
            if (res.status_code == 200) {
              this.$message({
                message,
                type: "success",
              });
              this.$refs.stageForm.resetFields();
              this.getStageList();
              // this.$router.push({ path: "/bidding/bid-doc-review", query: {} });
              this.stageVisible = false;
            } else {
              this.$message({
                message,
                type: "error",
              });
            }
          });
        }
      });
    },
    handleSaveClauses(data) {
      if (data.id) {
        updateClauses(data.id, data).then((res) => {
          let message = res.message;
          if (res.status_code == 200) {
            this.$message({
              message,
              type: "success",
            });
            this.getStageList();
          } else {
            this.$message({
              message,
              type: "error",
            });
          }
        });
      } else {
        addClauses(data).then((res) => {
          let message = res.message;
          if (res.status_code == 200) {
            this.$message({
              message,
              type: "success",
            });
            this.getStageList();
          } else {
            this.$message({
              message,
              type: "error",
            });
          }
        });
      }
    },
    handleSaveNode(data) {
      addNode(data).then((res) => {
        let message = res.message;
        if (res.status_code == 200) {
          this.$message({
            message,
            type: "success",
          });
          this.getStageList();
        } else {
          this.$message({
            message,
            type: "error",
          });
        }
      });
    },

    handleDelClauses(id) {
      delClauses(id).then((res) => {
        let message = res.message;
        if (res.status_code == 200) {
          this.$message({
            message,
            type: "success",
          });
          this.getStageList();
        } else {
          this.$message({
            message,
            type: "error",
          });
        }
      });
    },
    handleDelNode(id) {
      delNode(id).then((res) => {
        let message = res.message;
        if (res.status_code == 200) {
          this.$message({
            message,
            type: "success",
          });
          this.getStageList();
        } else {
          this.$message({
            message,
            type: "error",
          });
        }
      });
    },
    handleDelStage(id) {
      delStage(id).then((res) => {
        let message = res.message;
        if (res.status_code == 200) {
          this.$message({
            message,
            type: "success",
          });
          this.getStageList();
        } else {
          this.$message({
            message,
            type: "error",
          });
        }
      });
    },
    handleShowRule() {
      this.bidRule = true;
      this.getStageList();
      getBidRule(this.infoForm.id).then((res) => {
        this.ruleForm = { ...res.data };
        if (this.ruleForm.stage_rule_state === 2 || this.isAdmin()) {
          this.stateVisible = false;
        } else {
          this.stateVisible = true;
        }
        if (this.ruleForm.bid_clause_attachment == null) {
          this.ruleForm.bid_clause_attachment = [];
        } else {
          this.ruleFileList = [];
          this.ruleForm.bid_clause_attachment.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.ruleFileList.push(obj);
          });
        }
        console.log(res);
      });
    },
    handleCloseRule() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.bidRule = false;
          //   done();
        })
        .catch((_) => {});
    },
    handleCloseFile() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.fileVisible = false;
          //   done();
        })
        .catch((_) => {});
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.ruleForm.bid_clause_attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.ruleForm.bid_clause_attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.ruleForm.bid_clause_attachment.splice(index, 1);
      }
    },
    handleBidDownload() {
      getBidFileDetail(this.infoForm.id).then((res) => {
        this.fileData = { ...res.data };
      });
    },
    handleOpenHall() {
      this.$router.push("/hall/invite");
    },
    async handleView(val) {
      let params = {
        id: this.infoForm.id,
        status: val,
      };

      await getBidHalfDetailBySegmentId(params).then((res) => {
        if (this.isAdmin()) {
          if (res.data.length == 0) {
            this.$message.warning("暂无数据");
            return;
          } else {
            this.statusView = true;
            this.viewForm = res.data;
            this.fileList = [];
            if (this.viewForm.attachments != null) {
              this.viewForm.attachments.forEach((item) => {
                let obj = {
                  name: item.name,
                  url: item.path,
                };
                this.fileList.push(obj);
              });
            }
          }
        } else {
          if (res.data.length == 0) {
            this.$router.push("/bidding/bid-half-announcement");
          } else {
            this.viewShow = true;
            this.viewForm = res.data;
            console.log(this.viewForm);

            this.fileList = [];
            if (this.viewForm.attachments != null) {
              this.viewForm.attachments.forEach((item) => {
                let obj = {
                  name: item.name,
                  url: item.path,
                };
                this.fileList.push(obj);
              });
            }
          }
        }
      });
    },
    isAdmin() {
      let user = store.getters.user;
      let index = user.roles.indexOf(2);
      if (index == -1) {
        return false;
      } else {
        return true;
      }
    },
    handleViewReport() {
      this.reportForm = {};
      getExpertAuditReport(this.infoForm.id).then((res) => {
        if (this.reportForm.length == 0) {
          this.$message.warning("暂无数据");
          this.reportShow = false;
        } else {
          this.reportShow = true;
        }
        this.$nextTick(() => {
          this.reportForm = res.data;
        });

        console.log(this.reportForm);
      });
    },
    handleFileManage() {
      console.log(this.infoForm);
      if (this.isAdmin()) {
        if (this.infoForm.bid_document.length == 0) {
          this.$message.warning("暂无数据");
          return;
        } else {
          this.type = "superDetail";
        }
      } else {
        if (this.infoForm.bid_document.length == 0) {
          this.type = "add";
          this.addShow = true;
          this.segment_id = this.infoForm.id;
          this.editForm = this.infoForm;
          return;
        } else {
          this.type = "edit";
          this.segment_id = this.infoForm.id;
          this.editForm = this.infoForm;
        }
      }
      let id = this.infoForm.bid_document[0].id;
      console.log(this.infoForm);
      this.addShow = true;
      detailBidDocumnet(id).then((res) => {
        this.editForm = res.data;
        this.editForm.bidding_project = this.infoForm.bidding_project;
        this.editForm.segment_name = this.infoForm.segment_name;
        console.log(this.editForm, "this.editForm");
        this.fileList = [];
        if (this.editForm.attachments != null) {
          this.editForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
        if (this.editForm.status == 1 || this.editForm.status == 2) {
          this.isView = true;
        }
      });
      if (this.isAdmin()) {
        this.isView = true;
      }
    },
    handleSaveRule(status) {
      this.ruleForm.bid_segment_id = this.infoForm.id;
      this.ruleForm.stage_rule_state = status;
      updateBidRule(this.ruleForm).then((res) => {
        this.message(res.status_code, res.message);
        this.bidRule = false;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.bid_segment_id = this.segment_id;
      formData.status = 0;
      if (data.id) {
        delete this.formData.approvalLog;
        updateBidDocumnet(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidDocumnet(formData).then((res) => {
          this.message(res.status_code, res.message);
          this.$emit("init");
        });
      }
    },

    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      formData.bid_segment_id = this.segment_id;
      if (data.id) {
        delete this.formData.approvalLog;
        updateBidDocumnet(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidDocumnet(formData).then((res) => {
          this.message(res.status_code, res.message);
          this.$emit("init");
        });
      }
    },
    // handleSave(data) {
    //   let formData = { ...data };
    //   formData.status = 0;
    //   formData.bid_segment_id = this.segment_id;
    //   addBidDocumnet(formData).then((res) => {
    //     this.type = "edit";
    //     this.message(res.status_code, res.message);
    //   });
    // },
    // handleSubmit(data) {
    //   let formData = { ...data };
    //   formData.status = 1;
    //   formData.bid_segment_id = this.segment_id;
    //   addBidDocumnet(formData).then((res) => {
    //     this.type = "edit";
    //     this.message(res.status_code, res.message);
    //   });
    // },
    handleSaveHalf(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        updateBidHalfAnnouncements(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidHalfAnnouncements(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmitHalf(data) {
      let formData = { ...data };
      formData.status = 1;
      if (data.id) {
        updateBidHalfAnnouncements(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidHalfAnnouncements(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleCloseStage(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.stageVisible = false;
          //   done();
        })
        .catch((_) => {});
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        // this.$router.push({ path: "/bidding/bid-doc-review", query: {} });
        this.addShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
.status-list {
  display: flex;
}
.status-list > div {
  flex: 20%;
  text-align: center;
}
p {
  line-height: 40px;
}

.el-button {
  width: 108px;
}
</style>
