.Container {
  overflow: inherit !important;
  height: auto !important;
}

/* 涓昏酱 */
.main-new {
  width: 1200px;
  display: block;
  margin: 0 auto;
}
p {
  margin: 0;
  padding: 0;
}
/* 涓昏酱 */
.main {
  /* width: 1200px; */
  display: block;
  margin: 0 auto;
}
/* 椤甸潰搴曢儴鏍峰紡 */
.footer-new {
  position: relative;
  width: 100%;
  min-width: 1200px;
  height: 425px;
  /* background-image: url("../images/footer-bg.jpg"); */
  background-size: auto 100%;
  background-position: center center;
  background-repeat: no-repeat;
  padding-top: 15px;
}
.footer-title-new {
  color: #fff;
  font-size: 24px;
  width: 100%;
  text-align: center;
}
.footer-bottom-new {
  position: relative;
  text-align: center;
  font-size: 14px;
  color: #e4f4fb;
  font-weight: 400;
}
.footer-bottom-new p:first-of-type {
  margin-bottom: 7px;
}
.footer-inner-new {
  position: relative;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 20px;
  width: 100%;
}
.footer-inner-new ul {
  font-weight: 400;
  display: inline-block;
  vertical-align: top;
}
.footer-inner-new ul {
  line-height: 36px;
}
.footer-inner-new ul li:first-of-type {
  font-size: 18px;
  color: #fff;
}
.footer-inner-new a {
  font-size: 14px;
  color: #d9efff;
  display: block;
}
.footer-inner-new a:hover {
  color: #ff6f06;
}
.footer-line {
  height: 1px;
  width: 100vw;
  position: absolute;
  bottom: 75px;
  left: 0;
  background-color: #fff;
  opacity: 0.1;
}
/* 瀵艰埅鏍� */
.nav {
  position: fixed;
  width: 100%;
  background-color: transparent;
  height: 70px;
  z-index: 100;
  /* display: flex; */
}
.nav-logo {
  position: absolute;
  left: 54px;
  top: 50%;
  transform: translateY(-50%);
  height: 40px;
}
.nav-btns {
  height: 100%;
  position: absolute;
  right: 0;
  top: 50%;
  font-size: 0;
  transform: translateY(-50%);
  line-height: 70px;
  text-align: center;
}
.nav-btns .login-btn {
  font-size: 18px;
  height: 100%;
  display: inline-block;
  width: 100px;
  color: #666;
  cursor: pointer;
}
.nav-btns .login-btn:hover {
  color: #ff6f06;
}
.nav-btns .register-btn {
  font-size: 18px;
  color: #fff;
  display: inline-block;
  height: 100%;
  width: 100px;
  background-color: #ff6f06;
  cursor: pointer;
}
.nav-menu {
  position: absolute;
  left: 500px;
  top: 0;
  height: 100%;
  line-height: 70px;
  /* margin: 0 auto; */
  white-space: nowrap;
}
.nav-menu a,
.nav-ca {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  color: #666;
  transition: all 0.3s;
  font-size: 14px;
  cursor: pointer;
}
.nav-menu a:not(:first-of-type),
.nav-ca {
  margin-left: 40px;
}
.nav-active,
.nav-menu a:hover,
.nav-ca:hover {
  position: relative;
  color: #ff6f06 !important;
}
.nav-active::after,
.nav-menu a:hover::after,
.nav-ca:hover::after {
  content: "";
  height: 4px;
  position: absolute;
  bottom: 0;
  background-color: #ff6f06;
  width: 100%;
  left: 0;
}
.nav-menu-list a:hover::after {
  display: none;
}
.nav-ca:hover .nav-menu-list {
  display: block;
}
.nav-menu-list {
  display: none;
  bottom: 0;
  transform: translateY(100%);
  left: 0;
  position: absolute;
  width: 166px;
  padding-top: 5px;
  padding-bottom: 5px;
  background-color: #fff;
}
.nav-menu-list li {
  color: #666;
  display: block;
  height: 40px;
  line-height: 40px;
  padding-left: 20px;
}
.org-nav {
  background-color: #ff6f06 !important;
}
.org-nav .login-btn {
  color: #fff !important;
}
.org-nav .login-btn:hover {
  color: #fff !important;
}
.org-nav .register-btn {
  background-color: #fff !important;
  color: #ff6f06 !important;
}
.org-nav .nav-menu a,
.org-nav .nav-ca {
  color: #fff !important;
}
.org-nav .nav-active,
.org-nav .nav-menu a:hover {
  color: #fff !important;
}
.org-nav .nav-active::after,
.org-nav .nav-menu a:hover::after,
.org-nav .nav-ca:hover::after {
  background-color: #fff !important;
}
.org-nav .nav-menu-list {
  background-color: #ff6f06 !important;
}

/* 登录 */
.sx-login {
  width: 400px;
  height: 360px;
  background-color: rgba(255, 255, 255, 0.85);
  position: absolute;
  top: 138px;
  right: 90px;
  padding: 27px 26px;
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  align-items: center;
  z-index: 88;
  border-radius: 5px;
}
.sx-login-box {
  width: 350px;
  height: 70px;
  display: flex;
  align-items: center;
}
.sx-login-box:hover {
  cursor: pointer;
  opacity: 0.8;
}
.sx-login-box-img-box {
  width: 38px;
  height: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  margin-left: 20px;
}
.sx-login-box-text {
  font-size: 20px;
  color: #fff;
  margin-left: 10px;
}
.login-box1 {
  background-color: #5793df;
  border-radius: 10px;
}
.login-box2 {
  background-color: #a2ef4d;
  border-radius: 10px;
}
.login-box3 {
  background-color: #dea054;
  border-radius: 10px;
}
.login-box4 {
  background-color: #eb7930;
  border-radius: 10px;
}

.login-box5 {
  background-color: #70babc;
  border-radius: 10px;
}
.sx-login-box-img1 {
  width: 37px;
  height: 32px;
}
.sx-login-box-img2 {
  width: 34px;
  height: 33px;
}
.sx-login-box-img3 {
  width: 36px;
  height: 28px;
}
.sx-login-box-img4 {
  width: 33px;
  height: 34px;
}
.sx-login-box-img5 {
  width: 24px;
  height: 32px;
}

/* 涓棿閮ㄥ垎鏍峰紡 */
.sx-index {
  width: 100%;
  height: 100%;
  position: relative;
}
.sx-con {
  display: grid;
  width: 100%;
  margin: 0 auto;
}
.sx-title {
  margin-top: 40px;
  text-align: center;
  font-size: 30px;
  font-weight: 400;
  color: #333;
}
.sx-title .sx-title-icon {
  margin: -8px 0 0 518px;
  width: 93px;
  height: 12px;
  background: linear-gradient(to right, #ffb37c 0%, #fff5ed 80%);
}
.sx-select {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
.sx-select .layui-input {
  width: 260px;
  height: 30px;
}
.sx-select .sx-search {
  cursor: pointer;
  width: 30px;
  height: 30px;
  border-radius: 0px 4px 4px 0;
  /* background: #ff6f06 url("../images/search.png") center center no-repeat; */
  background-size: 17px 16px;
}
.sx-content {
  margin: 20px 0px;
  width: 100%;
  display: flex;
  /* flex-wrap: wrap; */
}
.sx-contents {
  margin-top: 60px;
}
.sx-contentes {
  margin-top: 60px;
}
.sx-con-box {
  margin-top: 60px;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.sx-con-box-img {
  width: 380px;
  height: 226px;
  margin-right: 20px;
  position: relative;
}
.sx-con-box-img img {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.sx-con-box-img p {
  position: absolute;
  width: 100%;
  height: 55px;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  text-align: center;
  font-size: 18px;
  font-weight: 400;
  color: #fff;
  line-height: 55px;
}
.sx-contentes .sx-content-right-meu-top {
  width: 926px;
}
.sx-contentes .sx-content-right-meu {
  margin-left: 46px;
}
.sx-contents .sx-content-right-meu-title {
  width: 996px;
}
.sx-content-top {
  width: 100%;
  height: 43px;
  background: linear-gradient(#ffe0c9 0%, #fff6ef 100%);
}
.sx-content-title {
  margin: 8px 0 0 202px;
  height: 33px;
  display: flex;
}
.sx-content-title-meu {
  margin-left: -20px;
  width: 83px;
  height: 33px;
  text-align: center;
  font-size: 15px;
  font-weight: 400;
  color: #666;
  line-height: 33px;
  cursor: pointer;
}
.sx-content-title-meuactive {
  /* background: url("../images/baiban.png") no-repeat; */
  background-size: 100% 100%;
  color: #ff6f06;
}
.sx-content-left {
  box-sizing: border-box;
  width: 150px;
  /* border-right: 2px solid #ff6f06; */
  margin-top: 2px;
}
.sx-content-left-meu {
  cursor: pointer;
  box-sizing: border-box;
  background-color: #f2f2f2;
  padding: 0 2px 0 0;
  margin-top: 2px;
  /* border-right: 2px solid #ff6f06; */
  display: flex;
  align-content: center;
  align-items: center;
  flex-wrap: wrap;
  line-height: 25px;
  width: 150px;
  height: 85px;
  font-size: 20px;
  font-weight: 400;
  color: #666;
  text-align: center;
}
.sx-content-left-meu:nth-child(1) {
  margin-top: 0;
}
.sx-content-left-meuactive {
  /* background: #fff url("../images/board.png") no-repeat; */
  background-size: 100% 100%;
  color: #fff;
  border-right: none;
  padding-right: 0;
  background: #7fcb80;
}
.sx-content-left-meu p {
  width: 100%;
}
.sx-content-right {
  /* width: 1080px; */
  /* height: 1350px; */
  overflow: hidden;
  flex: 1;
  /*overflow-y: auto;*/
  /*overflow-x: hidden;*/
}
.sx-content-right-meu {
  cursor: pointer;
  margin-top: 2px;
  height: 60px;
  display: flex;
}

.sx-content-right-meu-policy {
  cursor: pointer;
  margin-top: 2px;
  height: 80px;
  display: flex;
  margin-left: 30px;
}

.sx-content-right-meu:hover .sx-content-right-meu-title,
.sx-content-right-meu:hover .sx-content-right-meu-top-time,
.sx-content-right-meu:hover .sx-content-right-meu-top-times {
  color: #1890ff;
}
.sx-content-right-meu:hover .sx-content-right-meu-name {
  color: #1890ff;
}
.sx-content-right-meu:hover .sx-content-right-meu-name > div:nth-child(1) {
  /* background: url("../images/companyactive.png") no-repeat; */
  background-size: 100% 100%;
}
.sx-content-right-meu-icon {
  margin: 20px 3px 0 26px;
  width: 11px;
  height: 11px;
  background-size: 100% 100%;
}

/*.sx-content-right-meu-img{
    !*margin-right: 10px;*!
    width: 40px;
    height: 40px;
    background-size: 100% 100%;
    margin-left: 20px;
}*/

.sx-content-right-meu-con {
  width: 98%;
  border-bottom: 1px dashed #e5e5e5;
  display: flex;
  /* line-height: 33px; */
  align-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  box-sizing: border-box;
  margin-left: 10px;
}

.sx-content-right-meu-con-policy {
  width: 996px;
  /*border-bottom: 1px dashed #e5e5e5;*/
  display: flex;
  /* line-height: 33px; */
  align-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
  height: 80px;
  box-sizing: border-box;
}

.sx-content-right-meu-img {
  width: 50px;
  height: 50px;
  margin: 0 20px 0 0;
}
.sx-content-right-meu-img img {
  width: 100%;
  height: 100%;
}
.sx-content-right-meu-top {
  line-height: 25px;
  margin-top: 16px;
  /*line-height: 16px;*/
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.sx-content-right-meu-title {
  font-size: 19px;
  font-weight: 400;
  color: #333;
  width: 890px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.sx-content-right-meu-top-time {
  font-size: 19px;
  font-weight: 400;
  color: #999;
}
.sx-content-right-meu-top-times {
  margin-top: 19px;
  height: 12px;
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #999;
}
.sx-content-right-meu-name {
  margin-top: 19px;
  height: 12px;
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  display: flex;
  align-items: center;
}
.sx-content-right-meu-name > div:nth-child(1) {
  width: 14px;
  height: 14px;
  margin-right: 5px;
  background: url("../images/company.png") no-repeat;
  background-size: 100% 100%;
}

.sx-blfw {
  margin-top: 1px;
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.sx-blfw-box {
  width: 220px;
  height: 190px;
  box-shadow: 0px 5px 5px 0 rgba(101, 101, 101, 0.1);
  border-radius: 10px;
  cursor: pointer;
}
.sx-blfw-box:hover {
  box-shadow: 0px 30px 30px 0 rgba(101, 101, 101, 0.2);
}
.sx-blfw-box:hover p:nth-of-type(1) {
  font-weight: bold;
  color: #1890ff;
}
.sx-blfw-box:hover p:nth-of-type(2) {
  font-weight: bold;
  color: #1890ff;
}
.sx-blfw-box img {
  width: 70px;
  height: 62px;
  margin: 27px 74px 0;
}
.sx-blfw-box p:nth-of-type(1) {
  width: 100%;
  text-align: center;
  margin-top: 20px;
  height: 18px;
  font-weight: 400;
  color: #333;
  font-size: 18px;
}
.sx-blfw-box p:nth-of-type(2) {
  width: 100%;
  text-align: center;
  margin-top: 15px;
  height: 14px;
  font-weight: 400;
  color: #666;
  font-size: 14px;
}
.sx-zczl-title1 {
  width: 100%;
  text-align: center;
  height: 31px;
  font-size: 30px;
  font-weight: 400;
  color: #ff6f06;
  margin-top: 35px;
}
.sx-zczl-title2 {
  width: 100%;
  text-align: center;
  height: 16px;
  font-size: 16px;
  font-weight: 400;
  color: #666;
  margin-top: 20px;
}
.sx-zczl {
  margin-top: 40px;
  margin-left: -20px;
  display: flex;
  justify-content: space-around;
  width: 100%;
}
.sx-zczl-box {
  width: 160px;
  height: 200px;
  /* background: url("../images/zczl1.png") no-repeat; */
  background-size: 100% 100%;
}
.sx-zczl-box:nth-child(2) {
  /* background: url("../images/zczl2.png") no-repeat; */
  background-size: 100% 100%;
}
.sx-zczl-box:nth-child(3) {
  /* background: url("../images/zczl3.png") no-repeat; */
  background-size: 100% 100%;
}
.sx-zczl-box:nth-child(4) {
  /* background: url("../images/zczl4.png") no-repeat; */
  background-size: 100% 100%;
}
.sx-zczl-box:nth-child(5) {
  /* background: url("../images/zczl5.png") no-repeat; */
  background-size: 100% 100%;
}
.sx-zczl-box p:nth-child(1) {
  width: 100%;
  font-size: 34px;
  font-weight: 400;
  color: #ff6f06;
  margin-top: 110px;
  text-align: center;
}
.sx-zczl-box p:nth-child(2) {
  width: 100%;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  margin-top: 25px;
  text-align: center;
}
.sx-banner {
  width: 100%;
  height: 408px;
  /* background-image: url("../images/bannner.jpg"); */
  background-size: auto 100%;
  background-position: center center;
  background-repeat: no-repeat;
}
/* 杞挱鍥� */
.sx-swiper {
  width: 100%;
  height: 110px;
  margin-top: 70px;
}
.sx-swiper img {
  cursor: pointer;
  width: 100%;
  height: 100%;
}

/* 鍒嗛〉 */
.page-new {
  margin-top: 00px;
  display: flex;
  justify-content: flex-end;
}
.page-new .layui-laypage a {
  color: #666 !important;
  margin-left: 6px;
  font-weight: 400;
}
.page-new .layui-laypage a:hover {
  color: #ff6f06 !important;
}
.page-new .layui-laypage .layui-laypage-curr {
  margin-left: 6px;
  border: 1px solid #ff6f06;
}
.page-new .layui-laypage .layui-laypage-curr em {
  color: #ff6f06;
  font-weight: 400;
}
.page-new .layui-laypage .layui-laypage-curr .layui-laypage-em {
  padding: 0;
  left: 0;
  top: 0;
}
.page-new select {
  color: #666;
  font-weight: 400;
}
.page-new .layui-laypage-btn {
  color: #666;
}

.go-page {
  height: 40px;
  line-height: 40px;
  margin-left: 5px;
  color: #666;
  font-size: 14px;
}

.go-page input {
  border-radius: 4px;
  border: 1px solid #dcdee2;
  height: 30px;
  width: 66px;
  margin: auto 5px;
  padding-left: 5px;
}

.content-overflow {
  height: 100%;
  overflow-y: auto;
  /* min-width: 100vh; */
}
