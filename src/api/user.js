/*
 * @Author: wzc
 * @Date: 2022-04-11 11:32:06
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-24 20:28:55
 * @FilePath: /bid/src/api/user.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/api/login',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/api/user/my',
    method: 'get',
  })
}

export function updateUser(data) {
  return request({
    url: '/api/user/my',
    method: 'post',
    data
  })
}

export function updateUserInfo(data) {
  return request({
    url: `/api/user/${data.id}`,
    method: 'post',
    data
  })
}

export function addUser(data) {
  return request({
    url: '/api/company_info/user/add',
    method: 'post',
    data
  })
}


export function logout() {
  return request({
    url: '/api/logout',
    method: 'get'
  })
}

export function getUserList(params) {
  return request({
    url: '/api/users',
    method: 'get',
    params
  })
}

export function getUserById(id) {
  return request({
    url: `/api/user/${id}`,
    method: 'get'
  })
}

export function updatePassword(data) {
  return request({
    url: '/api/user/reset_pass',
    method: 'put',
    data
  })
}

export function getCompanyInfo() {
  return request({
    url: '/api/company_info',
    method: 'get'
  })
}

export function updateCompanyInfo(data) {
  return request({
    url: '/api/company_info/update',
    method: 'post',
    data
  })
}
export function sendMsg(phone) {
  return request({
    url: '/api/send_msg',
    method: 'post',
    data: { phone }
  })
}

export function register(data) {
  return request({
    url: '/api/register',
    method: 'post',
    data
  })
}

export function updateState(id, state) {
  return request({
    url: `/api/user/state/${id}`,
    method: 'put',
    data: {
      state
    }
  })
}

export function findPwdSubmit(data) {
  return request({
    url: `/api/found_password`,
    method: 'post',
    data
  })
}

export function findPwdSendMsg(phone) {
  return request({
    url: `/api/found_pwd_send_msg`,
    method: 'post',
    params: {
      phone
    }
  })
}



