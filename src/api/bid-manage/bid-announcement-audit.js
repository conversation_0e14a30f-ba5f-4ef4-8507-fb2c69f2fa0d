/*
 * @Author: wzc
 * @Date: 2024-12-01 10:48:50
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 18:27:41
 * @FilePath: /bid/src/api/bid-manage/bid-announcement-audit.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getBidAnnouncements(params) {
    return request({
        url: '/api/admin/announcements',
        method: 'get',
        params,
    })
}

export function auditBidAnnouncement(data) {
    return request({
        url: '/api/admin/approval_announcements',
        method: 'post',
        data
    })
}

export function getBidAnnouncementById(id) {
    return request({
        url: `/api/admin/announcements/${id}`,
        method: 'get'
    })
}

export function getBidHalfAnnouncements(params) {
    return request({
        url: '/api/admin/tender_notice',
        method: 'get',
        params,
    })
}

export function getBidHalfAnnouncementById(id) {
    return request({
        url: `/api/admin/tender_notice/${id}`,
        method: 'get'
    })
}