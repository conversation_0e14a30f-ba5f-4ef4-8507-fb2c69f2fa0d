/*
 * @Author: wzc
 * @Date: 2024-11-03 08:50:40
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 21:09:00
 * @FilePath: /bid/src/api/bid-manage/project-info.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getProjectList(params) {
    return request({
        url: '/api/bid/manage/project',
        method: 'get',
        params,
    })
}

export function addProject(data) {
    return request({
        url: '/api/bid/manage/project',
        method: 'post',
        data
    })
}

export function updateProject(id, data) {
    return request({
        url: `/api/bid/manage/project/${id}`,
        method: 'post',
        data
    })
}

export function getProjectById(id) {
    return request({
        url: `/api/bid/manage/project/${id}`,
        method: 'get'
    })
}

export function deleteProject(id) {
    return request({
        url: `/api/bid/manage/project/${id}`,
        method: 'delete'
    })
}

export function getIndustries() {
    return request({
        url: '/api/industries',
        method: 'get'
    })
}

export function getGeoAreas() {
    return request({
        url: '/api/geo_areas',
        method: 'get'
    })
}

//超管查看所有项目列表
export function getAllProjectList(params) {
    return request({
        url: '/api/admin/project',
        method: 'get',
        params,
    })
}
//超管查看项目详情
export function getProjectInfoById(id) {
    return request({
        url: `/api/admin/project_show/${id}`,
        method: 'get'
    })
}