/*
 * @Author: 廊坊汇集科技
 * @Date: 2024-11-30 14:18:43
 * @LastEditors: huiji
 * @LastEditTime: 2025-07-06 22:15:54
 * @FilePath: /bidding-web/src/api/bid-manage/bid-doc.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function addBidDocumnet(data) {
    return request({
        url: '/api/bid/manage/segment/document',
        method: 'post',
        data
    })
}
export function updateBidDocumnet(id, data) {
    return request({
        url: `/api/bid/manage/segment/document/${id}`,
        method: 'post',
        data
    })
}
export function getBidDocumnet(params) {
    return request({
        url: '/api/bid/manage/segment/document',
        method: 'get',
        params
    })
}

export function detailBidDocumnet(id) {
    return request({
        url: `/api/bid/manage/segment/document/${id}`,
        method: 'get'
    })
}
export function deteleBidDocumnet(id) {
    return request({
        url: `/api/bid/manage/segment/document/${id}`,
        method: 'DELETE'
    })
}

//超管招标文件接口

export function getAdminDocumnet(params) {
    return request({
        url: `/api/admin/document`,
        method: 'get',
        params
    })
}
export function detailAdminDocumnet(id) {
    return request({
        url: `/api/admin/document/${id}`,
        method: 'get',
    })
}

export function approvalDocumnet(data) {
    return request({
        url: `/api/admin/approval_document`,
        method: 'post',
        data
    })
}

export function getBidDocCount(id) {
    return request({
        url: `/api/bid/manage/segment/document/${id}`,
        method: 'get',
    })
}

export function downloadBidFile(id, params) {
    return request({
        url: `/api/bid/manage/segment/document/file/${id}`,
        method: 'get',
        responseType: 'blob', // 重要：设置响应类型为blob
        params
    })
}

export function getBidFileDetail(bid_segment_id) {
    return request({
        url: `/api/bid/manage/segment/info`,
        method: 'get',
        params: {
            bid_segment_id
        }
    })
}

export function checkBidPayment(bid_segment_id) {
    return request({
        url: '/api/invite_bid/check_bid_payment',
        method: 'get',
        params: { bid_segment_id }
    });
}