/*
 * @Author: wzc
 * @Date: 2024-11-30 14:18:43
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 18:28:36
 * @FilePath: /bid/src/api/bid-manage/bid-project-audit.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getBidProjectList(params) {
    return request({
        url: '/api/admin/bidding_projects',
        method: 'get',
        params,
    })
}

export function auditBidProject(data) {
    return request({
        url: '/api/admin/approval_project',
        method: 'post',
        data
    })
}

export function getBidProjectById(id) {
    return request({
        url: `/api/admin/bidding_projects_show/${id}`,
        method: 'get'
    })
}
