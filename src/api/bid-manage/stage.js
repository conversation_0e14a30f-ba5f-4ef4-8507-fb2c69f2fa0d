/*
 * @Author: wzc
 * @Date: 2024-11-05 22:54:41
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 17:27:49
 * @FilePath: /bid/src/api/bid-manage/state.js
 * @copyright: wzc
 * @Description: 标段的评审办法
 */
import request from '@/utils/request'

export function getStageList(params) {
    return request({
        url: '/api/bid/manage/segment_stages',
        method: 'get',
        params,
    })
}

export function addStage(data) {
    return request({
        url: '/api/bid/manage/segment_stages',
        method: 'post',
        data
    })
}

export function delStage(id) {
    return request({
        url: `/api/bid/manage/segment_stages/${id}`,
        method: 'delete'
    })
}

export function addNode(data) {
    return request({
        url: '/api/bid/manage/segment_stage_nodes',
        method: 'post',
        data
    })
}

export function delNode(id) {
    return request({
        url: `/api/bid/manage/segment_stage_nodes/${id}`,
        method: 'delete'
    })
}

export function addClauses(data) {
    return request({
        url: '/api/bid/manage/segment_stage_node_clauses',
        method: 'post',
        data
    })
}

export function delClauses(id) {
    return request({
        url: `/api/bid/manage/segment_stage_node_clauses/${id}`,
        method: 'delete'
    })
}

export function updateClauses(id, data) {
    return request({
        url: `/api/bid/manage/segment_stage_node_clauses/${id}`,
        method: 'put',
        data
    })
}

export function getClausesById(id) {
    return request({
        url: `/api/bid/manage/segment_stage_node_clauses/${id}`,
        method: 'get'
    })
}

export function getStageNodes(params) {
    return request({
        url: '/api/bid/manage/segment_stage_nodes',
        method: 'get',
        params,
    })
}

export function getNodeAudit(params) {
    return request({
        url: '/api/bid/manage/segment_stage_node_clauses',
        method: 'get',
        params,
    })
}

export function updateAuditResult(data) {
    return request({
        url: '/api/bid/manage/segment_stage_node_clauses_score',
        method: 'post',
        data
    })
}
