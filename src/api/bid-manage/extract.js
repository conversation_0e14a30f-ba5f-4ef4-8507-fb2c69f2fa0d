/*
 * @Author: 廊坊汇集科技
 * @Date: 2024-11-30 14:18:43
 * @LastEditors: 廊坊汇集科技
 * @LastEditTime: 2024-12-01 18:28:36
 * @FilePath: /bid/src/api/bid-manage/bid-project-audit.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function addExtraction(data) {
    return request({
        url: '/api/bid/manage/project_expert_extraction',
        method: 'post',
        data
    })
}
export function updateExtraction(id, data) {
    return request({
        url: `/api/bid/manage/project_expert_extraction/${id}`,
        method: 'put',
        data
    })
}
export function getExtraction(params) {
    return request({
        url: '/api/bid/manage/project_expert_extraction',
        method: 'get',
        params
    })
}

export function detailExtraction(id) {
    return request({
        url: `/api/bid/manage/project_expert_extraction/${id}`,
        method: 'get'
    })
}
export function deteleExtraction(id) {
    return request({
        url: `/api/bid/manage/project_expert_extraction/${id}`,
        method: 'DELETE'
    })
}
export function extractExpertList(data) {
    return request({
        url: `/api/extractExpertList`,
        method: 'post',
        data
    })
}

//获取地区
export function getCity(params) {
    return request({
        url: `/api/get_city`,
        method: 'get',
        params
    })
}


//超管
export function getApprovalList(params) {
    return request({
        url: `/api/admin/approval_project_expert_extraction`,
        method: 'get',
        params
    })
}
export function detailApproval(id) {
    return request({
        url: `/api/admin/approval_project_expert_extraction/${id}`,
        method: 'get',
    })
}

export function approvalExpertList(data) {
    return request({
        url: `/api/admin/approval_project_expert_extraction`,
        method: 'post',
        data
    })
}


