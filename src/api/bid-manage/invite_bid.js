/*
 * @Author: wzc
 * @Date: 2024-11-03 08:50:40
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 21:09:00
 * @FilePath: /bid/src/api/bid-manage/project-info.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getRegistrationsList(params) {
    return request({
        url: '/api/invite_bid/registrations_list',
        method: 'get',
        params,
    })
}

export function getRegistrationsMyList(params) {
    return request({
        url: '/api/invite_bid/registrations_list/my',
        method: 'get',
        params,
    })
}

export function getRegistrationsDetail(id) {
    return request({
        url: `/api/invite_bid/registrations_detail/${id}`,
        method: 'get'
    })
}

export function geInviteDetail(id) {
    return request({
        url: `/api/invite_bid/segments/${id}`,
        method: 'get',
    })
}

export function geInviteSegmentsDetail(id) {
    return request({
        url: `/api/invite_bid/bid_detail/${id}`,
        method: 'get',
    })
}

export function bidRegistrations(data) {
    return request({
        url: '/api/invite_bid/registrations',
        method: 'post',
        data
    })
}

export function uploadBidFile(data) {
    return request({
        url: '/api/invite_bid/documents',
        method: 'post',
        data
    })
}

export function getBidFileDetail(id) {
    return request({
        url: `/api/invite_bid/documents/${id}`,
        method: 'get',
    })
}

export function deleteFileLog(id) {
    return request({
        url: `/api/invite_bid/documents/${id}`,
        method: 'delete'
    })
}

export function getFiledNameValue(id) {
    return request({
        url: `/api/bid/manage/segment/custom_field_values`,
        method: 'get',
        params: {
            bid_segment_id: id
        }
    })
}
