/*
 * @Author: wzc
 * @Date: 2024-11-05 22:54:41
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 17:27:49
 * @FilePath: /bid/src/api/bid-manage/bid-project.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getBidProjectList(params) {
    return request({
        url: '/api/bid/manage/bidding_projects',
        method: 'get',
        params,
    })
}

export function addBidProject(data) {
    return request({
        url: '/api/bid/manage/bidding_projects',
        method: 'post',
        data
    })
}

export function getBidProjectById(id) {
    return request({
        url: `/api/bid/manage/bidding_projects/${id}`,
        method: 'get'
    })
}

export function updateBidProject(id, data) {
    return request({
        url: `/api/bid/manage/bidding_projects/${id}`,
        method: 'put',
        data
    })
}

export function deleteBidProject(id) {
    return request({
        url: `/api/bid/manage/bidding_projects/${id}`,
        method: 'delete'
    })
}

export function getTitles() {
    return request({
        url: '/api/career/titles',
        method: 'get'
    })
}

export function getDivisions() {
    return request({
        url: '/api/divisions',
        method: 'get'
    })
}

export function addBidAnnouncements(data) {
    return request({
        url: '/api/bid/manage/announcements',
        method: 'post',
        data
    })
}

export function updateBidAnnouncements(id, data) {
    return request({
        url: `/api/bid/manage/announcements/${id}`,
        method: 'post',
        data
    })
}

export function getBidAnnouncements(params) {
    return request({
        url: '/api/bid/manage/announcements',
        method: 'get',
        params,
    })
}
export function deleteBidAnnouncements(id) {
    return request({
        url: `/api/bid/manage/announcements/${id}`,
        method: 'delete'
    })
}
export function getBidAnnouncementById(id) {
    return request({
        url: `/api/bid/manage/announcements/${id}`,
        method: 'get',
    })
}

export function getBidderByName(name) {
    return request({
        url: '/api/bid/manage/bidder/by_name',
        method: 'get',
        params: { name, }
    })
}

export function getBidProjectSegments(params) {
    return request({
        url: '/api/bid/manage/segments',
        method: 'get',
        params,
    })
}

export function addBidProjectSegments(data) {
    return request({
        url: '/api/bid/manage/segments',
        method: 'post',
        data
    })
}

export function getBidSegmentsLog(params) {
    return request({
        url: '/api/bid/manage/agency_contract_scan',
        method: 'get',
        params,
    })
}

export function addBidSegmentsLog(data) {
    return request({
        url: '/api/bid/manage/agency_contract_scan',
        method: 'post',
        data
    })
}
export function updateBidSegmentsLog(id, data) {
    return request({
        url: `/api/bid/manage/agency_contract_scan/${id}`,
        method: 'put',
        data
    })
}
export function deleteBidSegmentsLog(id) {
    return request({
        url: `/api/bid/manage/agency_contract_scan/${id}`,
        method: 'delete'
    })
}

export function getBidSegmentLogId(id) {
    return request({
        url: `/api/bid/manage/agency_contract_scan/${id}`,
        method: 'get'
    })
}

export function getBidSegmentFiles(id) {
    return request({
        url: `/api/bid/manage/segment/files`,
        method: 'get',
        params: {
            bid_segment_id: id
        }
    })
}

export function addBidSegmentFiles(data) {
    return request({
        url: '/api/bid/manage/segment/files',
        method: 'post',
        data
    })
}

export function generateAnnouncement(data) {
    return request({
        url: '/api/bid/manage/announcements/generateAnnouncement',
        method: 'post',
        data
    })
}

export function addBidHalfAnnouncements(data) {
    return request({
        url: '/api/bid/manage/segment/announcements',
        method: 'post',
        data
    })
}

export function updateBidHalfAnnouncements(id, data) {
    return request({
        url: `/api/bid/manage/segment/announcements/${id}`,
        method: 'post',
        data
    })
}

export function getBidHalfAnnouncements(params) {
    return request({
        url: '/api/bid/manage/segment/announcements',
        method: 'get',
        params,
    })
}
export function deleteBidHalfAnnouncements(id) {
    return request({
        url: `/api/bid/manage/segment/announcements/${id}`,
        method: 'delete'
    })
}
export function getBidHalfAnnouncementById(id) {
    return request({
        url: `/api/bid/manage/segment/announcements/${id}`,
        method: 'get',
    })
}

export function auditBidsegmentLog(data) {
    return request({
        url: `/api/admin/agency_contract_scan_log`,
        method: 'post',
        data
    })
}

export function getBidHalfDetailBySegmentId(params) {
    return request({
        url: `/api/bid/manage/segment/show_detail`,
        method: 'get',
        params
    })
}

export function getNotifications(params) {
    return request({
        url: `/api/bid/notifications`,
        method: 'get',
        params
    })
}

export function getBidCompanyList(params) {
    return request({
        url: `/api/bid/notification/bid_document_submission`,
        method: 'get',
        params
    })
}

export function addNotifications(data) {
    return request({
        url: `/api/bid/notifications`,
        method: 'post',
        data
    })
}

export function getNotificationById(id) {
    return request({
        url: `/api/bid/notifications/${id}`,
        method: 'get'
    })
}

export function updateNotification(id, data) {
    return request({
        url: `/api/bid/notifications/${id}`,
        method: 'put',
        data
    })
}

export function deleteNotification(id) {
    return request({
        url: `/api/bid/notifications/${id}`,
        method: 'delete'
    })
}

export function downloadNotification(id) {
    return request({
        url: `/api/bid/notification_down`,
        method: 'get',
        responseType: 'blob', // 重要：设置响应类型为blob
        params: {
            id,
        }
    })
}

export function downloadCheck(id) {
    return request({
        url: `/api/invite_bid/check_pay`,
        method: 'get',
        params: {
            bid_segment_id: id,
        }
    })
}

export function goAliPay(id) {
    return request({
        url: `/alipay/pay`,
        method: 'get',
        params: {
            id,
        }
    })
}

export function getBidderCompanyList(params) {
    return request({
        url: `/api/admin/submission/list`,
        method: 'get',
        params
    })
}

export function getRegisterList(params) {
    return request({
        url: `/api/admin/registration/list`,
        method: 'get',
        params
    })
}
export function getPayLog(params) {
    return request({
        url: `/api/admin/order`,
        method: 'get',
        params
    })
}

export function getBidRule(bid_segment_id) {
    return request({
        url: `/api/bid/manage/bid_evaluation_rules`,
        method: 'get',
        params: {
            bid_segment_id
        }
    })
}

export function updateBidRule(data) {
    return request({
        url: `/api/bid/manage/bid_evaluation_rules`,
        method: 'put',
        data
    })
}

export function getQuestionList(params) {
    return request({
        url: `/api/bid/manage/questions`,
        method: 'get',
        params
    })
}

export function getResponsesList(params) {
    return request({
        url: `/api/bid/manage/responses`,
        method: 'get',
        params
    })
}

export function addQuestion(data) {
    return request({
        url: `/api/bid/manage/questions`,
        method: 'post',
        data
    })
}

export function responsesQuestion(data) {
    return request({
        url: `/api/bid/manage/responses`,
        method: 'post',
        data
    })
}

export function getQuestionById(id) {
    return request({
        url: `/api/bid/manage/questions/${id}`,
        method: 'get'
    })
}

export function getResponsesById(id) {
    return request({
        url: `/api/bid/manage/responses/${id}`,
        method: 'get'
    })
}

export function updateResponsesById(id, data) {
    return request({
        url: `/api/bid/manage/responses/${id}`,
        method: 'put',
        data
    })
}

export function getOpenFieldNames(id) {
    return request({
        url: `/api/bid/manage/segment/custom_fields`,
        method: 'get',
        params: {
            bid_segment_id: id
        }
    })
}

export function setOpenFieldNames(data) {
    return request({
        url: `/api/bid/manage/segment/custom_fields`,
        method: 'post',
        data
    })
}

export function getAuditList(params) {
    return request({
        url: `/api/bid/manage/segment/registration_list`,
        method: 'get',
        params
    })
}

export function getAuditInfo(id) {
    return request({
        url: `/api/bid/manage/segment/registration/${id}`,
        method: 'get'
    })
}

export function submitAudit(data) {
    return request({
        url: `/api/bid/manage/segment/registration_approval`,
        method: 'post',
        data
    })
}

export function getReportList(id) {
    return request({
        url: `/api/bid/manage/bid_evaluation_report`,
        method: 'get',
        params: {
            bid_segment_id: id
        }
    })
}

export function deleteReportFile(id) {
    return request({
        url: `/api/bid/manage/bid_evaluation_report/${id}`,
        method: 'delete'
    })
}


export function uploadReportFile(data) {
    return request({
        url: `/api/bid/manage/bid_evaluation_report`,
        method: 'post',
        data
    })
}

export function exportAllExpertScores(params) {
    return request({
        url: '/api/bid/manage/export_all_expert_scores',
        method: 'get',
        params,
        responseType: 'blob'
    })
}

export function getBidDocumentReceipt(id) {
    return request({
        url: `/api/public/bid_document_receipt/${id}`,
        method: 'get'
    })
}

export function exportChatRecords(segmentId) {
    return request({
        url: `/api/expert/chat/export-all/${segmentId}`,
        method: 'get',
        responseType: 'blob'
    })
}


export function exportSelectRecords(segmentId) {
    return request({
        url: `/api/bid/manage/project_expert_extraction/export/situation`,
        method: 'get',
        responseType: 'blob',
        params: {
            bid_segment_id: segmentId
        }
    })
}