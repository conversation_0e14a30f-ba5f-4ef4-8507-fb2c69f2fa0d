import request from "@/utils/request";

// 获取专家端的投标人联系人列表
export function getExpertContactList(segmentId) {
    return request({
        url: `/api/expert/chat/bidders/${segmentId}`,
        method: "get",
    });
}

// 获取投标人端的专家联系人列表
export function getBidderContactList(segmentId) {
    return request({
        url: `/api/company/chat/experts/${segmentId}`,
        method: "get",
    });
}

// 获取聊天消息记录
export function getChatMessages(params) {
    const { status, segmentId, contactId, page, per_page } = params;
    const userType = status == 3 ? "expert" : "company";
    const contactType = status == 3 ? "companyId" : "expertUserId";
    // The API doc is a bit inconsistent, using companyId for expert and expertUserId for company
    const url =
        status == 3
            ? `/api/expert/chat/messages/${segmentId}/${contactId}`
            : `/api/company/chat/messages/${segmentId}/${contactId}`;

    return request({
        url: url,
        method: "get",
        params: {
            page,
            per_page,
        },
    });
}

// 发送消息
export function sendMessage(params) {
    const { status, segmentId, contactId, message } = params;
    const url =
        status == 3
            ? `/api/expert/chat/messages/${segmentId}/${contactId}`
            : `/api/company/chat/messages/${segmentId}/${contactId}`;

    return request({
        url: url,
        method: "post",
        data: {
            message,
        },
    });
}

// 获取未读消息总数
export function getUnreadCount(segmentId) {
    return request({
        url: `/api/chat/unread-count/${segmentId}`,
        method: "get",
    });
}

// 标记为已读
export function markAsRead(params) {
    const { status, segmentId, contactId } = params;
    const url =
        status == 3
            ? `/api/expert/chat/read/${segmentId}/${contactId}`
            : `/api/company/chat/read/${segmentId}/${contactId}`;

    return request({
        url: url,
        method: "put",
    });
} 