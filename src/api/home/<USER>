import request from '@/utils/request'

export function getHallList(params) {
    return request({
        url: '/api/hall/list_hall',
        method: 'get',
        params
    })
}

export function inviteHallFunction(params) {
    return request({
        url: '/api/hall/open_hall',
        method: 'get',
        params
    })
}

export function bidHallFunction(params) {
    return request({
        url: '/api/hall/bid_open_hall',
        method: 'get',
        params
    })
}

export function getSegmentFiles(params) {
    return request({
        url: '/api/hall/export_evaluation',
        method: 'get',
        params
    })
}

export function getOpenBidList(params) {
    return request({
        url: '/api/hall/bid_document_submission',
        method: 'get',
        params
    })
}

export function getOpenBidListDownload(params) {
    return request({
        url: '/api/hall/bid_document_submission',
        method: 'get',
        responseType: 'blob', // 重要：设置响应类型为blob
        params
    })
}

export function getTwoPriceList(params) {
    return request({
        url: '/api/hall/two_price_list',
        method: 'get',
        params
    })
}

export function getTwoPriceListDownload(params) {
    return request({
        url: '/api/hall/two_price_list',
        method: 'get',
        responseType: 'blob', // 重要：设置响应类型为blob
        params
    })
}


export function expertAuditBid(data) {
    return request({
        url: '/api/hall/export_evaluation',
        method: 'put',
        data
    })
}

export function getExpertAuditReport(id) {
    return request({
        url: `/api/hall/export_evaluation/${id}`,
        method: 'get'
    })
}

export function twoSubmission(data) {
    return request({
        url: `/api/invite_bid/bid_document_two_submission`,
        method: 'post',
        data
    })
}


export function getBidAuditLog(params) {
    return request({
        url: `/api/bid/manage/segment_score`,
        method: 'get',
        params
    })
}

export function expertSign(data) {
    return request({
        url: `/api/hall/selection_status`,
        method: 'put',
        data
    })
}

export function getBidAuditResult(params) {
    return request({
        url: `/api/bid/manage/segment_expert_score`,
        method: 'get',
        params
    })
}

export function getExpertList(params) {
    return request({
        url: `/api/hall/bid_segment_expert_list`,
        method: 'get',
        params
    })
}

export function submitLeader(data) {
    return request({
        url: `/api/hall/evaluation_leader_submission`,
        method: 'post',
        data
    })
}

export function submitAuditStatus(data) {
    return request({
        url: `/api/bid/manage/segment_stage_node_status`,
        method: 'post',
        data
    })
}

export function submitTwoPrice(id) {
    return request({
        url: `/api/invite_bid/two_price_type`,
        method: 'put',
        params: {
            bid_segment_id: id
        }
    })
}

export function getBidAuditLogDownload(params) {
    return request({
        url: `/api/bid/manage/segment_score`,
        method: 'get',
        responseType: 'blob', // 重要：设置响应类型为blob
        params
    })
}