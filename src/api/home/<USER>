import request from '@/utils/request'

export function homeAnnouncementList(params) {
  return request({
    url: '/api/home_announcement',
    method: 'get',
    params
  })
}

export function getAnnouncementDetail(params) {
  return request({
    url: '/api/home_announcement_detail',
    method: 'get',
    params
  })
}

//政策法规
export function getNotices(params) {
  return request({
    url: '/api/home_notices',
    method: 'get',
    params
  })
}

export function getNoticesTwo(params) {
  return request({
    url: '/api/home_notices_two',
    method: 'get',
    params
  })
}

export function getNoticesDetail(id) {
  return request({
    url: `/api/home_notices_detail/${id}`,
    method: 'get',
  })
}

export function getNoticesList(params) {
  return request({
    url: '/api/admin/notice_list',
    method: 'get',
    params
  })
}

export function addNotice(data) {
  return request({
    url: '/api/admin/notice_list',
    method: 'post',
    data
  })
}

export function updateNotice(id, data) {
  return request({
    url: `/api/admin/notice_list/${id}`,
    method: 'post',
    data
  })
}

export function deleteNotice(id) {
  return request({
    url: `/api/admin/notice_list/${id}`,
    method: 'delete'
  })
}

export function getNoticeById(id) {
  return request({
    url: `/api/admin/notice_list/${id}`,
    method: 'get'
  })
}
