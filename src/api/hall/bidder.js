/*
 * @Author: wzc
 * @Date: 2024-11-13 23:04:15
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-14 00:00:16
 * @FilePath: /bid/src/api/hall/bidder.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getBidderList(params) {
    return request({
        url: '/api/bid/manage/bidder',
        method: 'get',
        params,
    })
}

export function addBidder(data) {
    return request({
        url: '/api/bid/manage/bidder',
        method: 'post',
        data
    })
}

export function updateBidder(id, data) {
    return request({
        url: `/api/bid/manage/bidder/${id}`,
        method: 'put',
        data
    })
}

export function deleteBidder(id) {
    return request({
        url: `/api/bid/manage/bidder/${id}`,
        method: 'delete'
    })
}

export function getBidderById(id) {
    return request({
        url: `/api/bid/manage/bidder/${id}`,
        method: 'get'
    })
}

// 获取修改记录
export function getModificationRecords(params) {
    return request({
        url: '/api/modification_records',
        method: 'get',
        params
    })
}