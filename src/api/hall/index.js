/*
 * @Author: wzc
 * @Date: 2024-11-13 23:04:15
 * @LastEditors: huiji
 * @LastEditTime: 2025-07-05 23:05:23
 * @FilePath: /bidding-web/src/api/hall/index.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getBidderHallList(params) {
    return request({
        url: '/api/hall/bid_hall',
        method: 'get',
        params,
    })
}

export function getInviteHallList(params) {
    return request({
        url: '/api/hall/tendering_hall',
        method: 'get',
        params,
    })
}

export function getExpertHallList(params) {
    return request({
        url: '/api/hall/expert_hall',
        method: 'get',
        params,
    })
}

export function getQuestionList(params) {
    return request({
        url: `/api/expert/clarifications`,
        method: 'get',
        params
    })
}

export function getResponsesList(params) {
    return request({
        url: `/api/invite_bid/clarifications`,
        method: 'get',
        params
    })
}

export function addQuestion(data) {
    return request({
        url: `/api/expert/clarifications`,
        method: 'post',
        data
    })
}

export function responsesQuestion(data) {
    return request({
        url: `/api/invite_bid/clarifications`,
        method: 'post',
        data
    })
}

export function getQuestionById(id) {
    return request({
        url: `/api/expert/clarifications/${id}`,
        method: 'get'
    })
}

export function getResponsesById(id) {
    return request({
        url: `/api/invite_bid/clarifications/${id}`,
        method: 'get'
    })
}

export function updateResponsesById(id, data) {
    return request({
        url: `/api/invite_bid/clarifications/${id}`,
        method: 'put',
        data
    })
}

export function checkBidOpening(segmentId) {
    console.log(segmentId);

    return request({
        url: `/api/company/check-bid-opening/${segmentId}`,
        method: 'get',
    });
}
