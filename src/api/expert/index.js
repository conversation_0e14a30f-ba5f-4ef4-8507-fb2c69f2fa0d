/*
 * @Author: wzc
 * @Date: 2024-11-28 00:18:25
 * @LastEditors: 王子超
 * @LastEditTime: 2024-12-03 00:03:07
 * @FilePath: /bidding-web/src/api/expert/index.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getExpertList(params) {
    return request({
        url: '/api/admin/expertList',
        method: 'get',
        params,
    })
}

export function auditExpert(data) {
    return request({
        url: `/api/admin/approvalExpert`,
        method: 'post',
        data
    })
}

export function getExpertById(id) {
    return request({
        url: `/api/admin/expertShow/${id}`,
        method: 'get'
    })
}

export function getExpertInfo() {
    return request({
        url: `/api/expert/expert_detail`,
        method: 'get'
    })
}

export function getProjectList(params) {
    return request({
        url: `/api/expert/expertProjectList`,
        method: 'get',
        params,
    })
}

export function getPromiseList(params) {
    return request({
        url: `/api/expert/expertPromiseList`,
        method: 'get',
        params,
    })
}

export function expertPromise(params) {
    return request({
        url: `/api/expert/expertPromise`,
        method: 'get',
        params,
    })
}



