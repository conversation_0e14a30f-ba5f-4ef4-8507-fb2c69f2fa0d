/*
 * @Author: wzc
 * @Date: 2024-11-16 12:47:54
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-27 23:39:01
 * @FilePath: /bid/src/api/expert/expert.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getExpertList(params) {
    return request({
        url: '/api/expertList',
        method: 'get',
        params,
    })
}

export function addExpert(data) {
    return request({
        url: '/api/saveExpert',
        method: 'post',
        data
    })
}

export function updateExpert(id, data) {
    return request({
        url: `/api/saveExpert`,
        method: 'post',
        data
    })
}

export function deleteExpert(id) {
    return request({
        url: `/api/delExpertList/${id}`,
        method: 'delete'
    })
}

export function getExpertById(id) {
    return request({
        url: `/api/expertDetail`,
        method: 'get',
        params: { expert_id: id }
    })
}

export function getExpertTypes() {
    return request({
        url: `/api/expert_categories`,
        method: 'get'
    })
}
