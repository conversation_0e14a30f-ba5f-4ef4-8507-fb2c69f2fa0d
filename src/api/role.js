/*
 * @Author: wzc
 * @Date: 2022-04-11 11:32:06
 * @LastEditors: wzc
 * @LastEditTime: 2024-10-13 14:19:56
 * @FilePath: /bid/src/api/role.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

export function getRoutes() {
  return request({
    url: '/api/menus',
    method: 'get'
  })
}

export function getRoles() {
  return request({
    url: '/api/roles',
    method: 'get'
  })
}

export function addRole(data) {
  return request({
    url: '/api/roles',
    method: 'post',
    data
  })
}

export function updateRole(id, data) {
  return request({
    url: `/api/roles/${id}`,
    method: 'put',
    data
  })
}

export function deleteRole(id) {
  return request({
    url: `/api/roles/${id}`,
    method: 'delete'
  })
}
