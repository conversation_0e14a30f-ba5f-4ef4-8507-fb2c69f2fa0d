/*
 * @Author: huiji
 * @Date: 2025-06-25 23:44:05
 * @LastEditors: huiji
 * @LastEditTime: 2025-06-25 23:55:21
 * @FilePath: /bidding-web/src/api/notification.js
 * @copyright: sunkaisens
 * @Description: 
 */
import request from '@/utils/request'

/**
 * 获取通知列表
 * @param {Object} params
 * @param {number} params.page 页码
 * @param {number} params.per_page 每页数量
 * @param {number|null} params.read_status 已读状态(0未读, 1已读, null全部)
 */
export function fetchNotifications(params = {}) {
    return request({
        url: '/api/notifications',
        method: 'get',
        params
    })
}

/**
 * 标记通知为已读
 * @param {number} notification_id 通知ID
 */
export function markNotificationRead(notification_id) {
    return request({
        url: '/api/notifications/mark-read',
        method: 'post',
        data: { notification_id }
    })
} 