import request from '@/utils/request'

export function getCompanyList(params) {
    return request({
        url: '/api/admin/index',
        method: 'get',
        params,
    })
}

export function getCompanyById(id) {
    return request({
        url: `/api/admin/show/${id}`,
        method: 'get'
    })
}

export function auditCompany(data) {
    return request({
        url: '/api/admin/approval',
        method: 'post',
        data
    })
}

export function getUsersByCompanyId(id) {
    return request({
        url: `/api/admin/company/users`,
        method: 'get',
        params: {
            company_info_id: id
        }
    })
}
