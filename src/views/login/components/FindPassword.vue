<template>
  <div>
    <el-form :model="form" ref="form" label-width="120px">
      <el-form-item
        label="手机号"
        prop="phone"
        :rules="[{ required: true, message: '请输入手机号', trigger: 'blur' }]"
      >
        <el-input
          v-model="form.phone"
          placeholder="请输入您的手机号"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="短信验证码"
        prop="sms_code"
        :rules="[{ required: true, message: '请输入验证码', trigger: 'blur' }]"
      >
        <el-row>
          <el-input v-model="form.sms_code" placeholder="短信验证码">
          </el-input>

          <el-button
            type="primary"
            @click="getSmsCode"
            :disabled="countdownActive"
          >
            {{ countdownActive ? countdownTime : "" }}获取验证码</el-button
          >
        </el-row>
      </el-form-item>
      <el-form-item
        label="新密码"
        prop="password"
        :rules="[{ required: true, message: '请输新密码', trigger: 'blur' }]"
      >
        <el-input
          type="password"
          v-model="form.password"
          placeholder="请输入新密码"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="确认新密码"
        prop="password_confirmation"
        :rules="[
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
        ]"
      >
        <el-input
          type="password"
          v-model="form.password_confirmation"
          placeholder="请再次输入新密码"
        ></el-input>
      </el-form-item>

      <!-- <el-form-item>
        <el-checkbox v-model="form.agree"
          >点击“立即注册”表示同意并愿意遵守</el-checkbox
        >
        <el-button type="text" @click="open">《用户协议》</el-button>
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" @click="submitForm">提交</el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      title="用户协议"
      :modal="false"
      :visible.sync="visible"
      width="60%"
      :before-close="handleClose"
    >
      <div style="height: 500px; overflow: auto" v-html="text"></div>
    </el-dialog>
  </div>
</template>

<script>
import { text } from "./text";
export default {
  data() {
    return {
      visible: false,
      form: {
        company_name: "",
        name: "",
        password: "",
        password_confirmation: "",
        phone: "",
        sms_code: "",
        agree: false,
      },
      text: text,
      countdownActive: false,
      timer: null,
      countdownTime: 120,
    };
  },
  methods: {
    open() {
      this.visible = true;
    },
    handleClose() {
      this.visible = false;
    },
    getSmsCode() {
      if (this.form.phone) {
        this.$emit("send", this.form.phone);
        this.startCountdown();
      } else {
        this.$message.error("手机号不能为空");
      }
    },
    startCountdown() {
      this.countdownActive = true; // 禁用按钮
      // this.countdownText = `${this.countdownTime}秒后可点击`;
      this.timer = setInterval(() => {
        // 设置定时器
        if (this.countdownTime > 0) {
          this.countdownTime--; // 倒计时减1
          // this.countdownText = `${this.countdownTime}秒后可点击`;
        } else {
          clearInterval(this.timer); // 清除定时器
          this.timer = null;
          this.countdownActive = false; // 启用按钮
          // this.countdownText = "点击开始倒计时"; // 重置按钮文本
          this.countdownTime = 120; // 重置倒计时时间
        }
      }, 1000);
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.password !== this.form.password_confirmation) {
            this.$message.warning("两次密码输入不一致");
            return;
          }
          this.$emit("submit", this.form);
          // 这里应该是提交表单的逻辑
          //   this.$message.success("注册成功");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.el-input {
  background-color: #7e7c7c;
  border: none;
  color: #2b3442;
}
/* .el-form-item {
  background: rgba(250, 249, 249, 0.1);
} */

.el-form-item {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: #fff !important;
  border-radius: 5px;
  color: #454545;
}
/* 添加样式以匹配图片中的布局 */
</style>
