<template>
  <div>
    <el-form :model="form" ref="form" label-width="120px">
      <el-form-item
        label="公司名称"
        prop="company_name"
        :rules="[
          { required: true, message: '请输入公司名称', trigger: 'blur' },
        ]"
      >
        <el-input
          v-model="form.company_name"
          placeholder="请输入您所在的公司名称全称"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="姓名"
        prop="name"
        :rules="[{ required: true, message: '请输入姓名', trigger: 'blur' }]"
      >
        <el-input v-model="form.name" placeholder="请输入您的姓名"></el-input>
      </el-form-item>
      <el-form-item
        label="登录密码"
        prop="password"
        :rules="[{ required: true, message: '请输入密码', trigger: 'blur' }]"
      >
        <el-input
          type="password"
          v-model="form.password"
          placeholder="请输入登录密码"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="确认密码"
        prop="password_confirmation"
        :rules="[{ required: true, message: '请确认密码', trigger: 'blur' }]"
      >
        <el-input
          type="password"
          v-model="form.password_confirmation"
          placeholder="请确认密码"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="手机号"
        prop="phone"
        :rules="[{ required: true, message: '请输入手机号', trigger: 'blur' }]"
      >
        <el-input
          v-model="form.phone"
          placeholder="请输入您的手机号"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="短信验证码"
        prop="sms_code"
        :rules="[{ required: true, message: '请输入验证码', trigger: 'blur' }]"
      >
        <el-row>
          <el-input v-model="form.sms_code" placeholder="短信验证码">
          </el-input>

          <el-button
            type="primary"
            @click="getSmsCode"
            :disabled="countdownActive"
          >
            {{ countdownActive ? countdownTime : "" }}获取验证码</el-button
          >
        </el-row>
      </el-form-item>
      <el-form-item>
        <el-checkbox v-model="form.agree"
          >点击“立即注册”表示同意并愿意遵守</el-checkbox
        >
        <el-button type="text" @click="open">《用户协议》</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">立即注册</el-button>
      </el-form-item>
    </el-form>
    <el-dialog
      title="用户协议"
      :modal="false"
      :visible.sync="visible"
      width="60%"
      :before-close="handleClose"
    >
      <div style="height: 500px; overflow: auto" v-html="text"></div>
    </el-dialog>
  </div>
</template>

<script>
import { text } from "./text";
export default {
  data() {
    return {
      visible: false,
      form: {
        company_name: "",
        name: "",
        password: "",
        password_confirmation: "",
        phone: "",
        sms_code: "",
        agree: false,
      },
      text: text,
      countdownActive: false,
      timer: null,
      countdownTime: 120,
    };
  },
  methods: {
    open() {
      this.visible = true;
    },
    handleClose() {
      this.visible = false;
    },
    getSmsCode() {
      if (this.form.phone) {
        this.$emit("send", this.form.phone);
        this.startCountdown();
      } else {
        this.$message.error("手机号不能为空");
      }
    },
    startCountdown() {
      this.countdownActive = true; // 禁用按钮
      // this.countdownText = `${this.countdownTime}秒后可点击`;
      this.timer = setInterval(() => {
        // 设置定时器
        if (this.countdownTime > 0) {
          this.countdownTime--; // 倒计时减1
          // this.countdownText = `${this.countdownTime}秒后可点击`;
        } else {
          clearInterval(this.timer); // 清除定时器
          this.timer = null;
          this.countdownActive = false; // 启用按钮
          // this.countdownText = "点击开始倒计时"; // 重置按钮文本
          this.countdownTime = 120; // 重置倒计时时间
        }
      }, 1000);
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.agree) {
            this.$message.warning("请先同意并勾选用户协议");
            return;
          }
          if (this.form.password !== this.form.password_confirmation) {
            this.$message.warning("两次密码输入不一致");
            return;
          }
          this.$emit("submit", this.form);
          // 这里应该是提交表单的逻辑
          //   this.$message.success("注册成功");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.el-input {
  background-color: #7e7c7c;
  border: none;
  color: #2b3442;
}
.el-form-item {
  background: #fff !important;
}

/* 添加样式以匹配图片中的布局 */
</style>
