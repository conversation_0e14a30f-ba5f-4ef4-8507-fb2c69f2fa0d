<template>
  <div class="login-container">
    <div class="login">
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        autocomplete="on"
        label-position="left"
      >
        <div class="title-container">
          <router-link to="/home">
            <h1 class="title">易招天成</h1>
          </router-link>
        </div>

        <el-form-item prop="phone">
          <span class="svg-container">
            <svg-icon icon-class="user" />
          </span>
          <el-input
            ref="phone"
            v-model="loginForm.phone"
            placeholder="用户名"
            name="phone"
            type="text"
            tabindex="1"
            autocomplete="on"
          />
        </el-form-item>

        <el-tooltip
          v-model="capsTooltip"
          content="Caps lock is On"
          placement="right"
          manual
        >
          <el-form-item prop="password">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="密码"
              name="password"
              tabindex="2"
              autocomplete="on"
              @keyup.native="checkCapslock"
              @blur="capsTooltip = false"
              @keyup.enter.native="handleLogin"
            />
            <span class="show-pwd" @click="showPwd">
              <svg-icon
                :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
              />
            </span>
          </el-form-item>
        </el-tooltip>

        <div style="display: flex; margin-top: 10px">
          <el-button
            :loading="loading"
            type="primary"
            style="width: 90%; margin-bottom: 30px"
            @click.native.prevent="handleLogin"
            >登陆</el-button
          >
          <el-button
            type="text"
            style="
              width: 30%;
              margin-bottom: 30px;
              color: #fff;
              background: #60a4f8;
              color: #fff;
              font-size: 14px;
            "
            @click="handleShowPwd"
            >忘记密码</el-button
          >
        </div>
        <div>
          <span>没有账号</span>
          <el-button
            type="text"
            style="width: 20%; margin-bottom: 30px; font-size: 16px"
            @click="handleRegist"
            >立即注册</el-button
          >
        </div>
      </el-form>
    </div>
    <el-dialog
      v-if="visible"
      title="注册"
      :visible.sync="visible"
      width="40%"
      :before-close="handleClose"
    >
      <regist-form
        @send="handleSendMsg"
        @submit="handleRegistSubmit"
      ></regist-form>
    </el-dialog>

    <el-dialog
      v-if="pwdVisible"
      title="忘记密码"
      :visible.sync="pwdVisible"
      width="40%"
      :before-close="handleClosePwd"
    >
      <find-password
        @send="handlePwdSendMsg"
        @submit="handlePwdSubmit"
      ></find-password>
    </el-dialog>
  </div>
</template>

<script>
import SocialSign from "./components/SocialSignin";
import RegistForm from "./components/RegistForm.vue";
import FindPassword from "./components/FindPassword.vue";

import { sendMsg, register, findPwdSendMsg, findPwdSubmit } from "@/api/user";
export default {
  name: "Login",
  components: { SocialSign, RegistForm, FindPassword },
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error("密码至少6位"));
      } else {
        callback();
      }
    };
    return {
      pwdVisible: false,
      visible: false,
      loginForm: {
        phone: "",
        password: "",
      },
      loginRules: {
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
      },
      passwordType: "password",
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query);
        }
      },
      immediate: true,
    },
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)
  },
  mounted() {
    if (this.loginForm.phone === "") {
      this.$refs.phone.focus();
    } else if (this.loginForm.password === "") {
      this.$refs.password.focus();
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    handleSendMsg(phone) {
      sendMsg(phone).then((res) => {
        console.log(res);
        this.message(res.status_code, res.message);
      });
    },
    handlePwdSendMsg(phone) {
      findPwdSendMsg(phone).then((res) => {
        console.log(res);
        this.message(res.status_code, res.message);
      });
    },
    handleShowPwd() {
      this.pwdVisible = true;
    },
    handleRegist() {
      this.visible = true;
    },
    handleClosePwd() {
      this.pwdVisible = false;
    },
    handleRegistSubmit(data) {
      register(data).then((res) => {
        this.message(res.status_code, res.message);
        if (res.status_code == 200) {
          this.visible = false;
        }
        console.log(res);
      });
    },

    handlePwdSubmit(data) {
      findPwdSubmit(data).then((res) => {
        this.message(res.status_code, res.message);
        if (res.status_code == 200) {
          this.pwdVisible = false;
        }
        console.log(res);
      });
    },

    handleClose() {
      this.visible = false;
    },
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && key >= "A" && key <= "Z";
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$store
            .dispatch("user/login", this.loginForm)
            .then(() => {
              this.$router.push({
                path: this.redirect || "/",
                query: this.otherQuery,
              });
              this.loading = false;
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
    },
    // afterQRScan() {
    //   if (e.key === 'x-admin-oauth-code') {
    //     const code = getQueryObject(e.newValue)
    //     const codeMap = {
    //       wechat: 'code',
    //       tencent: 'code'
    //     }
    //     const type = codeMap[this.auth_type]
    //     const codeName = code[type]
    //     if (codeName) {
    //       this.$store.dispatch('LoginByThirdparty', codeName).then(() => {
    //         this.$router.push({ path: this.redirect || '/' })
    //       })
    //     } else {
    //       alert('第三方登录失败')
    //     }
    //   }
    // }
  },
};
</script>

<style lang="scss" scoped>
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: rgba(0, 0, 0, 0.4);
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}
.login {
  background-image: url("../../../src/assets/images/login_bk.jpg");
  background-position: center;
  background-size: cover;
  width: 60%;
  margin-left: 20%;
  margin-top: 15%;
  border-radius: 30px;
  display: flex;
}
/* reset element-ui css */
.login-container {
  background-image: url("../../../src/assets/images/login.jpg");
  background-size: cover;
  background-position: center;
  width: 100%;
  height: 100vh;
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    color: #454545;
  }
}
</style>

<style lang="scss" scoped>
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;
.login-container {
  min-height: 100%;
  width: 100%;
  background-color: $bg;
  overflow: hidden;
  .login-form {
    position: relative;
    width: 40%;
    height: 480px;
    max-width: 100%;
    padding: 60px 35px 0;
    // margin: 0px 55%;
    overflow: hidden;
    left: 55%;
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      color: #60a4f8;
      font-weight: bold;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: bold;
      cursor: pointer;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}
</style>
