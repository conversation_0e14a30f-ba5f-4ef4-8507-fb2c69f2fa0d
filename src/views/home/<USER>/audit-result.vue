<template>
  <div>
    <el-table :data="tableData.rowData" border style="width: 100%">
      <!-- <el-table-column
        prop="person"
        label="人员\流程"
        align="center"
        width="150"
      >
        <template slot-scope="scope">
          <span v-if="scope.row.icon">{{ scope.row.icon }}</span>
          {{ scope.row.person }}
        </template>
      </el-table-column> -->
      <el-table-column
        v-for="(column, index) in tableData.title"
        :key="index"
        :label="column.label"
        align="center"
      >
        <template slot-scope="scope">
          <span v-if="column.prop === 'expert_name'">
            {{ scope.row[column.prop] }}
          </span>
          <div v-else>
            <span v-if="scope.row[column.prop] === 0">-</span>
            <el-link
              @click="handleClick(scope.row[column.prop], column, scope.$index)"
              v-else
              style="color: #558ef0"
              >✓</el-link
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <audit-bid
      v-if="currentNode.type == 1"
      :visible="auditVisible"
      :tableData="contenData"
      :node="currentNode"
      :title="title"
      @close="handleCloseNode"
    ></audit-bid>
    <audit-score
      v-else
      :title="title"
      :visible="auditVisible"
      :tableData="contenData"
      :node="currentNode"
      @close="handleCloseNode"
    ></audit-score>
  </div>
</template>

<script>
import { getNodeAudit } from "@/api/bid-manage/stage";
import AuditBid from "../audit-bid.vue";
import AuditScore from "../audit-score.vue";
export default {
  components: {
    AuditBid,
    AuditScore,
  },
  props: {
    tableData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: "",
      auditVisible: false,
      contenData: {},
      currentNode: {},
      tableData: [
        { person: "汇总", step1: 2, step2: 1, step3: 10 },
        { person: "王清祥", step1: 2, step2: 1, step3: 9 },
        { person: "董焕爽", step1: 2, step2: 1, step3: 8 },
        { person: "张苏国", step1: 2, step2: 1, step3: 7 },
        { person: "侯增勇", step1: 2, step2: 1, step3: 6 },
        {
          person: "吕红祥",
          step1: 2,
          step2: 2,
          step3: 2,
          icon: "🌐", // 图标
        },
      ],
      columns: [
        { label: "1: 初步评审", prop: "step1", mode: 1 },
        { label: "2: 价格评审", prop: "step2", mode: 1 },
        { label: "3: 详细评审", prop: "step3", mode: 2 },
      ],
    };
  },
  methods: {
    handleClick(val, node, index) {
      let rowLength = this.tableData.rowData.length;
      if (index === rowLength - 1) {
        return;
      }
      // this.auditVisible = true;
      let nodeId = Number(node.prop.split("_")[1]);
      this.currentNode = node;
      console.log(node);
      let params = {
        bid_segment_stage_node_id: nodeId,
        expert_id: val,
      };
      //   this.title = node.stage.name + ">" + node.name;
      getNodeAudit(params).then((res) => {
        this.contenData = res.data;
        this.currentNode = res.data.node;
        this.currentNode.progress_status = 2;
        this.title =
          this.currentNode.bid_segment_stage.name + ">" + this.currentNode.name;
        this.auditVisible = true;
        this.$forceUpdate();
        console.log(this.contenData);
      });
    },
    handleCloseNode() {
      this.auditVisible = false;
    },
  },
};
</script>

<style>
/* 如果需要进一步美化表格，可在此处添加样式 */
</style>
