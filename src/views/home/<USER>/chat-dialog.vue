<template>
  <el-dialog
    :visible.sync="dialogVisible"
    width="900px"
    :before-close="handleClose"
    title="聊天"
    class="chat-dialog"
    append-to-body
  >
    <div class="chat-container">
      <!-- Contact List -->
      <div class="contact-list">
        <div
          v-for="contact in contacts"
          :key="contact.id"
          class="contact-item"
          :class="{
            active: currentContact && currentContact.id === contact.id,
          }"
          @click="selectContact(contact)"
        >
          <div class="contact-info">
            <span class="contact-name">{{ contact.name }}</span>
            <span class="last-message-time">{{
              contact.last_message_at | formatTime
            }}</span>
          </div>
          <div class="last-message-details">
            <span class="last-message">{{ contact.last_message }}</span>
            <el-badge
              :value="contact.unread_count"
              :max="99"
              v-if="contact.unread_count > 0"
              class="unread-badge"
            ></el-badge>
          </div>
        </div>
      </div>

      <!-- Chat Area -->
      <div class="chat-area">
        <div class="chat-header">
          <h3>{{ currentContact ? currentContact.name : "请选择联系人" }}</h3>
        </div>
        <div class="messages-list" ref="messagesList">
          <div
            v-for="msg in messages"
            :key="msg.id"
            class="message-item"
            :class="isMyMessage(msg) ? 'sent' : 'received'"
          >
            <!-- 头像 -->
            <img
              class="avatar"
              :src="
                isMyMessage(msg)
                  ? myAvatar
                  : currentContact
                  ? 'http://bidding.senmoio.cn/' +
                    currentContact.contact_person_logo.path
                  : ''
              "
              alt="头像"
            />
            <div class="message-bubble">
              <div class="message-content">{{ msg.message }}</div>
              <div class="message-time">{{ msg.created_at | formatTime }}</div>
            </div>
          </div>
        </div>
        <div class="message-input">
          <el-input
            type="textarea"
            :rows="3"
            placeholder=""
            v-model="newMessage"
            @keyup.enter.native="handleSendMessage"
          ></el-input>
          <el-button
            type="primary"
            @click="handleSendMessage"
            class="send-button"
            >发送</el-button
          >
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  getExpertContactList,
  getBidderContactList,
  getChatMessages,
  sendMessage,
  markAsRead,
} from "@/api/home/<USER>";

export default {
  name: "ChatDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    status: {
      // 2 for bidder, 3 for expert
      type: [String, Number],
      required: true,
    },
    segmentId: {
      type: [String, Number],
      required: true,
    },
  },
  data() {
    return {
      contacts: [],
      currentContact: null,
      messages: [],
      newMessage: "",
      messageTimer: null,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    myAvatar() {
      return this.$store.getters.avatar;
    },
  },
  watch: {
    visible: {
      handler(newVal) {
        console.log("watch visible", newVal);
        if (newVal) {
          this.fetchContacts();
        } else {
          this.clearTimer();
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleClose() {
      this.dialogVisible = false;
    },
    async fetchContacts() {
      try {
        const fetcher =
          this.status == 3 ? getExpertContactList : getBidderContactList;
        const res = await fetcher(this.segmentId);
        if (res.data) {
          this.contacts = res.data.map((c) => ({
            // Normalize contact data
            id: this.status == 3 ? c.company_id : c.expert_user_id,
            name: this.status == 3 ? c.company_name : c.expert_name,
            ...c,
          }));
        }
      } catch (error) {
        console.error("Failed to fetch contacts:", error);
      }
    },
    async selectContact(contact) {
      this.currentContact = contact;
      this.messages = [];
      this.clearTimer();
      await this.fetchMessages();
      this.startMessagePolling();

      if (contact.unread_count > 0) {
        contact.unread_count = 0;
        await markAsRead({
          status: this.status,
          segmentId: this.segmentId,
          contactId: contact.id,
        });
        this.$emit("read");
      }
    },
    async fetchMessages() {
      if (!this.currentContact) return;
      try {
        const res = await getChatMessages({
          status: this.status,
          segmentId: this.segmentId,
          contactId: this.currentContact.id,
          page: 1,
          per_page: 20,
        });
        if (res.data && res.data.messages) {
          this.messages = res.data.messages.reverse(); // Newest at the bottom
          this.scrollToBottom();
        }
      } catch (error) {
        console.error("Failed to fetch messages:", error);
      }
    },
    async handleSendMessage() {
      console.log(this.newMessage.trim(), this.currentContact);

      if (!this.newMessage.trim() || !this.currentContact) return;
      try {
        const messageContent = this.newMessage;
        this.newMessage = "";

        const res = await sendMessage({
          status: this.status,
          segmentId: this.segmentId,
          contactId: this.currentContact.id,
          message: messageContent,
        });

        if (res.data) {
          this.messages.push(res.data);
          this.scrollToBottom();
        }
      } catch (error) {
        console.error("Failed to send message:", error);
        this.newMessage = messageContent; // Restore on failure
      }
    },
    isMyMessage(msg) {
      // 'sender_type' is 'expert' or 'company'
      if (this.status == 3) {
        // I am expert
        return msg.sender_type === "expert";
      } else {
        // I am bidder/company
        return msg.sender_type === "company";
      }
    },
    startMessagePolling() {
      this.messageTimer = setInterval(() => {
        this.fetchMessages();
      }, 3000);
    },
    clearTimer() {
      if (this.messageTimer) {
        clearInterval(this.messageTimer);
        this.messageTimer = null;
      }
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const list = this.$refs.messagesList;
        if (list) {
          list.scrollTop = list.scrollHeight;
        }
      });
    },
  },
  filters: {
    formatTime(value) {
      if (!value) return "";
      return value.substring(11, 16); // HH:mm
    },
  },
  beforeDestroy() {
    this.clearTimer();
  },
};
</script>

<style scoped>
.chat-dialog .el-dialog__body {
  padding: 0;
}

.chat-container {
  display: flex;
  height: 60vh;
  border: 1px solid #ebeef5;
}

.contact-list {
  width: 300px;
  border-right: 1px solid #ebeef5;
  overflow-y: auto;
}

.contact-item {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  position: relative;
}

.contact-item:hover {
  background-color: #f5f7fa;
}

.contact-item.active {
  background-color: #ecf5ff;
}

.contact-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.contact-name {
  font-weight: bold;
}

.last-message-time {
  font-size: 12px;
  color: #909399;
}

.last-message-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-message {
  font-size: 13px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 160px;
}
.unread-badge {
  margin-left: 10px;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f5f7fa;
}

.chat-header h3 {
  margin: 0;
  text-align: center;
}

.messages-list {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.message-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 15px;
}

.message-item.sent {
  flex-direction: row-reverse;
  margin-left: auto;
}
.message-item.received {
  flex-direction: row;
  justify-content: flex-start;
}
.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  background: #eee;
  margin: 0 10px;
  flex-shrink: 0;
}
.message-bubble {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}
.message-content {
  padding: 10px 15px;
  border-radius: 8px;
  word-break: break-all;
}

.message-time {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.sent .message-content {
  background-color: #a0e959;
}
.sent .message-time {
  text-align: right;
}

.received .message-content {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
}
.received .message-time {
  text-align: left;
}

.message-input {
  border-top: 1px solid #ebeef5;
  padding: 15px;
  position: relative;
}
.message-input .el-textarea__inner {
  border: none;
  resize: none;
  padding: 0;
}
.send-button {
  position: absolute;
  right: 15px;
  bottom: 15px;
}
</style>
