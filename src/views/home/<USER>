<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: huiji
 * @LastEditTime: 2025-07-05 22:54:56
 * @FilePath: /bidding-web/src/views/home/<USER>
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <div class="hall" v-if="model == 0">
      <el-row>
        <el-card>
          <h2 style="text-align: center">
            {{ projectName }}招标项目的{{ segmentName }}标段{{
              queryForm.status == 3 ? "评标大厅" : "开标大厅"
            }}
          </h2>

          <h1
            v-if="queryForm.status != 3 && flowState !== 5"
            style="
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
            "
          >
            <span style="font-size: 20px">倒计时：</span> {{ timeStr }}
          </h1>
        </el-card>
      </el-row>
      <el-card class="table" v-if="queryForm.status == 3">
        <el-steps
          :active="flowState"
          finish-status="success"
          simple
          style="margin-top: 20px"
        >
          <el-step title="开启评标"></el-step>
          <el-step title="开启签到"></el-step>
          <el-step title="选取组长"></el-step>
          <el-step title="评标"></el-step>
          <el-step title="评标结束"></el-step>
        </el-steps>
      </el-card>

      <el-card class="table" v-else>
        <el-steps
          :active="flowState"
          finish-status="success"
          simple
          style="margin-top: 20px"
        >
          <el-step title="开启开标"></el-step>
          <el-step title="开启签到"></el-step>
          <el-step title="开启解密"></el-step>
          <el-step title="开标一览表"></el-step>
          <el-step title="开标结束"></el-step>
        </el-steps>
      </el-card>

      <el-row :gutter="10">
        <el-col :span="18">
          <el-card class="table" :body-style="{ height: height }">
            <el-table :data="tableData" border style="margin-top: 10px">
              <!-- 表格列定义 -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              ></el-table-column>
              <el-table-column
                show-overflow-tooltip
                prop="updated_at"
                label="时间"
              ></el-table-column>
              <el-table-column
                width="180"
                prop="role"
                show-overflow-tooltip
                label="角色"
              >
              </el-table-column>
              <el-table-column
                prop="company_info"
                show-overflow-tooltip
                label="公司"
              >
              </el-table-column>
              <el-table-column prop="user" show-overflow-tooltip label="操作人">
              </el-table-column>
              <el-table-column width="100" prop="state" label="状态">
              </el-table-column>
            </el-table>
            <div style="text-align: center; padding: 20px 0px">
              <el-pagination
                background
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                :page-size="queryForm.per_page"
                :current-page="queryForm.page"
                @size-change="handleChangeSize"
                @current-change="handleChangePage"
              >
              </el-pagination>
            </div> </el-card
        ></el-col>
        <div class="right-menu">
          <el-col :span="6" class="btn" v-if="queryForm.status == 1"
            ><el-card :body-style="{ height: height }" style="margin-top: 10px">
              <div style="text-align: center">
                <div class="right-btn">
                  <el-button
                    type="primary"
                    :disabled="flowState != 0"
                    @click="handleInviteFunction(1)"
                    >开启开标
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button
                    :disabled="flowState != 1"
                    type="primary"
                    @click="handleInviteFunction(2)"
                    >开启签到
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button
                    :disabled="flowState != 2"
                    type="primary"
                    @click="handleInviteFunction(3)"
                    >开启解密
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button
                    :disabled="flowState != 3"
                    type="primary"
                    @click="handleInviteFunction(4)"
                    >开启唱标评标
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button
                    :disabled="flowState != 4"
                    type="primary"
                    @click="handleInviteFunction(5)"
                    >开标结束
                  </el-button>
                </div>
              </div>
            </el-card></el-col
          >
          <el-col :span="6" class="btn" v-if="queryForm.status == 2"
            ><el-card :body-style="{ height: height }" style="margin-top: 10px">
              <div style="text-align: center">
                <div class="right-btn">
                  <el-button
                    type="primary"
                    :disabled="this.tableData[0].sign == 1 || flowState != 2"
                    @click="handleBidFunction(1, 1)"
                    >签到
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button
                    type="primary"
                    :disabled="
                      this.tableData[0].decrypt == 1 || flowState !== 3
                    "
                    @click="handleClearPwd(1, 2)"
                    >解密
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button
                    :disabled="
                      this.tableData[0].confirm == 1 || flowState !== 4
                    "
                    type="primary"
                    @click="handleBidSureFunction(1, 3)"
                    >签字
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button type="primary" @click="handleClear"
                    >问题澄清
                  </el-button>
                </div>
              </div>
            </el-card></el-col
          >
          <el-col :span="6" class="btn" v-if="queryForm.status == 3"
            ><el-card :body-style="{ height: height }" style="margin-top: 10px">
              <div style="text-align: center">
                <div class="right-btn">
                  <el-button
                    type="primary"
                    :disabled="flowState != 2"
                    @click="handleBidFunction(2, 1)"
                    >签到
                  </el-button>
                </div>
                <!--  -->
                <div class="right-btn">
                  <el-button
                    type="primary"
                    :disabled="flowState !== 3"
                    @click="handleSelectLeader"
                    >选取组长
                  </el-button>
                </div>
                <div class="right-btn">
                  <el-button
                    type="primary"
                    :disabled="flowState !== 4"
                    @click="handleAuditBid"
                    >评标
                  </el-button>
                </div>
              </div>
            </el-card></el-col
          >
        </div>
      </el-row>
      <el-dialog
        v-if="visible"
        title="解密"
        width="400px"
        :visible.sync="visible"
        :before-close="handleClose"
      >
        <el-form
          ref="form"
          :model="form"
          label-width="80px"
          status-icon
          size="mini"
        >
          <el-form-item label="解密密码:" prop="password">
            <div style="display: flex">
              <el-input type="password" v-model="form.password"></el-input>
            </div>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="visible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">提交审核</el-button>
        </span>
      </el-dialog>
    </div>
    <div class="expert" v-else-if="model == 1">
      <expert-bid
        @back="handleBack"
        @submit="handleSubmitExpert"
        :fileData="fileData"
        :bid_segment_id="queryForm.bid_segment_id"
      ></expert-bid>
    </div>
    <div v-else>
      <clear-form
        :init="isInit"
        :segmentForm="fileData"
        @back="handleBackBid"
      ></clear-form>
    </div>
    <el-dialog
      title="开标一览表"
      v-if="bidVisible"
      :visible.sync="bidVisible"
      width="60%"
      :modal="false"
    >
      <!-- 公司 联系方式  投标报价  工期 -->
      <el-table
        :data="openList.rowData"
        border
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          v-for="item of filteredOpenListTitle"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
        />
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="bidVisible = false">取 消</el-button>
        <el-button type="primary" v-if="flowState === 3" @click="handleOpen"
          >确认</el-button
        >
        <el-button type="primary" v-else @click="handleSubmitSure"
          >确认签字</el-button
        >
      </span>
    </el-dialog>

    <el-dialog
      title="二次报价"
      v-if="priceVisible"
      :visible.sync="priceVisible"
      width="86%"
      :modal="false"
    >
      <!-- 公司 联系方式  投标报价  工期 -->
      <el-table
        :data="openList.rowData"
        border
        style="width: 100%; margin-top: 20px"
      >
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          v-for="item of filteredOpenListTitle"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          style="wdith: 210px"
        >
          <template slot-scope="scope">
            <div
              v-if="
                item.prop.includes('field_') &&
                item.label !== '投标总价（￥ 元）'
              "
            >
              <el-input v-model="scope.row[item.prop]"></el-input>
            </div>
            <div v-else>{{ scope.row[item.prop] }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="bid_price" label="二次报价(元)">
          <template slot-scope="scope">
            <div
              style="display: flex"
              v-if="
                openList.bidding_project_purchase_method == 1 ||
                openList.bidding_project_purchase_method == 2
              "
            >
              <el-input-number
                :min="0"
                v-model="scope.row.bid_price"
              ></el-input-number>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSavePrice()">提交</el-button>
        <el-button @click="priceVisible = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="handleSubmitSure">确认签字</el-button> -->
      </span>
    </el-dialog>
    <el-dialog title="签到" :visible.sync="okVisible" width="30%">
      <el-radio-group v-model="selection_status">
        <el-radio label="1">无需回避</el-radio>
        <el-radio label="2">需要回避</el-radio>
      </el-radio-group>
      <span slot="footer" class="dialog-footer">
        <el-button @click="okVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitOk">确认签到</el-button>
      </span>
    </el-dialog>
    <el-dialog title="选取组长" :visible.sync="leaderVisible" width="20%">
      <div v-for="item in expertList" :key="item" style="height: 40px">
        <el-radio v-model="groupId" :label="item.id">{{ item.name }}</el-radio>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="leaderVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitLeader">提交</el-button>
      </span>
    </el-dialog>

    <!-- Chat Feature -->
    <div
      class="chat-entry-point"
      v-if="bidding_project_purchase_method == 1"
      @click="handleOpenChat"
    >
      <el-badge :value="unreadCount" :max="99" :hidden="unreadCount === 0">
        <i class="el-icon-chat-dot-round"></i>
      </el-badge>
    </div>

    <chat-dialog
      v-if="chatVisible"
      :visible.sync="chatVisible"
      :status="queryForm.status"
      :segment-id="queryForm.bid_segment_id"
      @read="handleReadMessage"
    ></chat-dialog>

    <el-dialog
      :visible.sync="countdownVisible"
      width="800px"
      :close-on-click-modal="false"
      :show-close="false"
      :close-on-press-escape="false"
      :modal="true"
      :before-close="() => {}"
    >
      <div style="text-align: center; font-size: 50px; line-height: 1">
        距离开标时间剩余
      </div>
      <div style="text-align: center; font-size: 120px; line-height: 4">
        {{ countdownStr }}
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getHallList,
  inviteHallFunction,
  bidHallFunction,
  getSegmentFiles,
  expertAuditBid,
  getOpenBidList,
  twoSubmission,
  expertSign,
  getExpertList,
  submitLeader,
} from "@/api/home/<USER>";
import { getUnreadCount } from "@/api/home/<USER>";
import ExpertBid from "./expert-bid.vue";
import ClearForm from "./modules/clear-form.vue";
import ChatDialog from "./modules/chat-dialog.vue";

export default {
  components: {
    ExpertBid,
    ClearForm,
    ChatDialog,
  },
  props: {
    bidData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      priceVisible: false,
      groupId: 1,
      okVisible: false,
      selection_status: "1",
      bidVisible: false,
      openList: [],
      visible: false,
      fileData: {},
      total: 0,
      model: 0,
      flowState: 0,
      projectName: "",
      segmentName: "",
      intimer: null,
      queryForm: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
        bid_segment_id: null,
        status: null, //1招标人 2投标人 3专家
      },
      form: {
        password: "",
      },
      priceForm: {
        bid_price: 0,
      },
      height: 0,
      tableData: [], // 过滤后的数据
      isInit: false,
      leaderVisible: false,
      expertList: [],
      priceStatus: null,
      timeStr: "",
      localTimer: null, // 添加本地计时器
      localTimeSeconds: 0, // 添加本地倒计时秒数
      chatVisible: false,
      unreadCount: 0,
      chatTimer: null,
      countdownVisible: false,
      countdownStr: "00:00:00",
      countdownSeconds: 0,
      countdownTimer: null,
      bidding_project_purchase_method: 1,
    };
  },
  computed: {
    filteredOpenListTitle() {
      if (this.openList && this.openList.title) {
        return this.openList.title.filter((item) => item.prop !== "bid_price");
      }
      return [];
    },
  },
  methods: {
    handleOpenChat() {
      this.chatVisible = true;
    },
    fetchUnreadCount() {
      if (!this.queryForm.bid_segment_id) return;
      if (this.queryForm.status != 2 && this.queryForm.status != 3) {
        if (this.chatTimer) clearInterval(this.chatTimer);
        return;
      }
      getUnreadCount(this.queryForm.bid_segment_id)
        .then((res) => {
          this.unreadCount = res.data.unread_count;
        })
        .catch((err) => {
          console.error("Failed to fetch unread count:", err);
        });
    },
    handleReadMessage() {
      this.fetchUnreadCount();
    },
    handleSelectLeader() {
      this.leaderVisible = true;
      let params = {
        bid_segment_id: this.queryForm.bid_segment_id,
      };
      getExpertList(params).then((res) => {
        this.expertList = res.data;
      });
    },
    handleSubmitLeader() {
      let params = {
        bid_segment_id: this.queryForm.bid_segment_id,
        bidding_project_id: this.queryForm.bidding_project_id,
        leader_id: this.groupId,
      };
      submitLeader(params).then((res) => {
        this.leaderVisible = false;
        this.message(res.status_code, res.message);
      });
    },
    handleBackBid() {
      this.model = 0;
    },
    handleClear() {
      console.log(this.fileData);

      this.model = 2;
      let params = {
        bid_segment_id: this.queryForm.bid_segment_id,
      };
      getSegmentFiles(params).then((res) => {
        this.fileData = res.data;
        this.fileData.bid_segment_id = this.queryForm.bid_segment_id;
        console.log(res);
      });
    },
    handleSavePrice() {
      let params = Object.assign(
        { bid_segment_id: this.queryForm.bid_segment_id },
        { ...this.openList.rowData[0] }
      );
      twoSubmission(params).then((res) => {
        this.priceVisible = false;
        // this.message(res.status_code, res.message);
        this.handleViewList();
        let message = res.message;
        if (res.status_code == 200) {
          this.$message({
            message,
            type: "success",
          });
        } else {
          this.$message({
            message,
            type: "error",
          });
        }
      });
    },
    handleBack() {
      this.model = 0;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getHallList(this.queryForm).then((res) => {
        this.flowState = res.data.bid_segment;
        this.projectName = res.data.bidding_project_name;
        this.segmentName = res.data.bid_segment_name;
        this.tableData = res.data.data;
        this.total = res.data.total;
        this.priceStatus = res.data.two_price_type;
        this.timeStr = res.data.remaining_formatted;
        this.bidding_project_purchase_method =
          res.data.bidding_project_purchase_method;
        // 解析服务器返回的格式化时间,转换为秒数
        if (res.data.remaining_formatted) {
          const [minutes, seconds] = res.data.remaining_formatted
            .split(":")
            .map(Number);
          this.localTimeSeconds = minutes * 60 + seconds;
          this.formatTime(); // 立即格式化一次时间显示

          // 清除旧的计时器
          if (this.localTimer) {
            clearInterval(this.localTimer);
          }

          // 启动本地计时器
          this.startLocalTimer();
        }

        if (this.priceStatus == 1 && this.priceVisible == false) {
          this.priceVisible = true;
          this.handleViewList();
        }

        // 倒计时弹窗逻辑
        if (
          res.data.is_bid_opening_time === false &&
          this.queryForm.status == 1
        ) {
          this.countdownVisible = true;
          this.resetCountdown(res.data.bid_opening_countdown);
        } else {
          this.countdownVisible = false;
          this.clearCountdownTimer();
        }
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleClearPwd() {
      this.visible = true;
      this.form.password = "";
    },
    setRegText(val) {
      if (val == 2) {
        return "未开始";
      } else if (val == 3 || val == 4) {
        return "进行中";
      } else {
        return "已结束";
      }
    },
    getHeight() {
      this.height = window.innerHeight - 280 + "px";
    },
    handleOpen() {
      let params = {
        status: 4,
        bid_segment_id: this.queryForm.bid_segment_id,
      };
      inviteHallFunction(params).then((res) => {
        this.bidVisible = false;
        this.message(res.status_code, res.message);
      });
    },
    handleInviteFunction(val) {
      let text = "";
      switch (val) {
        case 1:
          text = "开启开标";
          break;
        case 2:
          text = "开启签到";
          break;
        case 3:
          text = "开启解密";
          break;
        case 4:
          text = "开启唱标评标";
          break;
        case 5:
          text = "开标结束";
          break;
        default:
          break;
      }
      if (val === 4) {
        this.bidVisible = true;
        this.handleViewList();
      } else {
        this.$confirm(`确定要${text}操作么?`, "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            let params = {
              status: val,
              bid_segment_id: this.queryForm.bid_segment_id,
            };
            inviteHallFunction(params).then((res) => {
              this.message(res.status_code, res.message);
            });
          })
          .catch((err) => {
            console.error(err);
          });
      }
    },
    handleBidFunction(role, val) {
      let text = "";
      let oktext = "确定";
      if (role == 1) {
        switch (val) {
          case 1:
            text = "签到";
            break;
          default:
            break;
        }
      } else {
        this.okVisible = true;
        return;
      }
      this.$confirm(`确定要${text}操作么?`, "提示", {
        confirmButtonText: `${oktext}`,
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let params = {
            status: val,
            bid_segment_id: this.queryForm.bid_segment_id,
            bidding_project_id: this.queryForm.bidding_project_id,
            role_status: role,
          };
          bidHallFunction(params).then((res) => {
            this.message(res.status_code, res.message);
          });
        })
        .catch((err) => {
          console.error(err);
        });
    },
    handleBidSureFunction() {
      this.bidVisible = true;
      this.handleViewList();
    },
    handleSubmitOk() {
      let params = {
        status: 1,
        bid_segment_id: this.queryForm.bid_segment_id,
        bidding_project_id: this.queryForm.bidding_project_id,
        role_status: 2,
        selection_status: this.selection_status,
      };
      bidHallFunction(params).then((res) => {
        this.message(res.status_code, res.message);
        if (this.selection_status === "2") {
          window.close();
        }
      });
    },
    handleSubmitSure() {
      let params = {
        status: 3,
        bid_segment_id: this.queryForm.bid_segment_id,
        bidding_project_id: this.queryForm.bidding_project_id,
        role_status: 1,
      };
      bidHallFunction(params).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleViewList() {
      let params = {
        bid_segment_id: this.queryForm.bid_segment_id,
      };
      getOpenBidList(params).then((res) => {
        this.openList = res.data;
        console.log(res);
      });
    },
    handleSubmit() {
      let params = {
        status: 2,
        bid_segment_id: this.queryForm.bid_segment_id,
        bidding_project_id: this.queryForm.bidding_project_id,
        role_status: 1,
        password: this.form.password,
      };
      bidHallFunction(params).then((res) => {
        this.message(res.status_code, res.message);
        this.visible = false;
      });
    },
    handleAuditBid() {
      this.model = 1;
      let params = {
        bid_segment_id: this.queryForm.bid_segment_id,
      };
      getSegmentFiles(params).then((res) => {
        this.fileData = res.data;
        console.log(res);
      });
    },
    handleSubmitExpert(data) {
      let params = {
        bidding_project_id: this.queryForm.bidding_project_id,
        bid_segment_id: this.queryForm.bid_segment_id,
        // score: data,
      };
      expertAuditBid(params).then((res) => {
        this.message(res.status_code, res.message);
        this.model = 0;
      });
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
        this.bidVisible = false;
        this.okVisible = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    // 格式化时间显示 - 修改为MM:SS格式
    formatTime() {
      const minutes = Math.floor(this.localTimeSeconds / 60);
      const seconds = this.localTimeSeconds % 60;
      this.timeStr = `${String(minutes).padStart(2, "0")}:${String(
        seconds
      ).padStart(2, "0")}`;
    },
    // 本地计时器方法保持不变
    startLocalTimer() {
      this.localTimer = setInterval(() => {
        if (this.localTimeSeconds > 0) {
          this.localTimeSeconds--;
          this.formatTime();
        } else {
          clearInterval(this.localTimer);
        }
      }, 1000);
    },
    resetCountdown(timeStr) {
      // timeStr格式为"HH:mm:ss"
      const [h, m, s] = timeStr.split(":").map(Number);
      this.countdownSeconds = h * 3600 + m * 60 + s;
      this.updateCountdownStr();
      this.clearCountdownTimer();
      this.countdownTimer = setInterval(() => {
        if (this.countdownSeconds > 0) {
          this.countdownSeconds--;
          this.updateCountdownStr();
        } else {
          this.clearCountdownTimer();
        }
      }, 1000);
    },
    updateCountdownStr() {
      const h = String(Math.floor(this.countdownSeconds / 3600)).padStart(
        2,
        "0"
      );
      const m = String(
        Math.floor((this.countdownSeconds % 3600) / 60)
      ).padStart(2, "0");
      const s = String(this.countdownSeconds % 60).padStart(2, "0");
      this.countdownStr = `${h}:${m}:${s}`;
    },
    clearCountdownTimer() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer);
        this.countdownTimer = null;
      }
    },
  },

  created() {
    this.queryForm.status = this.$route.query.model;
    this.queryForm.bidding_project_id = this.$route.query.projectId;
    this.queryForm.bid_segment_id = this.$route.query.segmentId;
    // console.log(model, bidding_project_id, segmentId);
    this.getList();
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
    this.intimer = setInterval(() => {
      this.getList();
    }, 1000 * 10);
    this.chatTimer = setInterval(() => {
      this.fetchUnreadCount();
    }, 1000 * 10);
  },

  beforeDestroy() {
    // 组件销毁时清除计时器
    if (this.localTimer) {
      clearInterval(this.localTimer);
    }
    if (this.intimer) {
      clearInterval(this.intimer);
    }
    if (this.chatTimer) {
      clearInterval(this.chatTimer);
    }
    this.clearCountdownTimer();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
  position: relative;
}
.right-menu >>> .el-button {
  width: 180px;
  height: 80px;
}
.btn > .el-card {
  background-color: transparent;
}
.right-btn {
  margin-top: 30px;
}
.table {
  margin-top: 10px;
}
.chat-entry-point {
  position: fixed;
  right: 40px;
  bottom: 100px;
  width: 60px;
  height: 60px;
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 2000;
}
</style>
