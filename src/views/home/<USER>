<template>
  <div>
    <el-dialog
      :title="title"
      width="80%"
      :visible.sync="visible"
      :modal="false"
      :before-close="handleClose"
    >
      <el-table :data="tableData.clauses" border>
        <!-- 第一列：序号 -->
        <el-table-column label="序号" align="center" width="80" type="index">
        </el-table-column>

        <!-- 第二列：评审项 -->
        <el-table-column label="评审项" align="left" width="200">
          <template slot-scope="scope">
            <el-link @click="handleView(scope.row)">{{
              scope.row.name + "(0~" + scope.row.score_range + ")"
            }}</el-link>
          </template>
        </el-table-column>

        <!-- 动态生成公司列 -->
        <el-table-column
          v-for="(company, index) in tableData.companyInfos"
          :key="index"
          :label="company.company_name"
          align="center"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-input-number
              :key="index"
              :disabled="node.progress_status === 2"
              :min="0"
              :max="tableData.clauses[scope.$index].score_range"
              @input="handleChange($event, scope.$index, index)"
              v-model="tableData.clauses[scope.$index].scores[index]"
              size="small"
            >
            </el-input-number>
          </template>
        </el-table-column>
      </el-table>
      <el-table :data="table" border style="margin-top: 10px">
        <el-table-column label="总结果" align="left" width="280">
          <template slot-scope="scope">
            {{ scope.row.title }}
          </template>
        </el-table-column>
        <!-- 动态生成公司列 -->
        <el-table-column
          v-for="(company, index) in tableData.companyInfos"
          :key="index"
          :label="company.company_name"
          align="center"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-input-number
              :disabled="node.progress_status === 2"
              size="small"
              v-if="scope.$index == 0"
              v-model="company.final_pass_status"
            >
            </el-input-number>

            <el-input
              v-else
              :disabled="node.progress_status === 2"
              v-model="company.final_pass_desc"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <span
        slot="footer"
        class="dialog-footer"
        v-if="node.progress_status === 2"
      >
        <el-button @click="handleClose">关 闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer" v-else>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <!-- <el-button type="primary" @click="handleSubmit">提交</el-button> -->
      </span>
    </el-dialog>
    <el-dialog title="查看评审项" :visible.sync="viewShow" width="40%">
      <h3>条款名称：{{ data.name }}</h3>
      <h3>条款描述：{{ data.description }}</h3>
      <h3>分值：{{ data.score_range }}</h3>
      <h3>是否扣分项：{{ data.is_deduction === 0 ? "否" : "是" }}</h3>
      <h3>是否客观项：{{ data.is_objective === 0 ? "否" : "是" }}</h3>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewShow = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: () => "",
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    companies: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    node: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      viewShow: false,
      data: {},
      table: [
        {
          title: "结论",
        },
        {
          title: "说明",
        },
      ],
    };
  },
  created() {},
  watch: {
    visible: {
      handler(val) {
        if (!val) {
          return;
        }
        for (let i = 0; i < this.tableData.companyInfos.length; i++) {
          let result = 0;
          this.tableData.clauses.forEach((item) => {
            result += item.scores[i];
            this.tableData.companyInfos[i].final_pass_status =
              result.toFixed(2);
            this.$forceUpdate();
          });
        }
      },
    },
  },
  methods: {
    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleChange(val, row, index) {
      console.log("testse");
      let result = 0;
      this.tableData.clauses.forEach((item) => {
        result += item.scores[index];
        this.tableData.companyInfos[index].final_pass_status =
          result.toFixed(2);
        this.$forceUpdate();
        console.log(result);
      });

      console.log(val, row, index);
    },
    handleSubmit() {
      this.$emit("submit", this.tableData);
      // 打印填写的结果
      console.log("填写结果：", this.tableData);
    },
    handleSave() {
      this.$emit("save", this.tableData);
      // 打印填写的结果
      console.log("填写结果：", this.tableData);
    },
    handleView(data) {
      console.log(data);
      this.viewShow = true;
      this.data = data;
    },
  },
};
</script>

<style scoped>
/* 自定义样式可根据需求调整 */
</style>
