<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: huiji
 * @LastEditTime: 2025-07-05 21:48:48
 * @FilePath: /bidding-web/src/views/home/<USER>
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <div v-if="mode === 1">
      <el-card>
        <div slot="header">
          <span>项目名称:{{ projectName }}</span>
        </div>
        <div>
          <div style="padding: 0px 20%">
            <el-row
              type="flex"
              justify="space-between"
              align="middle"
              style="margin-bottom: 20px"
            >
              <el-col :span="12">
                <div>
                  <span>招标信息: </span>
                  <el-link
                    type="primary"
                    style="font-size: 16px"
                    target="_blank"
                    :href="fileUrl"
                    >招标文件下载</el-link
                  >
                </div>
              </el-col>
              <el-col :span="12" style="text-align: left">
                <div>
                  <span>开标信息:</span>

                  <el-button
                    type="text"
                    style="font-size: 16px"
                    @click="handleViewList"
                    >开标一览表</el-button
                  >

                  <el-button
                    v-if="fileData.two_price_type === 1"
                    type="text"
                    style="font-size: 16px"
                    @click="handleViewTwoList"
                    >二次报价表</el-button
                  >
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <div>
                  <span>温馨提示:</span>
                  <el-button
                    type="text"
                    style="font-size: 16px"
                    @click="handleTip"
                    >查看</el-button
                  >
                </div></el-col
              >
              <el-col :span="12" style="text-align: left">
                <div>
                  <span>评标办法:</span>
                  <el-button
                    type="text"
                    style="font-size: 16px"
                    @click="handleShowRule"
                    >查看</el-button
                  >
                </div></el-col
              >
            </el-row>
          </div>
          <el-table
            :data="tableData"
            border
            style="width: 100%; margin-top: 20px"
          >
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="name" label="投标人"></el-table-column>
            <el-table-column label="文件">
              <template slot-scope="scope">
                <el-link
                  type="primary"
                  v-for="fileUrl in scope.row.urls"
                  :href="fileUrl.path"
                  :key="fileUrl.name"
                >
                  {{ fileUrl.name }}</el-link
                ><br />
              </template>
            </el-table-column>
            <el-table-column
              width="200"
              prop="is_decrypt"
              label="是否已解密"
              align="center"
            >
              <template slot-scope="scope">
                {{ scope.row.is_decrypt === 1 ? "已解密" : "未解密" }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <el-card style="margin-top: 10px">
        <div slot="header">
          <span>评标管理</span>
        </div>
        <div class="legend">
          <div class="legend-item">
            <div class="legend-color completed"></div>
            <span>已完成</span>
          </div>
          <div class="legend-item">
            <div class="legend-color in-progress"></div>
            <span>进行中</span>
          </div>
          <div class="legend-item">
            <div class="legend-color not-started"></div>
            <span>未开始</span>
          </div>
          <el-button
            type="primary"
            size="small"
            @click="handleClear"
            style="margin-left: 20px"
            >澄清记录</el-button
          >
          <div style="margin-left: auto">
            <el-button
              type="primary"
              size="small"
              @click="handleExportChatRecords"
            >
              聊天记录导出
            </el-button>

            <el-button
              type="primary"
              size="small"
              @click="handleExportExpertScores"
            >
              评标详情导出
            </el-button>

            <el-button type="primary" size="small" @click="showReport"
              >评标报告</el-button
            >
          </div>
        </div>
        <audit-result :vis :tableData="aduitData"></audit-result>
        <!-- <div style="display: flex">
          <div
            v-for="item in nodeList"
            :key="item"
            :style="{ width: setWidth() }"
            style="border: 1px solid #dddfe5; text-align: center"
          >
            <div>
              <p>{{ item.stage.name }}</p>
              <p :class="setClass(item)">{{ item.name }}</p>
            </div>
            <el-divider></el-divider>
            <div style="margin-bottom: 15px">
              <el-button
                :disabled="item.progress_status === 0"
                :class="setClass(item)"
                @click="handleAudit(item)"
                >{{
                  item.progress_status === 2 ? "已评审" : "待评审"
                }}</el-button
              >
            </div>
          </div>
        </div> -->
        <!-- <el-table :data="auditTable" border="" style="width: 100%">
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="name" label="投标人" width="180">
        </el-table-column>
        <el-table-column label="商务标">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.business_score"
              @blur="handleInputBlur(scope.row, 'business_score')"
              :maxlength="3"
              show-input-rules
              placeholder="请输入1-100的数字"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="技术标">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.technology_score"
              @blur="handleInputBlur(scope.row, 'technology_score')"
              :maxlength="3"
              show-input-rules
              placeholder="请输入1-100的数字"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column label="价格标">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.price_score"
              @blur="handleInputBlur(scope.row, 'price_score')"
              :maxlength="3"
              show-input-rules
              placeholder="请输入1-100的数字"
            >
            </el-input>
          </template>
        </el-table-column>
      </el-table> -->
      </el-card>
      <div style="text-align: center; margin-top: 10px">
        <el-button @click="handleBack">返回</el-button>
        <el-button type="primary" @click="handleViewLog"
          >查看评标记录</el-button
        >
      </div>

      <el-dialog
        title="二次报价表"
        :visible.sync="twoVisible"
        :modal="false"
        width="80%"
      >
        <el-button type="primary" @click="handleDownloadTwoPrice">
          下载</el-button
        >
        <el-table
          :data="twoPriceList.rowData"
          border
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column
            type="index"
            label="序号"
            width="50"
            align="center"
          ></el-table-column>

          <el-table-column
            v-for="item of twoPriceList.title"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
          />
        </el-table>
      </el-dialog>

      <el-dialog
        title="开标一览表"
        :visible.sync="visible"
        :modal="false"
        width="80%"
      >
        <!-- 公司 联系方式  投标报价  工期 -->
        <el-button type="primary" @click="handleDownload3"> 下载</el-button>
        <el-table
          :data="openList.rowData"
          border
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column
            type="index"
            label="序号"
            width="50"
            align="center"
          ></el-table-column>

          <el-table-column
            v-for="item of openList.title"
            :key="item.prop"
            :prop="item.prop"
            :label="item.label"
          />
          <!-- <el-table-column prop="company_name" label="公司"></el-table-column>
          <el-table-column prop="phone" label="联系方式"> </el-table-column>
          <el-table-column prop="bid_price" label="投标报价"> </el-table-column>
          <el-table-column prop="duration" label=" 工期"> </el-table-column>
          <el-table-column
            prop="bid_two_price"
            label="二次报价"
          ></el-table-column> -->
        </el-table>
      </el-dialog>
      <el-dialog
        title="评标记录"
        width="90%"
        :visible.sync="bidLog"
        :modal="false"
        :before-close="handleCloseLog"
      >
        <div>
          <h3>
            评审信息
            <el-button type="primary" @click="handleDownload1(1)">
              下载</el-button
            >
          </h3>
          <el-divider></el-divider>
          <el-table :data="tableData1" border>
            <el-table-column
              v-for="(item, index) in this.columns1"
              :key="index"
              :label="item"
              align="center"
              min-width="100"
            >
              <template slot-scope="scope">
                <span v-if="index > 1">{{
                  setAuditText(tableData1[scope.$index][index])
                }}</span>
                <span v-else>{{ tableData1[scope.$index][index] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="margin-top: 10px">
          <h3>
            排名信息
            <el-button type="primary" @click="handleDownload1(2)">
              下载</el-button
            >
          </h3>
          <el-divider></el-divider>
          <el-table :data="tableData2" border>
            <el-table-column
              v-for="(item, index) in this.columns2"
              :key="index"
              :label="item"
              align="center"
              min-width="100"
            >
              <template slot-scope="scope">
                <span>{{ tableData2[scope.$index][index] }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: center">
          <el-button @click="bidLog = false">关 闭</el-button>
        </div>
      </el-dialog>

      <audit-bid
        v-if="currentNode.type == 1"
        :visible="auditVisible"
        :tableData="contenData"
        :node="currentNode"
        :title="title"
        @close="handleCloseNode"
        @save="handleSaveAudit"
        @submit="handleSubmitAudit"
      ></audit-bid>
      <audit-score
        v-else
        :title="title"
        :visible="auditVisible"
        :tableData="contenData"
        :node="currentNode"
        @close="handleCloseNode"
        @save="handleSaveAudit"
        @submit="handleSubmitAudit"
      ></audit-score>
      <audit-rule
        :visible="bidRule"
        :stageList="stageList"
        :infoForm="fileData"
        :ruleForm="ruleForm"
        @close="handleCloseRule"
      ></audit-rule>
    </div>
    <div v-else>
      <clear-form
        :init="isInit"
        :segmentForm="fileData"
        @back="handleBackBid"
      ></clear-form>
    </div>
    <el-dialog
      :modal="false"
      title="评标报告"
      :visible.sync="visibleReport"
      width="50%"
    >
      <el-upload
        v-model="reportForm.file_paths"
        :show-file-list="false"
        ref="upload1"
        action="https://bidding.senmoio.cn/api/file"
        :headers="header"
        :accept="acceptTypes.join(',')"
        :before-upload="beforeUpload"
        :on-success="handleSuccess1"
      >
        <el-button slot="trigger" size="small" type="primary"
          >选取文件</el-button
        >
        <div slot="tip" class="el-upload__tip">
          可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
        </div>
      </el-upload>
      <el-table :data="reportList" style="width: 100%">
        <el-table-column prop="file_paths[0].name" label="文件名称">
        </el-table-column>
        <el-table-column prop="created_at" label="上传时间" width="180">
        </el-table-column>
        <el-table-column prop="address" label="操作" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="handleDownload(scope.row)"
              >下载</el-button
            >
            <el-button
              type="danger"
              size="mini"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="visibleReport = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBidRule,
  getReportList,
  uploadReportFile,
  deleteReportFile,
  exportAllExpertScores,
  exportChatRecords,
} from "@/api/bid-manage/bid-project";
import {
  getOpenBidList,
  getTwoPriceList,
  getBidAuditLog,
  getBidAuditResult,
  getBidAuditLogDownload,
  getOpenBidListDownload,
  getTwoPriceListDownload,
} from "@/api/home/<USER>";

import ClearForm from "./modules/clear-form.vue";
import AuditResult from "./modules/audit-result.vue";
import {
  getStageNodes,
  getNodeAudit,
  updateAuditResult,
  getStageList,
} from "@/api/bid-manage/stage";
import AuditBid from "./audit-bid.vue";
import AuditScore from "./audit-score.vue";
import AuditRule from "@/components/StatusInfo/audit-rule.vue";
import { getToken } from "@/utils/auth";
export default {
  components: {
    AuditBid,
    AuditScore,
    AuditRule,
    ClearForm,
    AuditResult,
  },
  props: {
    fileData: {
      type: Object,
      default: () => {},
    },
    bid_segment_id: {
      type: Number,
      default: () => 0,
    },
  },
  data() {
    return {
      header: {
        Authorization: "Bearer " + getToken(),
      },
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      reportList: [],
      reportForm: {
        file_paths: [],
      },
      visibleReport: false,
      // 表格数据
      tableData1: [
        { name: "张三", age: 25, address: "北京市" },
        { name: "李四", age: 30, address: "上海市" },
        { name: "王五", age: 35, address: "广州市" },
      ],
      tableData2: [
        { name: "张三", age: 25, address: "北京市" },
        { name: "李四", age: 30, address: "上海市" },
        { name: "王五", age: 35, address: "广州市" },
      ],
      // 列配置
      columns1: [
        { prop: "name", label: "姓名", width: "120", align: "center" },
        { prop: "age", label: "年龄", width: "80", align: "center" },
        { prop: "address", label: "地址", align: "left" },
      ],
      columns2: [
        { prop: "name", label: "姓名", width: "120", align: "center" },
        { prop: "age", label: "年龄", width: "80", align: "center" },
        { prop: "address", label: "地址", align: "left" },
      ],
      bidLog: false,
      isInit: false,
      mode: 1,
      title: "",
      infoForm: {},
      currentNode: {},
      contenData: [],
      nodeList: [],
      auditVisible: false,
      ruleForm: {
        bid_evaluation_rules: 1,
      },
      bidRule: false,
      ruleFileList: [],
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      projectName: "",
      visible: false,
      twoVisible: false,
      openList: [],
      twoPriceList: [],
      fileUrl: "",
      auditTable: [],
      fields: [
        {
          label: "招标项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      stageList: [],
      aduitData: {},
    };
  },
  watch: {
    fileData: {
      handler(val) {
        if (val) {
          console.log("test");

          this.projectName = val.bidding_project_name;
          this.fileUrl = `http://bidding.senmoio.cn${val.attachments[0].path}`;
          let fileTable = val.bid_document_submissions;
          this.tableData = [];
          this.auditTable = [];
          fileTable.forEach((item) => {
            console.log("item", item);

            let urls = [];
            item.attachments.forEach((files) => {
              let path = `http://bidding.senmoio.cn${files.path}`;
              let obj = {
                path,
                name: files.name,
              };
              urls.push(obj);
            });
            let obj = {
              name: item.name,
              urls,
              is_decrypt: item.is_decrypt,
            };
            this.tableData.push(obj);
            let auditObj = {
              id: item.id,
              name: item.name,
              business_score: "",
              technology_score: "",
              price_score: "",
              is_decrypt: item.is_decrypt,
            };
            this.auditTable.push(auditObj);
          });
        }
      },
      deep: true,
    },
  },
  methods: {
    handleDownload(data) {
      console.log(data);
      let url = `http://bidding.senmoio.cn/${data.file_paths[0].path}`;
      window.open(url, "_blank");
    },

    handleDelete(data) {
      this.$confirm("确定要删除该条记录么?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteReportFile(data.id).then((res) => {
            if (res.status_code === 200) {
              this.$message({
                type: "success",
                message: "操作成功!",
              });
              getReportList(this.bid_segment_id).then((res) => {
                console.log(res);
                this.reportList = res.data;
              });
            } else {
              this.$message({
                type: "error",
                message: "操作失败",
              });
            }
          });
        })
        .catch((err) => {
          console.error(err);
        });
    },
    showReport() {
      this.visibleReport = true;
      getReportList(this.bid_segment_id).then((res) => {
        console.log(res);
        this.reportList = res.data;
      });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      // const isLt10M = file.size / 1024 / 1024 < 100;
      // if (!isLt10M) {
      //   this.$message.error("上传文件大小不能超过 100MB!");
      //   return false;
      // }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.reportForm.file_paths.push(res.data);
      console.log(res, file, fileList);

      this.reportForm.bid_segment_id = this.bid_segment_id;
      uploadReportFile(this.reportForm).then((res) => {
        console.log(res);
        getReportList(this.bid_segment_id).then((res) => {
          console.log(res);
          this.reportList = res.data;
        });
      });
    },
    handleRemove1(file, fileList) {
      // let index = this.form.attachments.filter((item) => {
      //   return item.path == file.url;
      // });
      // if (index != -1) {
      //   this.form.attachments.splice(index, 1);
      // }
    },
    setAuditText(val) {
      if (val === 1) {
        return "不通过";
      } else if (val === 2) {
        return "通过";
      } else {
        return "";
      }
    },
    handleClear() {
      console.log(this.fileData);
      this.fileData.bid_segment_id = this.bid_segment_id;
      this.mode = 2;
    },
    handleCloseNode() {
      this.auditVisible = false;
    },
    setClass(data) {
      if (data.progress_status === 0) {
        return "not-start";
      } else if (data.progress_status === 1) {
        return "progress";
      } else {
        return "end";
      }
    },
    handleAudit(node) {
      this.currentNode = node;
      console.log(node);
      let params = {
        bid_segment_stage_node_id: node.id,
      };
      this.title = node.stage.name + ">" + node.name;
      getNodeAudit(params).then((res) => {
        this.contenData = res.data;
        this.auditVisible = true;
        console.log(this.contenData);
      });
    },
    setWidth() {
      let width = (1 / this.nodeList.length) * 100;
      console.log(width);
      return width + "%";
    },
    handleViewLog() {
      this.bidLog = true;
      let params = {
        bid_segment_id: this.bid_segment_id,
      };
      getBidAuditLog(params).then((res) => {
        this.columns1 = res.data["pingshen"].title;
        this.tableData1 = res.data["pingshen"].data;
        this.columns2 = res.data["scoreSort"].title;
        this.tableData2 = res.data["scoreSort"].lastData;
      });
    },
    handleDownload1(type) {
      let params = {
        bid_segment_id: this.bid_segment_id,
        download: type,
      };
      let fileName = "";
      if (type == 1) {
        fileName = "评审信息.xls";
      } else {
        fileName = "排名信息.xls";
      }
      getBidAuditLogDownload(params)
        .then((response) => {
          console.log(response);
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", fileName); // 指定下载文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); // 下载后移除元素
        })
        .catch((error) => {
          console.log(error);
        });
    },
    handleDownload3() {
      let params = {
        bid_segment_id: this.bid_segment_id,
        download: 1,
      };
      getOpenBidListDownload(params)
        .then((response) => {
          console.log(response);
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "开标一览表.xls"); // 指定下载文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); // 下载后移除元素
        })
        .catch((error) => {
          console.log(error);
        });
    },

    handleDownloadTwoPrice() {
      let params = {
        bid_segment_id: this.bid_segment_id,
        download: 1,
      };
      getTwoPriceListDownload(params)
        .then((response) => {
          console.log(response);
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "二次报价表.xls"); // 指定下载文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); // 下载后移除元素
        })
        .catch((error) => {
          console.log(error);
        });
    },

    handleCloseLog() {
      this.bidLog = false;
    },
    handleCloseRule() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.bidRule = false;
          //   done();
        })
        .catch((_) => {});
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    // handleShowRule() {
    //   this.bidRule = true;
    //   getBidRule(this.bid_segment_id).then((res) => {
    //     this.ruleForm = { ...res.data };
    //     if (this.ruleForm.bid_clause_attachment == null) {
    //       this.ruleForm.bid_clause_attachment = [];
    //     } else {
    //       this.ruleFileList = [];
    //       this.ruleForm.bid_clause_attachment.forEach((item) => {
    //         let obj = {
    //           name: item.name,
    //           url: item.path,
    //         };
    //         this.ruleFileList.push(obj);
    //       });
    //     }
    //     console.log(res);
    //   });
    // },
    handleShowRule() {
      console.log(this.fileData);

      this.bidRule = true;
      this.getStageList();
      getBidRule(this.bid_segment_id).then((res) => {
        this.ruleForm = { ...res.data };
      });
    },
    getStageList() {
      let params = {
        bid_segment_id: this.bid_segment_id,
      };
      getStageList(params).then((res) => {
        this.stageList = res.data;
      });
    },
    handleBack() {
      this.$emit("back");
    },
    handleBackBid() {
      this.mode = 1;
    },
    handleViewList() {
      let params = {
        bid_segment_id: this.bid_segment_id,
      };
      this.visible = true;
      getOpenBidList(params).then((res) => {
        this.openList = res.data;
        console.log(res);
      });
    },
    handleViewTwoList() {
      let params = {
        bid_segment_id: this.bid_segment_id,
      };
      this.twoVisible = true;
      getTwoPriceList(params).then((res) => {
        this.twoPriceList = res.data;
        console.log(res);
      });
    },
    handleTip() {
      let text = `<p>本人已经阅读并完全理解《易招天成平台保密制度》的含义和要求，并庄严承诺:</p>
      <p>1、严格遵守《中华人民共和国保守国家秘密法》和《易招天成平台保密制度》各项规定，坚决不做违反招标保密的事情。</p>
      <p>2、如果本人违反上述保密承诺，愿意按照国家法律和易招天成平台保密制度有关规定接受处罚;如果因为本人的不当行为而导致易招天成平台遭受来自第三方的投诉，本人愿意承担易招天成平台为应诉而产生的一切后果。</p>
      <p>3、本承诺书由易招天成平台招标监管机构留存、本承诺自签订之日起生效，并长期有效。</p>`;
      this.$alert(text, "温馨提示", {
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
      });
    },
    handleInputBlur(row, prop) {
      let value = parseInt(row[prop], 10);
      if (!isNaN(value)) {
        if (value < 0) {
          value = 1;
        }
        if (value > 100) {
          value = 100;
        }
        this.$set(row, prop, value);
      } else {
        this.$set(row, prop, 1);
      }
    },
    handleSubmit() {
      this.$emit("submit", this.auditTable);
    },
    handleSaveAudit(data) {
      let formData = { ...data };
      formData.status = 1;
      updateAuditResult(formData).then((res) => {
        this.message(res.status_code, res.message);
        this.initAudit();
        this.auditVisible = false;
      });
    },
    handleSubmitAudit(data) {
      let formData = { ...data };
      formData.status = 2;
      updateAuditResult(formData).then((res) => {
        this.message(res.status_code, res.message);
        this.initAudit();
        this.auditVisible = false;
      });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    initAudit() {
      let params = {
        bid_segment_id: this.bid_segment_id,
      };
      getBidAuditResult(params).then((res) => {
        this.aduitData = res.data;
        console.log(res);
      });
    },
    handleExportExpertScores() {
      let params = {
        bid_segment_id: this.bid_segment_id,
      };
      exportAllExpertScores(params)
        .then((response) => {
          console.log(response);
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "评标详情.xlsx"); // 指定下载文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); // 下载后移除元素
          this.$message({
            message: "评标详情导出成功",
            type: "success",
          });
        })
        .catch((error) => {
          console.log(error);
          this.$message({
            message: "评标详情导出失败",
            type: "error",
          });
        });
    },
    handleExportChatRecords() {
      exportChatRecords(this.bid_segment_id)
        .then((response) => {
          console.log(response);
          const url = window.URL.createObjectURL(new Blob([response]));
          const link = document.createElement("a");
          link.href = url;
          link.setAttribute("download", "聊天记录.zip"); // 指定下载文件名
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link); // 下载后移除元素
          this.$message({
            message: "聊天记录导出成功",
            type: "success",
          });
        })
        .catch((error) => {
          console.log(error);
          this.$message({
            message: "聊天记录导出失败",
            type: "error",
          });
        });
    },
  },
  created() {
    this.initAudit();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
.legend {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.legend-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}
.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}
.completed {
  background-color: #4090f7;
}
.in-progress {
  background-color: #59c25c;
}
.not-started {
  background-color: #c9c9c9;
}
.end {
  color: #4090f7;
}
.progress {
  color: #59c25c;
}
.not-start {
  color: #c9c9c9;
}
</style>
