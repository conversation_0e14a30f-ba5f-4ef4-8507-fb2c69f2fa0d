# 专家与投标人聊天系统设计文档

## 1. 需求概述

### 1.1 功能需求

**专家端需求：**
- 每名专家只展示自己与投标人的某个标段的聊天
- 展示该标段投标人列表
- 点击投标人进入聊天页面，有消息提醒

**投标人端需求：**
- 可能会有多名专家与自己聊天
- 展示与自己沟通的专家列表
- 有人说话有提醒

### 1.2 业务规则

1. **权限控制**：专家只能与自己参与评标的标段相关投标人聊天
2. **数据安全**：投标人只能与评标自己投标标段的专家聊天
3. **消息提醒**：集成现有通知系统，支持实时消息提醒
4. **会话管理**：每个专家与投标人在特定标段只能有一个会话

## 2. 系统流程设计

### 2.1 业务流程图

```mermaid
graph TD
    A[专家进入评标大厅] --> B[选择特定标段评标]
    B --> C[查看该标段投标人列表]
    C --> D[点击投标人]
    D --> E[进入聊天页面]
    E --> F[发送/接收消息]

    G[投标人进入评标大厅] --> H[进入特定标段页面]
    H --> I[查看该标段专家列表]
    I --> J[点击专家]
    J --> K[进入聊天页面]
    K --> L[发送/接收消息]

    F --> M[消息提醒]
    L --> M
    M --> N[更新未读计数]
    N --> O[推送通知]

    P[权限验证] --> C
    P --> I
    P --> Q{专家是否参与该标段评标?}
    Q -->|是| R[允许聊天]
    Q -->|否| S[拒绝访问]

    T{投标人是否投标该标段?} --> R
    T -->|否| S

    U[标段上下文] --> B
    U --> H
    U --> V[所有聊天都基于当前标段]
```

### 2.2 API调用时序图

```mermaid
sequenceDiagram
    participant E as 专家
    participant ES as 专家端系统
    participant API as API服务
    participant DB as 数据库
    participant NS as 通知服务
    participant CS as 投标人端系统
    participant C as 投标人

    Note over E,C: 专家发起聊天场景
    E->>ES: 进入标段评标页面
    ES->>API: GET /api/expert/chat/bidders/{segmentId}
    API->>DB: 查询标段投标人列表
    DB-->>API: 返回投标人数据
    API-->>ES: 返回投标人列表(含未读数)
    ES-->>E: 显示投标人列表

    E->>ES: 点击投标人
    ES->>API: GET /api/expert/chat/messages/{segmentId}/{companyId}?page=1
    API->>DB: 查询或创建会话，获取最新聊天记录
    DB-->>API: 返回消息列表(倒序)
    API-->>ES: 返回聊天记录和会话信息
    ES-->>E: 显示聊天页面(最新消息在下方)

    Note over E,ES: 用户向上滚动查看历史消息
    E->>ES: 向上滚动
    ES->>API: GET /api/expert/chat/messages/{segmentId}/{companyId}?page=2
    API->>DB: 查询更早的聊天记录
    DB-->>API: 返回历史消息
    API-->>ES: 返回历史消息
    ES-->>E: 在顶部插入历史消息

    E->>ES: 发送消息
    ES->>API: POST /api/expert/chat/messages/{segmentId}/{companyId}
    API->>DB: 保存消息
    API->>NS: 触发消息通知
    NS->>CS: 推送消息提醒
    CS-->>C: 显示新消息提醒
    API-->>ES: 返回发送成功
    ES-->>E: 在底部显示新消息
```

## 3. 系统架构设计

### 3.1 数据库设计

#### 3.1.1 conversations表（聊天会话）
```sql
CREATE TABLE conversations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    expert_user_id BIGINT UNSIGNED NOT NULL COMMENT '专家用户ID（User表的ID）',
    company_info_id BIGINT UNSIGNED NOT NULL COMMENT '投标公司ID',
    bid_segment_id BIGINT UNSIGNED NOT NULL COMMENT '标段ID',
    last_message_at TIMESTAMP NULL COMMENT '最后消息时间',
    unread_count_expert INT DEFAULT 0 COMMENT '专家未读消息数',
    unread_count_company INT DEFAULT 0 COMMENT '公司未读消息数',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    UNIQUE KEY unique_conversation (expert_user_id, company_info_id, bid_segment_id),
    FOREIGN KEY (expert_user_id) REFERENCES users(id),
    FOREIGN KEY (company_info_id) REFERENCES company_infos(id),
    FOREIGN KEY (bid_segment_id) REFERENCES bid_segments(id)
);
```

#### 3.1.2 chat_messages表（聊天消息）
```sql
CREATE TABLE chat_messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    conversation_id BIGINT UNSIGNED NOT NULL COMMENT '会话ID',
    sender_type ENUM('expert', 'company') NOT NULL COMMENT '发送者类型',
    sender_id BIGINT UNSIGNED NOT NULL COMMENT '发送者ID',
    message TEXT NOT NULL COMMENT '消息内容',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_created (conversation_id, created_at)
);
```

### 3.2 模型关系设计

#### 3.2.1 Conversation模型关系
```php
class Conversation extends Model
{
    // 关联专家用户
    public function expertUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'expert_user_id');
    }

    // 关联投标公司
    public function companyInfo(): BelongsTo
    {
        return $this->belongsTo(CompanyInfo::class);
    }

    // 关联标段
    public function bidSegment(): BelongsTo
    {
        return $this->belongsTo(BidSegment::class);
    }

    // 关联聊天消息
    public function chatMessages(): HasMany
    {
        return $this->hasMany(ChatMessage::class);
    }
}
```

#### 3.2.2 ChatMessage模型关系
```php
class ChatMessage extends Model
{
    // 关联会话
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }
    
    // 多态关联发送者
    public function sender(): MorphTo
    {
        return $this->morphTo();
    }
}
```

## 4. API接口设计（简化版）

> **说明**：聊天功能在评标大厅中使用，专家和投标人都已进入特定标段的评标环节，只提供最基本的聊天功能。

### 4.1 专家端API

#### 4.1.1 获取当前标段的投标人列表
```
GET /api/expert/chat/bidders/{segmentId}

Response:
{
    "code": 200,
    "data": [
        {
            "company_id": 1,
            "company_name": "某某建筑公司",
            "contact_person": "张三",
            "conversation_id": 10,
            "unread_count": 2,
            "last_message": "您好，请问...",
            "last_message_at": "2024-01-15 10:30:00"
        }
    ]
}
```

#### 4.1.2 获取聊天消息（支持向上滚动分页）
```
GET /api/expert/chat/messages/{segmentId}/{companyId}?page=1&per_page=20

说明：
- segmentId: 标段ID
- companyId: 投标公司ID
- page=1 返回最新的20条消息（倒序排列，最新消息在前）
- page=2 返回更早的20条消息
- 前端向上滚动时请求下一页来加载历史消息

Response:
{
    "code": 200,
    "data": {
        "conversation_id": 10,
        "segment_name": "标段一",
        "company_name": "某某建筑公司",
        "contact_person": "张三",
        "current_page": 1,
        "messages": [
            {
                "id": 100,
                "sender_type": "company",
                "sender_name": "张三",
                "message": "好的，谢谢专家解答",
                "created_at": "2024-01-15 11:30:00"
            },
            {
                "id": 99,
                "sender_type": "expert",
                "sender_name": "李专家",
                "message": "这个问题是这样的...",
                "created_at": "2024-01-15 11:25:00"
            },
            {
                "id": 98,
                "sender_type": "company",
                "sender_name": "张三",
                "message": "您好，请问关于技术标准...",
                "created_at": "2024-01-15 10:30:00"
            }
        ],
        "total": 50,
        "has_more": true
    }
}
```

#### 4.1.3 发送消息
```
POST /api/expert/chat/messages/{segmentId}/{companyId}

Request:
{
    "message": "消息内容"
}

Response:
{
    "code": 200,
    "message": "发送成功",
    "data": {
        "id": 123,
        "message": "消息内容",
        "sender_type": "expert",
        "created_at": "2024-01-15 10:30:00"
    }
}
```

### 4.2 投标人端API

#### 4.2.1 获取当前标段的专家聊天列表
```
GET /api/company/chat/experts/{segmentId}

Response:
{
    "code": 200,
    "data": [
        {
            "expert_user_id": 1,
            "expert_name": "李专家",
            "conversation_id": 10,
            "unread_count": 1,
            "last_message": "好的，我明白了",
            "last_message_at": "2024-01-15 11:00:00"
        }
    ]
}
```

#### 4.2.2 获取聊天消息
```
GET /api/company/chat/messages/{segmentId}/{expertUserId}?page=1&per_page=20

说明：
- segmentId: 标段ID
- expertUserId: 专家用户ID（User表的ID）
- 响应格式和分页逻辑同专家端

Response:
{
    "code": 200,
    "data": {
        "conversation_id": 10,
        "segment_name": "标段一",
        "expert_name": "李专家",
        "current_page": 1,
        "messages": [...],
        "total": 50,
        "has_more": true
    }
}
```

#### 4.2.3 发送消息
```
POST /api/company/chat/messages/{segmentId}/{expertUserId}

说明：
- expertUserId: 专家用户ID（User表的ID）

Request:
{
    "message": "消息内容"
}

Response:
{
    "code": 200,
    "message": "发送成功",
    "data": {
        "id": 124,
        "message": "消息内容",
        "sender_type": "company",
        "created_at": "2024-01-15 10:35:00"
    }
}
```

### 4.3 通用API

#### 4.3.1 获取当前标段未读消息数量
```
GET /api/chat/unread-count/{segmentId}

Response:
{
    "code": 200,
    "data": {
        "unread_count": 5
    }
}
```

#### 4.3.2 标记会话为已读
```
PUT /api/expert/chat/read/{segmentId}/{companyId}
PUT /api/company/chat/read/{segmentId}/{expertUserId}

Response:
{
    "code": 200,
    "message": "标记成功"
}
```

## 5. 权限验证逻辑

### 5.1 专家权限验证
```php
/**
 * 验证专家是否有权限与某投标人在特定标段聊天
 */
function canExpertChatWithCompany($expertUserId, $companyId, $segmentId) {
    // 1. 通过User ID获取Expert ID
    $expert = Expert::where('user_id', $expertUserId)->first();
    if (!$expert) {
        return false;
    }

    // 2. 检查专家是否参与该标段评标
    $expertInSegment = ExpertSelectionLog::where('expert_id', $expert->id)
        ->where('bid_segment_id', $segmentId)
        ->where('selection_status', 1) // 已接受邀请
        ->exists();

    // 3. 检查投标人是否投标该标段
    $companyInSegment = BidDocumentSubmission::where('company_info_id', $companyId)
        ->where('bid_segment_id', $segmentId)
        ->where('status', '>', 0) // 已提交投标
        ->exists();

    return $expertInSegment && $companyInSegment;
}
```

### 5.2 投标人权限验证
```php
/**
 * 验证投标人是否有权限与某专家在特定标段聊天
 */
function canCompanyChatWithExpert($companyId, $expertUserId, $segmentId) {
    return canExpertChatWithCompany($expertUserId, $companyId, $segmentId);
}
```

## 6. 消息提醒集成

### 6.1 发送消息时触发通知
```php
public function sendMessageNotification($message) {
    $conversation = $message->conversation;
    
    if ($message->sender_type === 'expert') {
        // 通知投标人
        $recipient = User::where('company_info_id', $conversation->company_info_id)->first();
        $title = "专家消息提醒";
        $content = "专家在标段「{$conversation->bidSegment->segment_name}」中给您发送了新消息";
    } else {
        // 通知专家
        $expert = $conversation->expert;
        $recipient = User::where('id', $expert->user_id)->first();
        $title = "投标人消息提醒";
        $content = "投标人在标段「{$conversation->bidSegment->segment_name}」中给您发送了新消息";
    }
    
    // 使用现有通知系统发送
    $notificationService = new NotificationService();
    $notificationService->sendNotification($recipient->id, $title, $content, 'chat_message', $message->id);
}
```

## 7. 简化后的API总结

### 7.1 专家端（评标大厅中）
- `GET /api/expert/chat/bidders/{segmentId}` - 获取当前标段投标人列表
- `GET /api/expert/chat/messages/{segmentId}/{companyId}` - 获取聊天消息（支持分页）
- `POST /api/expert/chat/messages/{segmentId}/{companyId}` - 发送消息
- `PUT /api/expert/chat/read/{segmentId}/{companyId}` - 标记会话为已读

### 7.2 投标人端
- `GET /api/company/chat/experts/{segmentId}` - 获取当前标段专家聊天列表
- `GET /api/company/chat/messages/{segmentId}/{expertUserId}` - 获取聊天消息（支持分页）
- `POST /api/company/chat/messages/{segmentId}/{expertUserId}` - 发送消息
- `PUT /api/company/chat/read/{segmentId}/{expertUserId}` - 标记会话为已读

> **重要说明**：expertUserId 是 User 表的 ID，与认证系统一致

### 7.3 通用API
- `GET /api/chat/unread-count/{segmentId}` - 获取当前标段未读消息数量

## 8. 实现文件结构

```
app/Models/
├── Conversation.php          # 聊天会话模型
├── ChatMessage.php          # 聊天消息模型

app/Http/Controllers/Api/
├── expert/ExpertChatController.php    # 专家聊天控制器
├── company/CompanyChatController.php  # 投标人聊天控制器

database/migrations/
├── create_conversations_table.php     # 会话表迁移
├── create_chat_messages_table.php     # 消息表迁移

app/Services/
├── ChatService.php          # 聊天业务逻辑服务

routes/
├── api.php                  # 路由配置
```

## 9. 性能优化建议

### 9.1 数据库索引优化
- conversations表：(expert_id, company_info_id, bid_segment_id)唯一索引
- chat_messages表：(conversation_id, created_at)复合索引

### 9.2 缓存策略
- 缓存用户的未读消息数量
- 缓存最近的聊天记录

### 9.3 分页加载
- 聊天消息采用分页加载，避免一次性加载过多数据

## 10. 安全考虑

1. **输入验证**：对所有用户输入进行严格验证和过滤
2. **XSS防护**：对消息内容进行HTML转义
3. **权限检查**：每个API调用都进行严格的权限验证
4. **敏感信息保护**：不在日志中记录聊天内容

## 11. 测试计划

### 11.1 单元测试
- 模型关系测试
- 权限验证逻辑测试
- 消息发送逻辑测试

### 11.2 集成测试
- API接口测试
- 通知系统集成测试
- 权限控制集成测试

### 11.3 性能测试
- 大量消息加载性能测试
- 并发聊天性能测试

## 12. 部署说明

1. 运行数据库迁移
2. 配置路由
3. 清除缓存
4. 测试API接口
5. 验证通知功能

---

**文档版本**：v1.0  
**创建日期**：2024-12-29  
**最后更新**：2024-12-29
