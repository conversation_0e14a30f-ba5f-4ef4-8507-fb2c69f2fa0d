<template>
  <div>
    <el-dialog
      :title="title"
      width="60%"
      :visible.sync="visible"
      :modal="false"
      :before-close="handleClose"
    >
      <el-button
        :disabled="node.progress_status === 2"
        type="primary"
        style="float: right; margin-bottom: 5px"
        @click="handleAllPass"
        >全部通过</el-button
      >
      <el-table :data="tableData.clauses" border>
        <!-- 第一列：序号 -->
        <el-table-column label="序号" align="center" width="80" type="index">
        </el-table-column>

        <!-- 第二列：评审项 -->
        <el-table-column label="评审项" align="left" width="200">
          <template slot-scope="scope">
            <el-link @click="handleView(scope.row)">
              {{ scope.row.name }}</el-link
            >
          </template>
        </el-table-column>

        <!-- 动态生成公司列 -->
        <el-table-column
          v-for="(company, index) in tableData.companyInfos"
          :key="index"
          :label="company.company_name"
          align="center"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-radio-group
              :disabled="node.progress_status === 2"
              @input="handleChange($event, scope.$index, index)"
              v-model="tableData.clauses[scope.$index].scores[index]"
              size="small"
            >
              <el-radio :label="2">✔️</el-radio>
              <el-radio :label="1">×</el-radio>
            </el-radio-group>
          </template>
        </el-table-column>
      </el-table>
      <el-table :data="table" border style="margin-top: 10px">
        <el-table-column label="总结果" align="left" width="280">
          <template slot-scope="scope">
            {{ scope.row.title }}
          </template>
        </el-table-column>
        <!-- 动态生成公司列 -->
        <el-table-column
          v-for="(company, index) in tableData.companyInfos"
          :key="index"
          :label="company.company_name"
          align="center"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-radio-group
              :disabled="node.progress_status === 2"
              size="small"
              v-if="scope.$index == 0"
              v-model="company.final_pass_status"
            >
              <el-radio :label="2">✔️</el-radio>
              <el-radio :label="1">×</el-radio>
            </el-radio-group>

            <el-input
              :disabled="node.progress_status === 2"
              v-else
              v-model="company.final_pass_desc"
            ></el-input>
          </template>
        </el-table-column>
      </el-table>
      <span
        slot="footer"
        class="dialog-footer"
        v-if="node.progress_status === 2"
      >
        <el-button @click="handleClose">关 闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer" v-else>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <!-- <el-button type="primary" @click="handleSubmit">提交</el-button> -->
      </span>
    </el-dialog>
    <el-dialog
      :modal="false"
      title="查看评审项"
      :visible.sync="viewShow"
      width="40%"
    >
      <h3>条款名称：{{ data.name }}</h3>
      <h3>条款描述：{{ data.description }}</h3>
      <h3>是否客观项：{{ data.is_objective === 0 ? "否" : "是" }}</h3>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewShow = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: () => "",
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    companies: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    node: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      viewShow: false,
      data: {},
      table: [
        {
          title: "结论",
        },
        {
          title: "说明",
        },
      ],
    };
  },
  methods: {
    handleAllPass() {
      // 遍历所有条款
      this.tableData.clauses.forEach((clause, clauseIdx) => {
        // 遍历所有公司
        this.tableData.companyInfos.forEach((company, companyIdx) => {
          // 设置为通过（2）
          this.$set(clause.scores, companyIdx, 2);
        });
      });
      // 触发每个公司的结论自动更新
      this.tableData.companyInfos.forEach((company, companyIdx) => {
        this.handleChange(2, 0, companyIdx);
      });
    },
    handleClose() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleChange(val, row, index) {
      console.log(this.node);
      let count = this.node.rejection_condition;
      let result = [];
      this.tableData.clauses.forEach((item) => {
        result.push(item.scores[index]);
        console.log(result);
      });
      let noPassCount = result.filter((item) => {
        return item === 1 || item === null;
      });
      console.log(noPassCount);

      if (noPassCount.length > count) {
        this.tableData.companyInfos[index].final_pass_status = 1;
      } else if (noPassCount.length === 0) {
        this.tableData.companyInfos[index].final_pass_status = 2;
      } else {
        this.tableData.companyInfos[index].final_pass_status = 2;
      }
      console.log(val, row, index);
    },
    handleSubmit() {
      this.$emit("submit", this.tableData);
      // 打印填写的结果
      console.log("填写结果：", this.tableData);
    },
    handleSave() {
      this.$emit("save", this.tableData);
      // 打印填写的结果
      console.log("填写结果：", this.tableData);
    },
    handleView(data) {
      console.log(data);
      this.viewShow = true;
      this.data = data;
    },
  },
};
</script>

<style scoped>
/* 自定义样式可根据需求调整 */
</style>
