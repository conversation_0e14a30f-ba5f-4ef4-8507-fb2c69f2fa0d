<template>
  <div class="login-container">
    <div class="menu" style="justify-content: space-around">
      <a> <img style="height: 120px" :src="logoUrl" alt="" /></a>
      <nav>
        <router-link to="/home?name=home">
          <a href="#">首页</a>
        </router-link>
        <router-link to="/home?name=about-us">
          <a href="#">招标信息</a>
          <el-divider style="background-color: chartreuse"></el-divider>
        </router-link>
        <router-link to="/home?name=showcases">
          <a href="#showcases">政策法规</a>
        </router-link>
        <router-link to="/home?name=service"
          ><a href="#service">公司资讯</a>
        </router-link>
        <router-link to="/home?name=cmap">
          <a href="#cmap">保函业务</a>
        </router-link>
      </nav>
      <div class="logo">
        <!-- <span style="font-size: 20px; margin-right: 10px"> wangdana</span>
        <img style="width: 50px; height: 50px" :src="logoUrl" alt="" /> -->
      </div>
    </div>

    <div class="slide-content">
      <img
        :style="{ width: '100%' }"
        class="swiper-wrapper-img"
        :src="imgUrl"
      />
    </div>

    <div class="content-wrapper">
      <div class="announcement">
        <h2 style="text-align: center">
          {{ bidData.bidding_project.bidding_project_name }}
        </h2>
        <div class="details">
          <p>发布时间：{{ bidData.publish_date }}</p>
          <p>查看次数：{{ bidData.num }}</p>
          <p>
            招标/采购项目编号：{{
              bidData.bidding_project.bidding_project_code
            }}
          </p>
          <!-- <p>所属行业：{{}}</p> -->
          <p>业主单位：{{ bidData.bidding_project.owner_unit }}</p>
          <p>
            招标项目地址：{{ bidData.bidding_project.bidding_project_location }}
          </p>
          <p>招标组织形式：{{ bidData.bidding_project.organizational_form }}</p>
        </div>
        <el-steps
          v-if="$route.query.status == 6"
          :active="bidData.flow_status"
          finish-status="success"
        >
          <el-step
            title="招标文件获取时间"
            :description="bidData.file_obtain_start_time"
          ></el-step>
          <el-step
            title="招标文件获取截止时间"
            :description="bidData.file_obtain_end_time"
          ></el-step>
          <el-step
            title="投标文件递交截止时间"
            :description="bidData.submission_deadline"
          ></el-step>
          <el-step
            title="开标时间"
            :description="bidData.submission_deadline"
          ></el-step>
        </el-steps>
      </div>
      <div id="content"></div>
      <div id="file">
        附件:<a
          style="margin-right: 10px"
          v-for="item in bidData.attachments"
          :key="item.name"
          :href="'http://bidding.senmoio.cn/' + item.path"
          >{{ item.name }}</a
        >
      </div>
      <el-button
        style="margin-top: 10px"
        type="success"
        @click="handleJion"
        v-if="$route.query.status == 6"
        >立即参与</el-button
      >
    </div>

    <footer>
      <div class="footer-menus">
        <div class="contact-us">
          <p class="menu-title">联系我们</p>
          <p>地址：河北省邢台市</p>
          <p>电话：0319-4562808</p>
          <p>电子邮箱：<EMAIL></p>
        </div>
        <div class="contact-us footer-menu">
          <p class="menu-title">公共服务平台</p>
          <p>
            <el-link href="http://www.cebpubservice.com" target="blank"
              >中国招标投标公共服务平台</el-link
            >
          </p>
          <p>
            <el-link href="http://jzsc.mohurd.gov.cn/home" target="blank"
              >全国建筑市场监督公共服务平台</el-link
            >
          </p>
          <p>
            <el-link href="http://ggzy.hebei.gov.cn/hbjyzx" target="blank"
              >河北省全国公共资源服务平台</el-link
            >
          </p>
          <p>
            <el-link href="http://ggzy.hebei.gov.cn/index.html" target="blank"
              >河北省政务服务管理办公室</el-link
            >
          </p>
        </div>

        <div class="contact-us">
          <p class="menu-title">行业监管部门</p>
          <p>
            <el-link href="http://hbdrc.hebei.gov.cn" target="blank"
              >河北省发改委</el-link
            >
          </p>
          <p>
            <el-link href="http://zfcxjst.hebei.gov.cn" target="blank"
              >河北省住建厅</el-link
            >
          </p>
          <p>
            <el-link href="http://jtt.hebei.gov.cn" target="blank"
              >河北省交通运输厅</el-link
            >
          </p>
          <p>
            <el-link href="http://slt.hebei.gov.cn" target="blank">
              河北省水利厅</el-link
            >
          </p>
          <p>
            <el-link href="http://hbsa.hebei.gov.cn" target="blank"
              >河北省国资委</el-link
            >
          </p>
          <p>
            <el-link href="http://czt.hebei.gov.cn" target="blank">
              河北省财政厅</el-link
            >
          </p>
        </div>

        <p class="icp-info">
          <a href="http://beian.miit.gov.cn/">冀ICP备2024097245号</a>
        </p>
        <p class="rights">©Copyright 2024 河北绿萝科技发展有限公司 版权所有</p>
        <div class="scrollToTop">
          <a href="#home"><i class="fas fa-chevron-up"></i></a>
        </div>
        <div class="customer-service-sidebar">
          <el-menu
            default-active="1"
            class="el-menu-vertical-demo"
            background-color="#f5f5f5"
            text-color="#333"
            active-text-color="#409EFF"
            unique-opened
          >
            <el-popover placement="right" width="240" trigger="hover">
              <div style="text-align: left; dispaly: flex">
                <div style="font-size: 20px; color: #4daa44">
                  电话:0319-4562808
                </div>
                <div style="font-size: 20px; color: #4daa44">
                  邮箱:<EMAIL>
                </div>
              </div>
              <el-menu-item slot="reference" index="1">
                <template slot="title">
                  <div>
                    <img
                      style="width: 32px; height: 32px"
                      :src="phoneUrl"
                      alt=""
                    />
                    <span>咨询热线</span>
                  </div>
                </template>
              </el-menu-item>
            </el-popover>

            <el-popover placement="right" width="200" trigger="hover">
              <div style="text-align: center; dispaly: flex">
                <img
                  style="width: 150px; height: 150px"
                  :src="codeUrl"
                  alt=""
                />
                <div style="color: #4daa44">微信客服</div>
              </div>
              <el-menu-item slot="reference" index="2">
                <img style="width: 32px; height: 32px" :src="wechatUrl" />
                <span>微信客服</span>
              </el-menu-item>
            </el-popover>
          </el-menu>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import {
  homeAnnouncementList,
  getNoticesTwo,
  getNotices,
  getAnnouncementDetail,
} from "@/api/home/<USER>";
import { sendMsg, register } from "@/api/user";
// 引入js
import Swiper from "swiper";
// 引入css
import "swiper/css/swiper.min.css";
import "../../assets/home/<USER>";
import { text } from "@/views/bid-announcement-manage/modules/text";
import moment from "moment";
export default {
  name: "Login",
  components: {
    Swiper,
  },
  data() {
    return {
      text: text,
      visible: false,
      swiper: null,
      bidIndex: 6,
      fIndex: 1,
      gIndex: 3,
      width: 1024,
      height: 768,
      fgList: [],
      gsList: [],
      ggTotal: 0,
      fgTotal: 0,
      gsTotal: 0,
      ggPage: 1,
      fgPage: 1,
      gsPage: 1,
      announcementList: [],
      imgUrl: require("@/assets/images/img1.jpg"),
      wechatUrl: require("@/assets/images/wechat.jpg"),
      phoneUrl: require("@/assets/images/phone.jpg"),
      logoUrl: require("@/assets/images/home_logo.jpg"),
      codeUrl: require("@/assets/images/code.jpg"),
      passwordType: "password",
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页显示条目个数
      total: 100, // 总条目数
      bidData: null,
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query);
        }
      },
      immediate: true,
    },
  },
  created() {
    this.width = window.innerWidth + "px";
    this.height = window.innerHeight / 2 + "px";
  },
  mounted() {
    this.getSwiper();
    this.getHomeAnnouncementList(this.bidIndex);
    this.initData();
    let params = {
      id: this.$route.query.id,
      status: this.$route.query.status,
    };
    console.log(params);

    this.handleGgInfo(params);
    // 创建一个元素，例如div
  },
  methods: {
    handleJion() {
      this.$router.push({ path: "/tender/registrat-invitat" });
    },
    handleGgInfo(params) {
      getAnnouncementDetail(params).then((res) => {
        this.bidData = res.data;
        console.log(res);
        this.$nextTick(() => {
          var element = document.createElement("div");

          // 设置innerHTML
          if (this.$route.query.status == 6) {
            element.innerHTML = this.bidData.details;
          } else {
            element.innerHTML = this.bidData.content;
          }

          document.getElementById("content").appendChild(element);
        });
      });
      //   console.log(item);
    },
    initData() {
      this.getNotices(1);
      this.getNoticesTwo(3);
    },
    gotoCgPage(index) {
      this.bidIndex = index;
      this.getHomeAnnouncementList(index);
    },
    cgChangePage(page) {
      this.ggPage = page;
      this.getHomeAnnouncementList(this.bidIndex);
    },
    getNotices(index) {
      let params1 = {
        status: index,
        page: this.fgPage,
        per_page: 5,
      };
      getNotices(params1).then((res) => {
        this.fgList = res.data.data;
        this.fgTotal = res.data.total;
        this.fgList.forEach((item) => {
          item.created_at =
            item.created_at &&
            moment(new Date(item.created_at)).format("YYYY-MM-DD");
        });
        console.log("111", this.fgList);
      });
    },
    gotoFgPage(index) {
      this.fIndex = index;
      this.getNotices(index);
    },
    fgChangePage(page) {
      this.fgPage = page;
      this.getNotices(this.fIndex);
    },
    getNoticesTwo(index) {
      let params1 = {
        status: index,
        page: this.gsPage,
        per_page: 5,
      };
      getNoticesTwo(params1).then((res) => {
        this.gsList = res.data.data;
        this.gsTotal = res.data.total;
        this.gsList.forEach((item) => {
          item.created_at =
            item.created_at &&
            moment(new Date(item.created_at)).format("YYYY-MM-DD");
        });
        console.log("111", this.gsList);
      });
    },
    gotoGsPage(index) {
      this.gIndex = index;
      this.getNoticesTwo(index);
    },
    gsChangePage(page) {
      this.gsPage = page;
      this.getNoticesTwo(this.gIndex);
    },
    getHomeAnnouncementList(status) {
      let params = {
        status,
        page: this.ggPage,
        per_page: 5,
      };
      homeAnnouncementList(params).then((res) => {
        this.announcementList = res.data.data;
        this.ggTotal = res.data.total;
        this.announcementList.forEach((item) => {
          item.publish_date =
            item.publish_date &&
            moment(new Date(item.publish_date)).format("YYYY-MM-DD");
        });
      });
    },
    getSwiper() {
      this.swiper = new Swiper(".swiper-container", {
        loop: true, // 无缝
        autoplay: {
          //自动开始
          delay: 3000, //时间间隔
          disableOnInteraction: false, //*手动操作轮播图后不会暂停*
        },
        paginationClickable: true,
        slidesPerView: 1, // 一组三个
        // spaceBetween: 30, // 间隔
        // 如果需要前进后退按钮
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        // 窗口变化,重新init,针对F11全屏和放大缩小,必须加
        observer: true,
        observeParents: true,
        // 如果需要分页器
        pagination: {
          el: ".swiper-pagination",
          clickable: true, // 分页器可以点击
        },
      });
    },
    gotoLogin() {
      this.$router.push("/login");
    },
    handleRegist() {
      this.visible = true;
    },
    handleRegistSubmit(data) {
      register(data).then((res) => {
        this.message(res.status_code, res.message);
        if (res.status_code == 200) {
          this.visible = false;
        }
        console.log(res);
      });
    },
    handleClose() {
      this.visible = false;
    },
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && key >= "A" && key <= "Z";
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
    },
  },
};
</script>

<style lang="scss" scoped>
.logo {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-lightest);
  /* width:80%; */
}
.menu {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  font-family: "思源宋体";
}
.menu nav {
  // justify-self: end;
  // text-align: right;
  display: flex;
  padding-top: 35px;
  // justify-content: space-between;
  // align-items: center;
}

.menu nav i {
  color: var(--text-color-lightest);
}

.menu nav a {
  text-decoration: none;
  margin: 0 20px;
  font-size: 18px;
  font-size: 22px;
  font-weight: bold;
}
.menu nav a.nav-active {
  color: #7fcb80;
}

.menu .burger {
  display: none;
}

.menu.sticky {
  top: 0px;
  position: fixed;
  background-color: white;
  box-shadow: 0 0 18px rgba(0, 0, 0, 0.2);
  animation: dropDown 0.5s ease-in-out forwards;
}

.menu.sticky .log,
.menu.sticky nav a,
.menu.sticky nav i {
  color: var(--text-color-darker);
}
.el-divider {
  background-color: #90c987;
}
.el-divider--horizontal {
  display: block;
  height: 2px;
  width: 100%;
  margin: 20px 0;
}
.customer-service-sidebar {
  position: fixed;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  z-index: 1000;
  width: 80px; /* 根据需要调整宽度 */
  background-color: #f5f5f5;
}

.el-menu {
  background-color: transparent;
  border: none;
}

.el-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80px; /* 根据需要调整高度 */
  text-align: center;
  border-bottom: 1px solid #eee;
  line-height: 30px !important;
}

.el-menu-item i {
  margin-bottom: 5px; /* 图标和文字之间的间距 */
  font-size: 24px; /* 图标大小 */
}

.el-menu-item span {
  display: block;
  font-size: 12px; /* 文字大小 */
  color: #333;
}

.wechat-qrcode {
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: #fff;
  padding: 10px;
  text-align: center;
}

.wechat-qrcode img {
  width: 100%;
  height: auto;
  margin-bottom: 5px;
}
#file {
  width: 80%;
  background-color: #c6c6c6;
  height: 40px;
  line-height: 40px;
  padding: 00px 20px;
}
#content {
  margin-top: 20px;
  margin-bottom: 20px;
  width: 80%;
}
.announcement {
  width: 80%;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
}

.details p {
  margin: 25px 0;
}

.el-steps {
  margin-top: 20px;
}

.container {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px;
}
.item {
  text-align: center;
  width: 100%;
  padding: 0px 20px;
}
.item img {
  width: 300px; /* 根据需要调整图片大小 */
  height: 192px;
}
.item h3 {
  margin-top: 10px;
}
.responsive-container {
  position: relative;
  width: 100%; /* 宽度可以根据需要调整 */
  height: 0; /* 高度设为0，由padding-bottom控制比例 */
  padding-bottom: 56.25%; /* 16:9 比例 (9 / 16 * 100) */
  overflow: hidden;
}

.responsive-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover; /* 保持图片宽高比并覆盖整个容器 */
}
/* 设置主要色调和次要色调 */
:root {
  --primary-color: rgb(71, 102, 255);
  --secondary-color: #e3e3e3;
  --text-color-lightest: #e7e9ec;
  --text-color-darker: #2e2e2e;
  --text-color-dark: #494949;
  --text-color-gray: #8b8b8b;
  --text-color-dark-gray: #727272;
  --text-color-light-gray: #c6c6c6;
  --backdrop-color: rgba(42, 42, 42, 0.69);
}
.login-container {
  overflow-x: hidden;
}
// .swiper {
//   overflow: hidden;
// }
.swiper-slide {
  height: auto;
  text-align: center;
}

// .swiper-wrapper-img {
//   width: 100%;
// }

/* head.sticky,
.glide,
section,
footer{
    max-width: 100vm;
} */

/* 头部导航 */
header {
  height: 510px;
  /* 栅格布局 */
  display: grid;
  padding: 0 20px;
  grid-template-columns: 1fr 2fr;
  align-items: center;

  z-index: 200;
  background-image: url("../../assets/images/bid.png");
  object-fit: fill;
  background-repeat: round;
}

.logo {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-color-lightest);
  /* width:80%; */
}

.vader {
  height: 30%;
  width: 30%;
  top: 10px;
  left: 0;
  /* vertical-align: middle; */
  /* left:20px; */
}

/* .vader1{
    display: inline;
    position: relative;
    width:100px;
    top:0px;
    left: 0px */

header nav {
  justify-self: end;
  text-align: right;
  display: flex;
  justify-content: space-between;
  position: absolute;
  top: 50px;
}

header nav i {
  color: var(--text-color-lightest);
}

header nav a {
  color: #fff;
  text-decoration: none;
  margin: 0 24px;
  font-size: 18px;
}

header .burger {
  display: none;
}

header.sticky {
  top: 0px;
  position: fixed;
  background-color: white;
  box-shadow: 0 0 18px rgba(0, 0, 0, 0.2);
  animation: dropDown 0.5s ease-in-out forwards;
}

header.sticky .log,
header.sticky nav a,
header.sticky nav i {
  color: var(--text-color-darker);
}

@keyframes dropDown {
  from {
    transform: translateY(-100px);
  }
  to {
    transform: translateY(0);
  }
}
.glide {
  position: relative;
  top: -120px;
  z-index: 50;
}

.glide__slide img,
.glide__slide video {
  width: 100vw;
  height: 100vh;
  object-fit: cover;
}

.slide-caption {
  position: absolute;
  z-index: 70;
  color: var(--text-color-lightest);
  text-align: center;
  max-width: 60vw;
}

.glide__slide {
  display: flex;
  align-items: center;
  justify-content: center;
}

.slide-caption h1 {
  font-size: 54px;
  font-weight: 600;
}

.slide-caption h3 {
  font-size: 24px;
  margin: 48px 0;
}

.slide-caption > * {
  opacity: 0;
}

.slide-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
.slide-caption {
  position: absolute;
  bottom: 30px;
  left: 30px;
  color: white;
  z-index: 1;
  text-align: center;
}
.backdrop {
  z-index: 60;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}

.explore-btn {
  padding: 14px 32px;
  background-color: var(--primary-color);
  border: 0;
  border-radius: 4px;
  color: var(--text-color-lightest);
  font-size: 18px;
  cursor: pointer;
  outline: none;
}

/* 内容区域通用样式 */

.content-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-top: 50px;
}

section {
  display: grid;
  justify-items: center;
  max-width: 1180px;
  padding: 0 80px;
}

section-bg {
  position: relative;
}

.section-bg::before {
  content: "";
  display: block;
  position: absolute;
  background-color: #f9fbfb;
  width: 100vw;
  height: 100%;
  z-index: -1;
}

.title1 {
  font-size: 34px;
  color: var(--text-color-darker);
}

.title1::after {
  content: "";
  display: block;
  width: 80%;
  height: 4px;
  background-color: var(--primary-color);
  margin-top: 14px;
  transform: translateX(10%);
}

.intro {
  margin: 20px 0 60px 0;
  font-size: 20px;
  color: var(--text-color-dark-gray);
  text-align: center;
}

.intro i {
  color: var(--primary-color);
  vertical-align: -2px;
  font-weight: 600;
  font-style: normal;
}

/* 关于我们 */
.about-us {
  padding-bottom: 32px;
}

.features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 180px);
  column-gap: 5vw;
}

.feature {
  display: grid;
  grid-template-areas:
    "icon title"
    "icon content";
  grid-template-columns: 60px 1fr;
  grid-template-rows: 1fr 3fr;
}

.feature i.fas,
.feature i.fab {
  grid-area: icon;
  font-size: 34px;
  color: var(--primary-color);
  margin-top: 19px;
}

.feature-title {
  grid-area: title;
  font-size: 18px;
  color: var(--text-color-darker);
  margin-bottom: 8px;
}

.feature-content {
  grid-area: content;
  color: var(--text-color-gray);
  margin-top: 8px;
}

/* 成功案例 */
.showcases {
  max-width: unset;
  padding: 0;
  padding-top: 72px;
}

.filter-btns {
  margin-top: 54px;
  margin-bottom: 38px;
}
.filter-btn {
  margin: 0 7px;
  background-color: var(--secondary-color);
  border: 0;
  color: var(--text-color-dark-gray);
  padding: 8px 18px;
  border-radius: 4px;
  cursor: pointer;
  transition: 0.4s;
}

.filter-btn:focus,
.filter-btns:active {
  outline: none;
}

.filter-btn.active,
.filter-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.showcases .cases {
  width: 100vw;
}

.showcases .case-item {
  width: 25vw;
  height: 20vw;
  overflow: hidden;
}

.case-item img {
  height: 100%;
  object-fit: cover;
}

/* 服务流程 */
.service {
  padding-top: 161px;
}

.services {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 240px);
  column-gap: 28px;
  row-gap: 24px;
}

.service-item {
  display: grid;
  grid-template-areas:
    "icon title"
    "icon content";
  grid-template-columns: 70px 1fr;
  grid-template-rows: 1fr 3fr;
  padding: 24px;
  box-shadow: 0 0 18px rgba(0, 0, 0, 0.06);
}

.service-item i.fas {
  grid-area: icon;
  font-size: 34px;
  color: var(--primary-color);
  padding-top: 20px;
}

.service-item .service-title {
  grid-area: "title";
  color: var(--text-color-darker);
  font-size: 24px;
}

.service-item .service-content {
  grid-area: content;
  color: var(--text-color-gray);
  line-height: 30px;
  font-size: 16px;
  margin-top: 8px;
}

.cmap {
  margin: 80px;
}

/* 团队介绍 */

.team-intro {
  margin: 48px 0;
  padding-top: 62px;
  padding-bottom: 52px;
}

.team-members {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 24px;
  margin-top: 86px;
}

.team-member {
  background-color: white;
  box-shadow: 0 0 24 rgba(0, 0, 0, 0.2);
  text-align: center;
  padding-bottom: 28px;
  transition: 0.4s;
  display: grid;
  justify-items: center;
}

.photo {
  overflow: hidden;
}

.photo img {
  width: 100%;
  height: 264px;
  object-fit: cover;
  object-position: top center;
}

.team-member .name {
  margin-top: 18px;
  font-size: 18px;
  font-weight: 500;
  color: var(--text-color-darker);
}

.team-member .position {
  color: var(--text-color-dark-gray);
  margin-top: 12px;
  margin-bottom: 18px;
}

.social-links {
  width: 100%;
  max-width: 200px;
  display: flex;
  justify-content: space-between;
  padding: 0 42px;
}

.social-links li {
  list-style: none;
}

.social-links li a {
  color: var(--text-color-darker);
  font-size: 24px;
  text-decoration: none;
}

.team-member:hover {
  transform: translateY(-20px) scale(1.05);
  box-shadow: 0 0 36px rgba(0, 0, 0, 0.1);
}

/* 数据部分 */

.data-section {
  max-width: 100%;
  width: 100vw;
  height: 255px;
  // background-image: url("../../assets/images/adult-business-computer-contemporary-380769.jpg");
  background-size: cover;
  background-position: center;

  display: grid;
  grid-template-columns: repeat(4, minmax(auto, 220px));
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 20;
}

.data-section::before {
  content: "";
  display: block;
  position: absolute;
  background-color: rgba(42, 42, 42, 0.69);
  width: 100%;
  height: 100%;
  z-index: 1;
}

.data-piece {
  width: 250px;
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  justify-items: center;
  color: white;
  position: relative;
  z-index: 40;
}

.data-piece i.fas {
  font-size: 44px;
}

.data-piece .num {
  margin-top: 7px;
  font-size: 35px;
  font-weight: 500;
}

.data-piece .data-desc {
  font-size: 20px;
  font-weight: 400;
}

footer {
  margin-top: 124px;
  background-color: #4daa44;
  display: grid;
  justify-items: center;
  padding-top: 72px;
  padding-bottom: 24px;
  color: #fff;
}

/* 底部菜单 */
.footer-menus {
  width: 100%;
  max-width: 1180px;
  display: grid;
  /* 5列布局 */
  grid-template-columns: 2fr repeat(4, 1fr);
  padding: 0 80px;
  position: relative;
}

/* 导航菜单，靠右对齐 */
.footer-menu {
  justify-self: end;
}

/* 一级菜单 */
.menu-title {
  font-size: 20px;
  color: white;
  font-weight: 600;
  margin-bottom: 20px;
}

.contact-us {
  justify-self: start;
  color: var(--text-color-lightest);
  width: 400px;
}

.contact-us .el-link {
  color: #fff;
  font-size: 16px;
}

/* 联系我们，文字 */
.contact-us p:not(:first-child) {
  padding-bottom: 16px;
}

/* 菜单项 */
.menu-items li {
  list-style: none;
  padding-bottom: 8px;
}
/* 菜单链接 */
.menu-items li a {
  text-decoration: none;
  font-weight: 300;
  color: var(--text-color-lightest);
}

/* 备案信息 */
.icp-info {
  margin-top: 24px;
  margin-bottom: 16px;
}

/* 备案信息，版权信息 */
.icp-info,
.rights {
  /* 占满整行，-1代表最后一个编号 */
  grid-column: 1 / -1;
  /* 居中对齐 */
  justify-self: center;
  color: white;
  font-size: 14px;
}

.scrollToTop {
  display: none;
}
.scrollToTop a {
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  position: fixed;
  bottom: 60px;
  right: 30px;
  z-index: 300;
}

/* 自适应，小于1100象素时 */
@media (max-width: 1100px) {
  /* 导航设置为不可见，点击折叠按钮显示全屏导航 */
  header nav {
    display: none;
  }

  /* 头部平分两列布局 */
  header {
    grid-template-columns: repeat(2, 1fr);
  }

  /* 折叠菜单样式，显示出来 */
  header .burger {
    display: block;
    justify-self: center;
    cursor: pointer;
    position: relative;
    width: 20px;
    height: 6px;
  }

  /* 折叠按钮线条样式 */
  .burger-line1,
  .burger-line2,
  .burger-line3 {
    width: 20px;
    height: 2px;
    background-color: var(--text-color-lightest);
    /* position: relative; */
  }

  /* 上移第一条线 */
  .burger-line1 {
    position: absolute;
    top: -6px;
  }
  /* 下移第三条线 */
  .burger-line3 {
    position: absolute;
    top: 6px;
  }

  /* 全屏导航展开时，折叠按钮设置为深色 */
  header.open .burger-line1,
  header.open .burger-line2,
  header.open .burger-line3,
  header.sticky .burger-line1,
  header.sticky .burger-line2,
  header.sticky .burger-line3 {
    background-color: var(--text-color-darker);
    transition: 0.4s ease;
  }

  /* 全屏导航显示时，折叠按钮第一条线样式 */
  header.open .burger-line1 {
    transform: rotate(45deg) translate(3px, 5px);
  }

  /* 全屏导航显示时，折叠按钮第二条线样式 */
  header.open .burger-line2 {
    transform: translateX(5px);
    opacity: 0;
  }

  /* 全屏导航显示时，折叠按钮第三条线样式 */
  header.open .burger-line3 {
    transform: rotate(-45deg) translate(3px, -5px);
  }
  /* 全屏导航显示时，logo样式 */
  header.open .logo {
    color: var(--text-color-darker);
    z-index: 40;
  }

  /* 全屏导航显示时，导航菜单样式 */
  header.open nav {
    display: grid;
    /* 每行高度为内容的高度，不设置会平分全屏高度 */
    grid-auto-rows: max-content;
    /* 菜单项靠右对齐 */
    justify-self: end;
    justify-items: end;
    position: absolute;
    top: 0;
    left: 0;
    background: white;
    width: 100vw;
    height: 100vh;
    padding: 0 40px;
    opacity: 0;
    /* 下滑效果 */
    animation: slideDown 0.6s ease-out forwards;
  }
  /* 全屏导航显示时，导航菜单项样式和动画 */
  header.open nav > * {
    margin: 4px 0;
    font-size: 18px;
    color: var(--text-color-darker);
    opacity: 0;
    animation: showMenu 0.5s linear forwards 0.4s;
  }

  /* 搜索按钮 */
  header.open nav > i.fas {
    margin-top: 10px;
    color: var(--text-color-darker);
  }

  /* 导航下滑动画 */
  @keyframes slideDown {
    from {
      height: 0;
      opacity: 0;
    }
    to {
      height: 100vh;
      padding-top: 80px;
      opacity: 1;
    }
  }

  /* 菜单项动画 */
  @keyframes showMenu {
    from {
      opacity: 0;
      transform: translateY(-1vh);
    }
    to {
      opacity: 1;
    }
  }
  /* 缩小业务流程标题字体 */
  .service-item .service-title {
    font-size: 20px;
  }
  /* 缩小业务流程内容字体和行距 */
  .service-item .service-content {
    font-size: 14px;
    line-height: 24px;
  }

  /* 团队成员改为两列 */
  .team-members {
    grid-template-columns: repeat(2, 1fr);
    column-gap: 6vw;
    row-gap: 36px;
  }
  /* 公司动态改为两列 */
  .activities {
    grid-template-columns: repeat(2, 1fr);
    row-gap: 36px;
  }
}

/* 小于992象素时 */
@media (max-width: 992px) {
  /* 轮播标题字号缩小  */
  .slide-caption h1 {
    font-size: 48px;
  }

  .slide-caption h3 {
    font-size: 18px;
  }

  /* 关于我们和业务流程设置为两列布局 */
  .features,
  .services {
    grid-template-columns: repeat(2, 1fr);
    /* 取消两行布局 */
    grid-template-rows: unset;
  }
  /* 数据部分设置为两列布局 */
  .data-section {
    /* 每列最小宽度为200象素，最大为自动 */
    grid-template-columns: repeat(2, minmax(200px, auto));
    row-gap: 24px;
    height: auto;
    padding: 24px 0;
    background-size: 200%;
  }
  /* 成功案例图片设置为3列 */
  .showcases .case-item {
    width: calc(100vw / 3);
  }
}

/* 小于768象素时 */
@media (max-width: 768px) {
  /* 区域的左右内边距设置为40象素 */
  section,
  .footer-menus {
    padding: 0 40px;
  }

  /* 关于我们和业务流程设置为1列 */
  .features,
  .services {
    grid-template-columns: 1fr;
  }

  /* 团队成员设置为1列 */
  .team-members {
    grid-template-columns: minmax(200px, 400px);
    /* column-gap: 6vw;
      row-gap: 36px; */
  }

  /* 数据部分设置为1列 */
  .data-section {
    grid-template-columns: 1fr;
    /* 增加背景尺寸 */
    background-size: 320%;
  }
  /* 公司动态设置为1列 */
  .activities {
    grid-template-columns: 1fr;
    row-gap: 36px;
  }

  /* 成功案例图片显示为两列 */
  .showcases .case-item {
    width: calc(100vw / 2);
    height: 30vw;
  }

  /* 底部菜单设置为3列 */
  .footer-menus {
    grid-template-columns: 2fr repeat(2, 1fr);
    row-gap: 24px;
  }

  /* 联系我们占前两列 */
  .contact-us {
    grid-row: 1 / 2;
  }

  /* 菜单文字靠右对齐 */
  .footer-menu {
    text-align: right;
  }
}

/* 小于576象素 */
@media (max-width: 576px) {
  /* 缩小轮播标题文字，探索更多按钮文字 */
  .slide-caption h1 {
    font-size: 28px;
  }

  .slide-caption h3 {
    font-size: 14px;
  }

  .explore-btn {
    padding: 8px 18px;
    font-size: 14px;
  }

  /* 成功案例显示为1列 */
  .showcases .case-item {
    width: 100vw;
    height: 60vw;
  }

  /* 底部菜单显示为1列 */
  .footer-menus {
    grid-template-columns: 1fr;
    /* row-gap: 24px; */
  }

  /* 底部菜单左对齐 */
  .footer-menu {
    justify-self: start;
    text-align: left;
  }
  .swiper-container {
    width: 100%;
    height: 100vh; /* 或者根据需要设置高度 */
    position: relative;
  }

  .swiper-slide {
    position: relative;
  }

  .slide-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
    z-index: 1;
  }

  .slide-caption {
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.5); /* 文字背景遮罩 */
    border-radius: 5px;
  }

  .swiper-wrapper-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
