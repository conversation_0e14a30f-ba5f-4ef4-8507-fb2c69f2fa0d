<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 23:21:09
 * @FilePath: /bid/src/views/hall/bidder/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <div slot="header">
        <span>项目名称:{{ projectName }}</span>
      </div>
      <div>
        <div style="padding: 0px 20%">
          <el-row
            type="flex"
            justify="space-between"
            align="middle"
            style="margin-bottom: 20px"
          >
            <el-col :span="12">
              <div>
                <span>招标信息: </span>
                <el-link type="primary" style="font-size: 16px" :href="fileUrl"
                  >招标文件下载</el-link
                >
              </div>
            </el-col>
            <el-col :span="12" style="text-align: left">
              <div>
                <span>开标信息:</span>

                <el-button
                  type="text"
                  style="font-size: 16px"
                  @click="handleViewList"
                  >开标一览表</el-button
                >
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <div>
                <span>温馨提示:</span>
                <el-button
                  type="text"
                  style="font-size: 16px"
                  @click="handleTip"
                  >查看</el-button
                >
              </div></el-col
            >
            <!-- <el-col :span="12" style="text-align: left">
              <div>
                <span>评标办法:</span>
                <el-button
                  type="text"
                  style="font-size: 16px"
                  @click="handleShowRule"
                  >查看</el-button
                >
              </div></el-col
            > -->
          </el-row>
        </div>
        <el-table
          :data="tableData"
          border
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column
            type="index"
            label="序号"
            width="50"
            align="center"
          ></el-table-column>
          <el-table-column prop="name" label="投标人"></el-table-column>
          <el-table-column label="文件">
            <template slot-scope="scope">
              <el-link
                type="primary"
                v-for="fileUrl in scope.row.urls"
                :href="fileUrl.path"
                :key="fileUrl.name"
              >
                {{ fileUrl.name }}</el-link
              ><br />
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-card style="margin-top: 10px">
      <div slot="header">
        <span>评标管理</span>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane
          v-for="item in auditTable"
          :label="item.name"
          :name="item.name"
          :key="item.name"
        >
          <el-table :data="item.bid_score" border="" style="width: 100%">
            <el-table-column
              type="index"
              label="序号"
              width="50"
            ></el-table-column>
            <el-table-column prop="user.name" label="评标人" width="180">
            </el-table-column>
            <el-table-column label="商务标" prop="business_score">
            </el-table-column>
            <el-table-column label="技术标" prop="technology_score">
            </el-table-column>
            <el-table-column label="价格标" prop="price_score">
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    <div style="text-align: center; margin-top: 10px">
      <el-button @click="handleBack">关 闭</el-button>
    </div>
    <el-dialog
      title="开标一览表"
      :visible.sync="visible"
      width="80%"
      :modal="false"
    >
      <!-- 公司 联系方式  投标报价  工期 -->
      <el-table :data="openList" border style="width: 100%; margin-top: 20px">
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column prop="company_name" label="公司"></el-table-column>
        <el-table-column prop="phone" label="联系方式"> </el-table-column>
        <el-table-column prop="bid_price" label="投标报价"> </el-table-column>
        <el-table-column prop="duration" label=" 工期"> </el-table-column>
        <el-table-column
          prop="bid_two_price"
          label="二次报价(元)"
        ></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getOpenBidList } from "@/api/home/<USER>";
export default {
  components: {},
  props: {
    fileData: {
      type: Object,
      default: () => {},
    },
    bid_segment_id: {
      type: Number,
      default: () => 0,
    },
  },
  data() {
    return {
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      projectName: "",
      visible: false,
      openList: [],
      fileUrl: "",
      auditTable: [],
      activeName: "",
      bidList: [],
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  watch: {
    fileData: {
      handleShowRule() {
        this.$emit("show");
      },
      handler(val) {
        if (val) {
          this.projectName = val.bidding_project_name;
          this.fileUrl = `http://bidding.senmoio.cn/${val.attachments[0].path}`;
          let fileTable = val.bid_document_submissions;
          this.tableData = [];
          this.auditTable = val.bid_document_submissions;
          if (val.bid_document_submissions.length > 0) {
            console.log(val.bid_document_submissions[0].name);

            this.activeName = val.bid_document_submissions[0].name;
          }
          fileTable.forEach((item) => {
            let urls = [];
            item.attachments.forEach((files) => {
              let path = `http://bidding.senmoio.cn/${files.path}`;
              let obj = {
                path,
                name: files.name,
              };
              urls.push(obj);
            });
            let obj = {
              name: item.name,
              urls,
            };
            this.tableData.push(obj);
          });
        }
      },
    },
  },
  methods: {
    handleBack() {
      this.$emit("back");
    },
    handleViewList() {
      let params = {
        bid_segment_id: this.bid_segment_id,
      };
      this.visible = true;
      getOpenBidList(params).then((res) => {
        this.openList = res.data;
        console.log(res);
      });
    },
    handleTip() {
      let text = `<p>本人已经阅读并完全理解《易招天成平台保密制度》的含义和要求，并庄严承诺:</p>
      <p>1、严格遵守《中华人民共和国保守国家秘密法》和《易招天成平台保密制度》各项规定，坚决不做违反招标保密的事情。</p> 
      <p>2、如果本人违反上述保密承诺，愿意按照国家法律和易招天成平台保密制度有关规定接受处罚;如果因为本人的不当行为而导致易招天成平台遭受来自第三方的投诉，本人愿意承担易招天成平台为应诉而产生的一切后果。</p>
      <p>3、本承诺书由易招天成平台招标监管机构留存、本承诺自签订之日起生效，并长期有效。</p>`;
      this.$alert(text, "温馨提示", {
        confirmButtonText: "确定",
        dangerouslyUseHTMLString: true,
      });
    },
    handleInputBlur(row, prop) {
      let value = parseInt(row[prop], 10);
      if (!isNaN(value)) {
        if (value < 0) {
          value = 1;
        }
        if (value > 100) {
          value = 100;
        }
        this.$set(row, prop, value);
      } else {
        this.$set(row, prop, 1);
      }
    },
    handleSubmit() {
      this.$emit("submit", this.auditTable);
    },
  },
  created() {},
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
