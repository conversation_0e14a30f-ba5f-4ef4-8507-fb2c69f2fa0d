<template>
  <div class="main">
    <el-collapse v-model="collapse">
      <el-collapse-item title="项目信息" name="1">
        <el-form
          ref="form"
          :model="segmentForm"
          label-width="230px"
          status-icon
          size="mini"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="招标项目编号:" prop="bidding_project_code">
                <el-input
                  disabled
                  v-model="segmentForm.bidding_project_code"
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="招标项目名称:" prop="bidding_project_name">
                <el-input
                  disabled
                  v-model="segmentForm.bidding_project_name"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="标段编号:" prop="bid_segment_code">
                <el-input
                  disabled
                  v-model="segmentForm.bid_segment_code"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标段名称:" prop="bid_segment_name">
                <el-input
                  disabled
                  v-model="segmentForm.bid_segment_name"
                ></el-input> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
      </el-collapse-item>

      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="专家提出的问题" name="first">
          <el-button type="primary" @click="handleAdd" v-if="addShow"
            >添加澄清问题</el-button
          >
          <el-table :data="tableData" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="title"
              label="澄清问题标题"
            ></el-table-column>
            <el-table-column
              prop="created_at"
              width="180"
              show-overflow-tooltip
              label="澄清问题提出时间"
            >
            </el-table-column>
            <el-table-column width="100" prop="status" label="状态">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.process_state) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="expert.name"
              width="180"
              show-overflow-tooltip
              label="澄清问题提出人"
            >
            </el-table-column>

            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="150"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleView(scope.row)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
        <el-tab-pane label="投标人回复/澄清" name="second">
          <el-button type="primary" v-if="answerShow" @click="handleAdd1"
            >回复/提出澄清</el-button
          >
          <el-table :data="tableData1" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="title"
              label="澄清问题标题"
            ></el-table-column>
            <el-table-column
              prop="created_at"
              width="180"
              show-overflow-tooltip
              label="澄清问题提出时间"
            >
            </el-table-column>
            <el-table-column width="100" prop="status" label="状态">
              <template slot-scope="scope">
                <span>{{ setResStatusText(scope.row.status) }}</span>
              </template>
            </el-table-column>

            <el-table-column
              label="操作"
              fixed="right"
              align="center"
              width="150"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="primary"
                  @click="handleView1(scope.row)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total1"
              :page-size="queryForm1.per_page"
              :current-page="queryForm1.page"
              @size-change="handleChangeSize1"
              @current-change="handleChangePage1"
            >
            </el-pagination>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-collapse>
    <div style="text-align: center; margin-top: 10px">
      <el-button @click="handleBack">返 回</el-button>
    </div>
    <el-dialog
      title="问题澄清"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
      :modal="false"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="230px"
        status-icon
        size="mini"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="澄清标题:" prop="title">
              <el-input
                :disabled="isDisabled"
                v-model="form.title"
              ></el-input> </el-form-item
          ></el-col>
        </el-row>
        <el-form-item label="针对投标人:" prop="company_info_ids">
          <el-select
            :disabled="isDisabled"
            v-model="form.company_info_ids"
            multiple
            placeholder="请选择"
          >
            <el-option
              v-for="item in segmentForm.bid_document_submissions"
              :key="item.company_info_id"
              :label="item.name"
              :value="item.company_info_id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item label="澄清问题内容:" prop="content">
              <el-input
                :disabled="isDisabled"
                type="textarea"
                v-model="form.content"
              ></el-input> </el-form-item
          ></el-col>
        </el-row>
        <el-form-item label="附件:" prop="attachment" v-if="isDisabled">
          <el-upload
            ref="upload1"
            action="#"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-form-item>
        <el-form-item label="附件:" prop="attachment" v-else>
          <el-upload
            v-model="form.attachment"
            ref="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件说明:" prop="file_description">
              <el-input
                :disabled="isDisabled"
                type="textarea"
                v-model="form.file_description"
              ></el-input> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{
          isDisabled ? "关 闭" : "取 消"
        }}</el-button>
        <!-- <el-button type="primary" v-if="!isDisabled" @click="handleSave"
          >保存</el-button
        > -->
        <el-button type="primary" v-if="!isDisabled" @click="handleSubmit"
          >提交</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      :modal="false"
      v-if="visible1"
      title="回复澄清"
      :visible.sync="visible1"
      width="80%"
      :before-close="handleClose1"
    >
      <el-form
        ref="form1"
        :model="form1"
        :rules="rules1"
        label-width="230px"
        status-icon
        size="mini"
      >
        <el-form-item label="澄清对象:" prop="expert_clarification_id">
          <el-select
            :disabled="!answerShow || isDisabled1"
            v-model="form1.expert_clarification_id"
            placeholder="请选择"
          >
            <el-option
              v-for="item in optionList"
              :key="item.id"
              :label="item.title"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="澄清标题:" prop="title">
          <el-input
            :disabled="!answerShow || isDisabled1"
            v-model="form1.title"
          ></el-input>
        </el-form-item>

        <el-form-item label="澄清内容:" prop="content">
          <el-input
            :disabled="!answerShow || isDisabled1"
            type="textarea"
            v-model="form1.content"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="附件:"
          prop="attachment"
          v-if="!answerShow || isDisabled1"
        >
          <el-upload
            ref="upload1"
            action="#"
            :file-list="fileList1"
            :on-preview="handlePreview1"
            :before-remove="handleRemove1"
          >
          </el-upload>
        </el-form-item>
        <el-form-item label="附件:" prop="attachment" v-else>
          <el-upload
            v-model="form1.attachment"
            ref="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList1"
            :on-remove="handleRemove2"
            :on-success="handleSuccess2"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item label="文件说明:" prop="file_description">
              <el-input
                :disabled="isDisabled1"
                type="textarea"
                v-model="form1.file_description"
              ></el-input> </el-form-item
          ></el-col>
        </el-row>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
        v-if="!answerShow || isDisabled1"
      >
        <el-button @click="handleClose1">关 闭</el-button>
      </span>
      <span slot="footer" class="dialog-footer" v-else>
        <el-button @click="handleClose1">取 消</el-button>
        <el-button type="primary" @click="handleSave1">保存</el-button>
        <el-button type="primary" @click="handleSubmit1">发出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import store from "@/store";
import { getToken } from "@/utils/auth";
import {
  getQuestionList,
  addQuestion,
  getQuestionById,
  responsesQuestion,
  getResponsesList,
  getResponsesById,
  updateResponsesById,
} from "@/api/hall/index";
export default {
  name: "ProjectForm",
  components: {},
  props: {
    segmentForm: {
      type: Object,
      default: () => {},
    },
    init: {
      type: Boolean,
      default: () => false,
    },
  },
  watch: {
    segmentForm: {
      handler(val) {
        if (val) {
          this.getList();
        }
      },
      deep: true,
    },
    init: {
      handler(val) {
        if (val) {
          this.activeName = "first";
          this.getList();
        }
      },
      deep: true,
    },
  },
  computed: {
    addShow() {
      console.log(this.segmentForm);
      let user = store.getters.user;
      let index = user.roles.indexOf(5);
      if (index == -1) {
        return false;
      } else {
        return true;
      }
    },
    answerShow() {
      let user = store.getters.user;
      let index = user.roles.indexOf(4);
      console.log(index);
      if (index == -1) {
        return false;
      } else {
        return true;
      }
    },
  },
  created() {
    this.activeName = "first";
    this.getList();
  },
  mounted() {},
  data() {
    return {
      type: 1,
      visible: false,
      visible1: false,
      optionList: [],
      fileList: [],
      fileList1: [],
      total: 0,
      total1: 0,
      activeName: "first",
      queryForm: {
        page: 1,
        per_page: 10,
      },
      queryForm1: {
        page: 1,
        per_page: 10,
      },
      resQueryForm: {
        page: 1,
        per_page: 20,
      },
      tableData: [],
      tableData1: [],
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      isDisabled: false,
      isDisabled1: false,
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5"],
      form: {
        title: null,
        content: null,
        bid_segment_id: null,
        attachment: [],
        status: 2,
      },
      form1: {
        title: null,
        content: null,
        bid_segment_id: null,
        attachment: [],
        expert_clarification_id: [],
        status: 1,
        type: 1,
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      companyList: [],
      rules: {
        title: [
          {
            required: true,
            message: "请输入澄清标题",
            trigger: ["blur", "change"],
          },
        ],
        content: [
          {
            required: true,
            message: "请输入澄清内容",
            trigger: ["blur", "change"],
          },
        ],
        company_info_ids: [
          {
            required: true,
            message: "请选择针对投标人",
            trigger: ["blur", "change"],
          },
        ],
      },
      rules1: {
        title: [
          {
            required: true,
            message: "请输入澄清标题",
            trigger: ["blur", "change"],
          },
        ],
        content: [
          {
            required: true,
            message: "请输入澄清内容",
            trigger: ["blur", "change"],
          },
        ],
        method: [
          {
            required: true,
            message: "请选择澄清提出方式",
            trigger: ["blur", "change"],
          },
        ],

        expert_clarification_id: [
          {
            required: true,
            message: "请选择澄清对象",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  methods: {
    handleView(data) {
      getQuestionById(data.id).then((res) => {
        this.visible = true;
        this.isDisabled = true;
        this.fileList = [];
        this.form = { ...res };
        if (this.form.attachment) {
          this.form.attachment.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },
    handleView1(data) {
      if (data.status === 2) {
        this.getResList(null);
      } else {
        this.getResList(1);
      }
      getResponsesById(data.id).then((res) => {
        this.visible1 = true;
        this.fileList = [];
        this.form1 = { ...res.data };
        if (this.form1.status === 2) {
          this.isDisabled1 = true;
        } else {
          this.isDisabled1 = false;
        }
        if (this.form1.attachment) {
          this.form1.attachment.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList1.push(obj);
          });
        }
      });
    },
    handleChange(val) {
      this.$refs.form1.clearValidate();
      if (val === 1) {
        this.getResList(1);
      }
      if (val === 2) {
        this.rules1.expert_clarification_id = [];
      } else {
        this.rules1.expert_clarification_id.push({
          required: true,
          message: "请选择澄清对象",
          trigger: ["blur", "change"],
        });
        this.$forceUpdate();
      }
    },
    handleAdd() {
      this.visible = true;
      this.isDisabled = false;
      this.form = {};
      this.form.attachment = [];
      this.fileList = [];
    },
    handleAdd1() {
      this.visible1 = true;
      this.isDisabled1 = false;
      this.form1 = {};
      this.form1.attachment = [];
      this.fileList1 = [];
      this.getResList(1);
    },
    handleClick(val) {
      if (this.activeName === "first") {
        this.getList();
      } else {
        this.getList1();
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleChangeSize1(number) {
      this.queryForm1.page = 1;
      this.queryForm1.per_page = number;
      this.getList1();
    },
    handleChangePage1(page) {
      this.queryForm1.page = page;
      this.getList1();
    },
    getList() {
      console.log(this.segmentForm.bid_segment_id);
      if (!this.segmentForm.bid_segment_id) {
        return;
      }
      this.queryForm.bid_segment_id = this.segmentForm.bid_segment_id;
      getQuestionList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    getResList(status) {
      this.resQueryForm.bid_segment_id = this.segmentForm.bid_segment_id;
      this.resQueryForm.per_page = 20;
      this.resQueryForm.process_state = status;
      getQuestionList(this.resQueryForm).then((res) => {
        this.optionList = res.data.data;
      });
    },
    getList1() {
      this.queryForm1.bid_segment_id = this.segmentForm.bid_segment_id;
      getResponsesList(this.queryForm1).then((res) => {
        this.tableData1 = res.data.data;
        this.total1 = res.data.total;
      });
    },
    handleBack() {
      this.$emit("back", 2);
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handlePreview1(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 1:
          text = "待处理";
          break;
        case 2:
          text = "已回复";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    setResStatusText(value) {
      let text = "";
      switch (value) {
        case 1:
          text = "待发出";
          break;
        case 2:
          text = "已发出";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },

    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$refs.form.resetFields();
          this.visible = false;

          //   done();
        })
        .catch((_) => {});
    },
    handleClose1(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$refs.form1.resetFields();
          this.visible1 = false;
          //   done();
        })
        .catch((_) => {});
    },
    handleSave() {},
    handleSubmit() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.bid_segment_id = this.segmentForm.bid_segment_id;
          this.form.status = 2;
          addQuestion(this.form).then((res) => {
            this.visible = false;
            this.getList();
            this.message(res.status_code, res.message);
          });
        }
      });
    },
    handleSave1() {
      console.log(this.form1);
      this.$refs.form1.validate((valid) => {
        if (valid) {
          this.form1.bid_segment_id = this.segmentForm.bid_segment_id;
          this.form1.status = 1;
          if (this.form1.id) {
            updateResponsesById(this.form1.id, this.form1).then((res) => {
              this.visible1 = false;
              this.getList1();
              this.message(res.status_code, res.message);
            });
          } else {
            responsesQuestion(this.form1).then((res) => {
              this.visible1 = false;
              this.getList1();
              this.message(res.status_code, res.message);
            });
          }
        }
      });
    },
    handleSubmit1() {
      console.log(this.form1);
      this.$refs.form1.validate((valid) => {
        if (valid) {
          this.form1.bid_segment_id = this.segmentForm.bid_segment_id;
          this.form1.status = 2;
          if (this.form1.id) {
            updateResponsesById(this.form1.id, this.form1).then((res) => {
              this.visible1 = false;
              this.getList1();
              this.message(res.status_code, res.message);
            });
          } else {
            responsesQuestion(this.form1).then((res) => {
              this.visible1 = false;
              this.getList1();
              this.message(res.status_code, res.message);
            });
          }
        }
      });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachment.splice(index, 1);
      }
    },
    handleSuccess2(res, file, applicaList) {
      this.form1.attachment.push(res.data);
      console.log(res, file, applicaList);
    },
    handleRemove2(file, applicaList) {
      let index = this.form1.attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form1.attachment.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
.main {
  padding: 10px;
}
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
