<template>
  <el-form
    :model="form"
    label-width="150px"
    @submit.native.prevent="handleSubmit"
  >
    <el-row :gutter="20" v-for="(row, rowIndex) in visibleRows" :key="rowIndex">
      <el-col :span="colSpan" v-for="(field, index) in row" :key="index">
        <el-form-item :label="field.label">
          <component
            :is="field.component"
            v-model="form[field.prop]"
            v-bind="field.props"
            v-on="field.events"
          >
          </component>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- <el-row>
      <el-col :span="24" style="text-align: center; margin-top: 10px"> </el-col>
    </el-row> -->
    <el-row :gutter="20">
      <el-col :span="24" style="text-align: center">
        <el-button type="primary" @click="handleSubmit">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button v-if="hasMoreRows" :gutter="20" @click="toggleCollapse">{{
          collapsed ? "展开" : "收起"
        }}</el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
export default {
  props: {
    fields: {
      type: Array,
      required: true,
      default: () => [],
    },
    itemsPerRow: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      form: {},
      collapsed: false,
    };
  },
  computed: {
    colSpan() {
      return 24 / this.itemsPerRow;
    },
    allRows() {
      const rows = [];
      const fieldsCopy = [...this.fields];
      while (fieldsCopy.length > 0) {
        rows.push(fieldsCopy.splice(0, this.itemsPerRow));
      }
      return rows;
    },
    visibleRows() {
      return this.collapsed ? [this.allRows[0]] : this.allRows;
    },
    hasMoreRows() {
      return this.allRows.length > 1;
    },
  },
  watch: {
    fields: {
      immediate: true,
      handler(newFields) {
        this.form = newFields.reduce((acc, field) => {
          acc[field.prop] = field.default || null;
          return acc;
        }, {});
      },
    },
  },
  methods: {
    handleSubmit() {
      this.$emit("search", { ...this.form });
    },
    handleReset() {
      this.fields.forEach((field) => {
        this.form[field.prop] = field.default || null;
      });
      this.handleSubmit(); // 重置后重新触发搜索
    },
    toggleCollapse() {
      this.collapsed = !this.collapsed;
    },
  },
};
</script>

<style scoped>
/* 保持每一行的搜索条件宽度一致 */
.el-col {
  padding: 10px;
  box-sizing: border-box;
}

/* 确保表单项的宽度一致 */
.el-form-item {
  margin-bottom: 0; /* 移除默认的底部间距 */
}

/* 自定义输入框的宽度 */
.el-input,
.el-select,
.el-date-editor {
  width: 100%;
}
</style>
