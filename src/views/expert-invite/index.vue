<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: 王子超
 * @LastEditTime: 2024-12-03 23:51:32
 * @FilePath: /bidding-web/src/views/expert-invite/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bidding_project.bidding_project_name"
          label="项目名称"
        >
        </el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project.bidding_project_code"
          label="项目编号"
        ></el-table-column>

        <el-table-column
          prop="segment_name"
          show-overflow-tooltip
          width="180"
          label="标段名称"
        >
          <template slot-scope="scope">
            {{
              scope.row.bid_segments.length > 0
                ? scope.row.bid_segments[0].segment_name
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="opening_date"
          label="开标时间"
        ></el-table-column>

        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="evaluation_date"
          label="评标时间"
        ></el-table-column>

        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="tender_agency"
          label="招标代表机构"
        ></el-table-column>

        <el-table-column
          show-overflow-tooltip
          prop="investment_amount"
          label="标段投资额"
        ></el-table-column>

        <el-table-column width="100" prop="submission_status" label="状态">
          <template slot-scope="scope">
            <span>{{
              setStatusText(scope.row.expert_selection_log[0].selection_status)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import { getPromiseList, expertPromise } from "@/api/expert/index";
import { detailApproval } from "@/api/bid-manage/extract";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";
export default {
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      applicaList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "招标项目名称",
          prop: "project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "状态",
          prop: "selection_status",
          component: "el-select",
          props: {
            placeholder: "请选择状态",
          },
          options: [
            { label: "全部", value: null },
            { label: "已接受", value: 1 },
            { label: "拒绝", value: 2 },
          ],
        },
        {
          label: "标段名称：",
          prop: "segments_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
        {
          label: "标段编号",
          prop: "segments_code",
          component: "el-input",
          props: {
            placeholder: "请输入标段编号",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleDetail(data) {
      this.addShow = true;
      detailApproval(data.id).then((res) => {
        let data = res.data;
        this.editForm = data;
        this.editForm.segments = data.bid_segments;
      });
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleUpdate(data) {},
    getList() {
      getPromiseList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleSubmit(data) {
      expertPromise(data).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "未接受";
          break;
        case 1:
          text = "已接受";
          break;
        case 2:
          text = "拒绝";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
