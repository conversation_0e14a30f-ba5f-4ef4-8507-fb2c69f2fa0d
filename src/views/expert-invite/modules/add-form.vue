<template>
  <div>
    <el-dialog
      title="专家项目详情"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="标段" name="2">
          <div>
            <!-- <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addBidSegment"
                >选择标段</el-button
              >
            </div> -->
            <el-table
              :data="form.segments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" prop="segment_number">
              </el-table-column>
              <el-table-column label="标段名称" prop="segment_name">
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="开评标信息" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="开标时间:" prop="opening_date">
                  <div style="display: flex">
                    <el-date-picker
                      v-model="form.opening_date"
                      type="datetime"
                      disabled
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开标地点:" prop="opening_location">
                  <el-select
                    v-model="form.opening_location"
                    placeholder="请选择"
                    disabled
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="评标时间:" prop="evaluation_date">
                  <div style="display: flex">
                    <el-date-picker
                      disabled
                      v-model="form.evaluation_date"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评标地点:" prop="evaluation_location">
                  <el-select
                    v-model="form.evaluation_location"
                    placeholder="请选择"
                    disabled
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="注意事项:" prop="notes">
                  <el-input
                    type="textarea"
                    disabled
                    v-model="form.notes"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="单位信息" name="4">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位:" prop="owner_unit">
                  <el-input
                    v-model="form.owner_unit"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="owner_representative">
                  <el-input
                    v-model="form.owner_representative"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="owner_representative_phone">
                  <el-input
                    v-model="form.owner_representative_phone"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标代理机构:" prop="tender_agency">
                  <el-input
                    v-model="form.tender_agency"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="agency_representative">
                  <el-input
                    v-model="form.agency_representative"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="agency_representative_phone">
                  <el-input
                    v-model="form.agency_representative_phone"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="监督单位:" prop="supervision_unit">
                  <el-input
                    v-model="form.supervision_unit"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="supervisor">
                  <el-input
                    v-model="form.supervisor"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="supervisor_phone">
                  <el-input
                    v-model="form.supervisor_phone"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="招标内容:" prop="tender_content">
                  <el-input
                    v-model="form.tender_content"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="抽取要求" name="5">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="240px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目所在地:" prop="project_location">
                  <el-cascader
                    v-model="form.project_location"
                    :props="addressProps"
                    :options="addressOptions"
                    disabled
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="抽取终端:" prop="extraction_terminal">
                  <el-select
                    v-model="form.extraction_terminal"
                    disabled
                    placeholder="请选择"
                  >
                    <el-option key="0" label="随机抽取" value="0"> </el-option>
                    <el-option key="1" label="逐条抽取" value="1"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="本次抽取项目或标段投资额（元）:"
                  prop="investment_amount"
                >
                  <el-input
                    v-model="form.investment_amount"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="拟抽取日期:"
                  prop="intended_selection_date"
                >
                  <div style="display: flex">
                    <el-date-picker
                      v-model="form.intended_selection_date"
                      disabled
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item
                  label="评标委员会人数:"
                  prop="committee_member_count"
                >
                  <el-input
                    v-model="form.committee_member_count"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
          <div>
            <el-table
              :data="form.expert_selection_conditions"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="地区" width="180">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.geo_area_id"
                    disabled
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="专家类别">
                <template slot-scope="scope">
                  <el-cascader
                    v-model="scope.row.expert_type"
                    disabled
                    :props="expertProps"
                    :options="expertOptions"
                    placeholder="请选择"
                  ></el-cascader>
                </template>
              </el-table-column>
              <el-table-column label="专家人数" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model.number="scope.row.expert_count"
                    disabled
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="报到时间" width="230">
                <template slot-scope="scope">
                  <div style="display: flex">
                    <el-date-picker
                      v-model="scope.row.report_time"
                      disabled
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报到地点" width="180">
                <template slot-scope="scope"
                  ><el-input
                    v-model="scope.row.report_location"
                    disabled
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="评标用时（天）" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model.number="scope.row.evaluation_duration"
                    disabled
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="抽取状态" width="180">
                <template slot-scope="scope">
                  {{ scope.row.selection_status ? "已完成" : "未完成" }}
                </template>
              </el-table-column> -->
              <el-table-column label="专家姓名" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.expertList"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>

      <span slot="footer" class="dialog-footer">
        <el-button
          v-if="
            form.expert_selection_conditions[0].expert_selection_logs[0]
              .selection_status === 0
          "
          type="primary"
          @click="handleSubmit(1)"
          >接受邀请</el-button
        >
        <el-button
          v-if="
            form.expert_selection_conditions[0].expert_selection_logs[0]
              .selection_status === 0
          "
          type="warning"
          @click="handleSubmit(2)"
          >拒绝邀请</el-button
        >

        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBidProjectList,
  getBidProjectSegments,
  generateAnnouncement,
} from "@/api/bid-manage/bid-project";
import { getGeoAreas } from "@/api/bid-manage/project-info";
import { getExpertTypes } from "@/api/expert/expert";
import { extractExpertList, getCity } from "@/api/bid-manage/extract";
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import Tinymce from "@/components/Tinymce";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          val.expert_selection_conditions?.forEach((item) => {
            console.log();

            item.experts = item.expert_selection_logs.filter((v) => {
              return (v.expert_selection_condition_id = item.id);
            });
            item.expertList = item.experts
              ?.map((j) => {
                return j.expert_name;
              })
              .join(",");
          });
          this.form = { ...val };
          this.form.bidding_project_name =
            val.project_detail[0].bidding_project_name;
          this.form.bidding_project_code =
            val.project_detail[0].bidding_project_code;
          this.queryFormSegment.bidding_project_id =
            this.form.bidding_project_id;
          if (val.submission_status !== 1) {
            this.isDisabled = true;
          }
          console.log(this.form);
        }
      },
      deep: true,
    },
  },
  created() {},
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      auditForm: {},
      isDisabled: false,
      addressProps: {
        label: "name",
        value: "id",
      },
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        bidding_project_name: null,
        bidding_project_code: null,
        opening_date: null,
        opening_location: null,
        evaluation_date: null,
        evaluation_location: null,
        notes: null,
        owner_unit: null,
        owner_representative: null,
        owner_representative_phone: null,
        tender_agency: null,
        agency_representative: null,
        agency_representative_phone: null,
        supervision_unit: null,
        supervisor: null,
        supervisor_phone: null,
        tender_content: null,
        project_location: null,
        extraction_terminal: null,
        investment_amount: null,
        intended_selection_date: null,
        committee_member_count: null,
        segments: [],
        expert_selection_conditions: [],
        submission_status: 0,
      },
      innerVisible: false,
      selectSegmentShow: false,
      expertVisible: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      expertIndex: 0,
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      tableDataExpert: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {},
      expertProps: {
        label: "name",
        value: "id",
      },
      selectProject: [],
      selectExpert: [],
      tableDataSegment: [],
      cityOptions: [],
      expertOptions: [],
      addressOptions: [],
      totalSegment: 0,
    };
  },
  created() {
    this.getGeoExpert();
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSelectionChange() {},
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.handleGetProjectList();
    },
    handleSubmit(val) {
      console.log(this.form);

      let obj = {
        expert_selection_log_id:
          this.form.expert_selection_conditions[0].expert_selection_logs[0].id,
        selection_status: val,
      };
      this.$emit("submit", obj);
    },
    handleGetSegmentList() {
      getBidProjectSegments(this.queryFormSegment).then((res) => {
        this.tableDataSegment = res.data.data;
        this.totalSegment = res.data.total;
      });
    },
    getGeoExpert() {
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      getExpertTypes().then((res) => {
        this.expertOptions = res.data;
      });
      getCity().then((res) => {
        this.cityOptions = res.data;
      });
    },
    handleGetProjectList() {
      getBidProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleGetExpert(index, row) {
      this.expertIndex = index;
      let params = {
        geo_area_id: row.geo_area_id,
        expert_type: row.expert_type,
        expert_count: row.expert_count,
      };
      extractExpertList(params).then((res) => {
        this.tableDataExpert = res.data.expert.data;
        console.log(this.tableDataExpert, "this.tableDataExpert");
        this.expertVisible = true;
      });
    },
    handleSelectExpert(data) {
      this.selectExpert = data;
      console.log(data);
    },
    handleSaveExpert() {
      let name = this.selectExpert.map((item) => {
        return item.name;
      });
      this.form.expert_selection_conditions[this.expertIndex].expertList =
        name.join(",");
      this.form.expert_selection_conditions[this.expertIndex].experts =
        this.selectExpert;
      this.expertVisible = false;
    },

    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
