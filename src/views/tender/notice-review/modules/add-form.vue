<template>
  <div>
    <el-dialog
      title="中标未中标公告通知书"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
      :modal="false"
    >
      <el-dialog
        v-if="this.innerVisible"
        width="80%"
        title="选择招标项目"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fields"
            :itemsPerRow="2"
            @search="handleSearch"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableData"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectProject"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="bidding_project_code"
              label="招标/采购项目编号"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="bidding_project_name"
              label="招标/采购项目名称"
            ></el-table-column>
            <el-table-column
              prop="project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSaveProject">确定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        v-if="this.selectSegmentShow"
        width="80%"
        title="选择标段"
        :visible.sync="selectSegmentShow"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fieldsSegment"
            :itemsPerRow="2"
            @search="handleSearchSegment"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableDataSegment"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectSegment"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="segment_number"
              label="标段编号"
            ></el-table-column>
            <el-table-column
              prop="segment_name"
              label="标段名称"
            ></el-table-column>
            <el-table-column
              prop="bidding_project.bidding_project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="totalSegment"
              :page-size="queryFormSegment.per_page"
              :current-page="queryFormSegment.page"
              @size-change="handleChangeSizeSegment"
              @current-change="handleChangePageSegment"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCloseSegment">取 消</el-button>
          <el-button type="primary" @click="handleSaveSegment">确定</el-button>
        </span>
      </el-dialog>
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                    <el-button
                      size="mini"
                      v-if="isEdit"
                      @click="handleShowProject"
                      >选择</el-button
                    >
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="标段" name="2">
          <div>
            <div style="text-align: right">
              <el-button
                size="mini"
                type="primary"
                v-if="isEdit"
                @click="addBidSegment"
                >选择标段</el-button
              >
            </div>
            <el-table
              :data="form.bid_segment"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" prop="segment_number">
              </el-table-column>
              <el-table-column label="标段名称" prop="segment_name">
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="中标公司" name="1">
          <el-radio-group
            v-model="form.bid_document_submission_id"
            size="small"
          >
            <el-radio
              v-for="item in companyList"
              :key="item.id"
              :label="item.id"
              border
              >{{ item.name }}</el-radio
            >
          </el-radio-group>
        </el-collapse-item>
        <el-collapse-item title="通知详情" name="2">
          <el-input type="textarea" v-model="form.details"></el-input>
        </el-collapse-item>
        <el-collapse-item title="相关附件" name="3" v-if="isEdit">
          <el-upload
            ref="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
            :limit="1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="相关附件" name="3" v-else>
          <el-upload
            ref="upload1"
            action="#"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>
        <el-collapse-item v-if="false" title="审核记录" name="4">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">
          {{ isEdit ? "取消" : "关闭" }}</el-button
        >
        <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
        <el-button type="primary" v-if="isEdit" @click="handleSubmit"
          >递交生效</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <span>
        【温馨提示：尊敬的投标人您好，查询到您未缴纳平台服务费，无法下载中标通知书，请前往缴费。】</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="handlePay">支付宝缴费</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBidProjectList,
  getBidProjectSegments,
  getBidCompanyList,
  downloadNotification,
  downloadCheck,
  goAliPay,
} from "@/api/bid-manage/bid-project";
import { getToken } from "@/utils/auth";
import Tinymce from "@/components/Tinymce";
import SearchForm from "@/components/SearchForm/index.vue";

export default {
  name: "ProjectForm",
  components: {
    Tinymce,
    SearchForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: () => false,
    },
  },
  watch: {
    editForm: {
      async handler(val) {
        if (val) {
          this.form = { ...val };
          console.log(this.form);

          await this.getCompanyList();
          this.form.publish_type = "0";
          if (this.form.attachment == null) {
            this.form.attachment = [];
          }
          this.form.bidding_project_name =
            val.bidding_project.bidding_project_name;
          this.form.bidding_project_code =
            val.bidding_project.bidding_project_code;
          this.form.qualification_review = "0";
          this.queryFormSegment.bidding_project_id =
            this.form.bidding_project_id;
        }
      },
      deep: true,
    },
    innerVisible: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
    selectSegmentShow: {
      handler(val) {
        if (val) {
          this.handleSearchSegment();
        }
      },
    },
  },
  created() {
    // let user = this.$store.getters.user;
    // let obj = {
    //   label: user.company_info.company_name,
    //   value: user.company_info.id,
    // };
    // this.companyList.push(obj);
    // console.log(user);
  },
  data() {
    return {
      dialogVisible: false,
      total: 0,
      order: {},
      companyList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      innerVisible: false,
      selectSegmentShow: false,
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4"],
      form: {
        publish_type: "0",
        status: 0,
        title: "",
        publisher: this.$store.getters.name,
        company_info_id: null,
        announcement_cate: null,
        geo_area_id: 36,
        keywords: "",
        publish_date: "",
        modify_date: "",
        source: "",
        author: "",
        content: "",
        attachment: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      tableDataSegment: [],
      loading: false,
      companyList: [],
      rules: {
        title: [
          {
            required: true,
            message: "请输入公告名称",
            trigger: ["blur", "change"],
          },
        ],
        announcement_cate: [
          {
            required: true,
            message: "请选择公告类别",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  methods: {
    handleCloseSegment() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.selectSegmentShow = false;
          //   done();
        })
        .catch((_) => {});
    },
    handlePay() {
      window.open(
        `http://bidding.senmoio.cn/alipay/pay?order_id=${this.order.id}`,
        "_blank"
      );
      // goAliPay(this.order.id).then((res) => {
      //   console.log(res);
      // });
    },
    handlePreview() {
      downloadCheck(this.form.bid_segment[0].id).then((res) => {
        console.log(res);

        if (res.data.status == 1) {
          downloadNotification(this.form.id)
            .then((response) => {
              console.log(response);
              const url = window.URL.createObjectURL(new Blob([response]));
              const link = document.createElement("a");
              link.href = url;
              link.setAttribute("download", this.form.attachment[0].name); // 指定下载文件名
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link); // 下载后移除元素
            })
            .catch((error) => {
              console.log(error);
            });
        } else {
          this.dialogVisible = true;
          this.order = res.data.order;
        }
      });
    },
    handleRemove() {
      return false;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },

    handleSave() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.bid_segment_id = this.form.bid_segment[0].id;
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.bid_segment_id = this.form.bid_segment[0].id;
          this.$emit("submit", this.form);
        }
      });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachment.splice(index, 1);
      }
    },
    handleSaveSegment() {
      this.selectSegmentShow = false;
      this.getCompanyList();
    },
    getCompanyList() {
      let params = {
        bid_segment_id: this.form.bid_segment[0].id,
      };
      console.log(params);

      getBidCompanyList(params).then((res) => {
        console.log(res);
        this.companyList = res.data;
      });
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.handleGetProjectList();
    },
    handleSearchSegment(filters) {
      Object.assign(this.queryFormSegment, filters);
      this.handleGetSegmentList();
    },
    handleGetSegmentList() {
      getBidProjectSegments(this.queryFormSegment).then((res) => {
        this.tableDataSegment = res.data.data;
        this.totalSegment = res.data.total;
      });
    },
    handleGetProjectList() {
      getBidProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetProjectList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.handleGetProjectList();
    },
    handleSelectProject(data) {
      this.selectProject = data;
      console.log(data);
    },
    handleSelectSegment(data) {
      if (data.length > 1) {
        this.$message.warning("只能选择一个标段");
        return;
      }
      this.form.bid_segment = data;
    },
    handleSaveProject() {
      if (this.selectProject.length > 1) {
        this.$message.warning("只能选择一个项目");
        return;
      }
      this.form.bidding_project_name =
        this.selectProject[0].bidding_project_name;
      this.form.bidding_project_code =
        this.selectProject[0].bidding_project_code;
      this.form.bidding_project_id = this.selectProject[0].id;
      this.queryFormSegment.bidding_project_id = this.selectProject[0].id;
      this.innerVisible = false;
      // this.handleCreate();
    },
    handleShowProject() {
      this.innerVisible = true;
    },
    addBidSegment() {
      console.log(this.form.bidding_project_name);
      if (
        this.form.bidding_project_name == "" ||
        this.form.bidding_project_name == null
      ) {
        this.$message.warning("请先选择招标项目");
        return;
      }
      this.selectSegmentShow = true;
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
