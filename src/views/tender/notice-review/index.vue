<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 20:05:21
 * @FilePath: /bid/src/views/bidding/bid-half-announcement/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button v-if="isShow" type="primary" @click="handleAdd"
        >新建</el-button
      >
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column v-if="isShow" label="编辑" align="center" width="180">
          <template slot-scope="scope">
            <!-- <el-button
              size="mini"
              type="primary"
              :disabled="scope.row.status == 1"
              @click="handleUpdate(scope.row, true)"
              >编辑</el-button
            > -->

            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project_code"
          label="招标/采购项目编号"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bidding_project_name"
          label="招标/采购项目名称"
        >
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="segment_name"
          label="标段名称"
        >
        </el-table-column>
        <el-table-column
          prop="segment_number"
          width="180"
          label="标段编号"
        ></el-table-column>
        <el-table-column
          width="180"
          prop="company"
          label="中标人"
          show-overflow-tooltip
        ></el-table-column>

        <!-- <el-table-column
          prop="publisher"
          width="120"
          label="发出人"
        ></el-table-column>
        <el-table-column
          width="180"
          prop="keywords"
          label="发出时间"
        ></el-table-column> -->
        <!-- <el-table-column width="100" prop="status" label="公告状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row, false)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>

    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :fileList="fileList"
      :isEdit="isEdit"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import {
  getNotifications,
  addNotifications,
  deleteBidHalfAnnouncements,
  updateBidHalfAnnouncements,
  getNotificationById,
  updateNotification,
  deleteNotification,
} from "@/api/bid-manage/bid-project";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";
import store from "@/store";

export default {
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      isShow: true,
      isEdit: false,
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      this.addShow = true;
      this.isEdit = true;
      this.fileList = [];
    },
    handleUpdate(data, isEdit) {
      this.addShow = true;
      this.isEdit = isEdit;
      getNotificationById(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        if (this.editForm.attachment != null) {
          this.editForm.attachment.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },
    getList() {
      getNotifications(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        updateNotification(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addNotifications(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      if (data.id) {
        updateNotification(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addNotifications(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteNotification(row.id).then((res) => {
            this.message(res.status_code, res.message);
            // this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    setTypeText(status) {
      let text = "";
      switch (status) {
        case 1:
          text = "招标公告";
          break;
        case 2:
          text = "变更公告";
          break;
        case 3:
          text = "招标候选人公示";
          break;
        case 4:
          text = "结果公告";
          break;
        case 5:
          text = "废标公告";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    let user = store.getters.user;
    if (user.roles.indexOf(3) != -1) {
      this.isShow = true;
    } else {
      this.isShow = false;
    }
    console.log(user);

    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
