<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: huiji
 * @LastEditTime: 2025-07-05 15:52:25
 * @FilePath: /bidding-web/src/views/tender/bid-track/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project.bidding_project_code"
          label="招标/采购项目编号"
        ></el-table-column>
        <el-table-column
          width="280"
          prop="bidding_project.bidding_project_name"
          show-overflow-tooltip
          label="招标/采购项目名称"
        >
        </el-table-column>

        <el-table-column
          width="280"
          prop="segment_name"
          show-overflow-tooltip
          label="标段名称"
        >
        </el-table-column>
        <el-table-column prop="segment_number" width="180" label="标段编号">
        </el-table-column>
        <el-table-column
          width="180"
          prop="bid_document[0].document_submission_deadline"
          label="投标截止日期"
        ></el-table-column>
        <el-table-column
          width="220"
          prop="password"
          label="投标文件密码"
        ></el-table-column>
        <el-table-column
          width="180"
          prop="bid_document_submissions[0].created_at"
          label="投标文件上传时间"
        ></el-table-column>
        <el-table-column
          prop="bid_document[0].document_submission_deadline"
          label="回执单"
        >
          <template slot-scope="scope">
            <el-link
              type="primary"
              @click="handleView(scope.row.bid_document_submissions)"
              >预览</el-link
            >
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" align="center" width="280">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleStatusView(scope.row)"
              >项目详情</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="handleViewHall(scope.row)"
              >进入开标大厅</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <status-view
      v-if="statusView"
      :visible="statusView"
      :code="segmentCode"
      :name="segmentName"
      :fileData="fileData"
      @click="handleClick"
      @close="statusView = false"
    ></status-view>
    <submit-file
      v-if="fileShow"
      :visible="fileShow"
      :editForm="editForm"
      :fileList="fileList"
      :segmentForm="segmentForm"
      :projectForm="projectForm"
      @submit="handleSubmitFile"
      @delete="handleDeleteLog"
      @close="fileShow = false"
    ></submit-file>
    <company-list
      v-if="listShow"
      :visible="listShow"
      :list="companyList"
      @close="listShow = false"
    ></company-list>
  </div>
</template>

<script>
import {
  getRegistrationsMyList,
  geInviteDetail,
  geInviteSegmentsDetail,
  uploadBidFile,
  getBidFileDetail,
  deleteFileLog,
} from "@/api/bid-manage/invite_bid";
import { getBidderCompanyList } from "@/api/bid-manage/bid-project";
import SearchForm from "@/components/SearchForm/index.vue";
import CompanyList from "./modules/company-list.vue";
import AddForm from "./modules/add-form.vue";
import StatusView from "./modules/status-view.vue";
import SubmitFile from "./modules/submit-file.vue";
import store from "@/store";
export default {
  components: {
    SearchForm,
    AddForm,
    StatusView,
    SubmitFile,
    CompanyList,
  },
  data() {
    return {
      listShow: false,
      companyList: [],
      segmentCode: "",
      segmentName: "",
      segmentData: null,
      fileData: {
        status: 1,
      },
      projectForm: {},
      segmentForm: {},
      statusView: false,
      addShow: false,
      fileShow: false,
      total: 0,
      editForm: null,
      fileList: [],
      applicaList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleViewHall() {
      this.$router.push({ path: "/hall/bidder" });
    },
    handleView(data) {
      if (data.length === 0) {
        this.$message.warning("回执单不存在");
        return;
      }
      let id = data[0].id;
      // 获取JWT token
      const token = this.$store.getters.token || localStorage.getItem("token");
      // 在新页签中打开回执单页面
      const url = `/api/public/bid_document_receipt/${id}?token=${token}`;
      window.open(url, "_blank");
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getRegistrationsMyList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleStatusView(data) {
      let user = store.getters.user;
      if (user.roles.indexOf(2) != -1) {
        this.listShow = true;
        let params = {
          bid_segment_id: data.id,
        };
        getBidderCompanyList(params).then((res) => {
          this.companyList = res.data;
          console.log(res);
        });
      } else {
        this.segmentName = data.segment_name;
        this.segmentCode = data.segment_number;
        this.segmentData = data;
        if (
          data.bid_document_submissions == null ||
          data.bid_document_submissions.length == 0
        ) {
          this.fileData.status = 1;
        } else {
          this.fileData.status = 2;
        }
        this.statusView = true;
        this.fileData.flow_status = data.flow_status;
        this.fileData.id = data.id;
      }
      console.log(user);
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    async handleClick(eventName) {
      console.log(eventName);
      if (eventName == "submitFile" || eventName == "cancelFile") {
        // this.statusView = false;
        await geInviteSegmentsDetail(this.segmentData.id).then((res) => {
          console.log(res);
          this.segmentForm = res.data;
          this.fileShow = true;
          if (eventName == "submitFile") {
            this.fileList = [];
          }
        });
        console.log(this.segmentData);
        await geInviteDetail(this.segmentData.id).then((res) => {
          this.projectForm = { ...res.data.registration[0] };
          console.log(this.projectForm);
        });
        if (this.segmentData.bid_document_submissions.length > 0) {
          getBidFileDetail(
            this.segmentData.bid_document_submissions[0].id
          ).then((res) => {
            this.editForm = res.data;
            if (this.editForm.tender_document != null) {
              this.fileList = [];
              this.editForm.tender_document.forEach((item) => {
                let obj = {
                  name: item.name,
                  url: item.path,
                };
                this.fileList.push(obj);
              });
            }
            console.log(this.editForm);
          });
        }
      }
    },

    handleSubmitFile(data) {
      let formData = { ...data };
      formData.status = 1;
      formData.bid_segment_id = this.segmentData.id;
      formData.bidding_project_id = this.segmentData.bidding_project.id;
      uploadBidFile(formData).then((res) => {
        this.message(res.status_code, res.message);
        this.statusView = false;
      });
    },
    handleDeleteLog(data) {
      deleteFileLog(data.id).then((res) => {
        this.message(res.status_code, res.message);
        this.statusView = false;
      });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.fileShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
