<template>
  <div>
    <el-dialog
      title="投递投标文件"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="标段信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="title">
                  <el-input
                    disabled
                    v-model="segmentForm.bidding_project.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="标段编号:" prop="title">
                  <el-input
                    disabled
                    v-model="segmentForm.segment_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目名称:" prop="bidding_project_name">
                  <el-input
                    disabled
                    v-model="segmentForm.bidding_project.bidding_project_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="segment_name">
                  <el-input
                    disabled
                    v-model="segmentForm.segment_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="报名开始时间:" prop="title">
                  <el-date-picker
                    disabled
                    v-model="segmentForm.bid_document[0].document_obtain_start"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="报名结束时间:" prop="title">
                  <el-date-picker
                    disabled
                    v-model="
                      segmentForm.bid_document[0].document_submission_deadline
                    "
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker></el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="标段内容:" prop="title">
                  <el-input
                    disabled
                    type="textarea"
                    v-model="segmentForm.segment_content"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="项目联系人信息" name="2">
          <el-form
            ref="form"
            :model="projectForm"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目联系人:" prop="project_contact_name">
                  <el-input
                    disabled
                    v-model="projectForm.project_contact_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="身份证号:" prop="id_number">
                  <el-input
                    disabled
                    v-model="projectForm.id_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系电话:" prop="project_contact_phone">
                  <el-input
                    disabled
                    v-model="projectForm.project_contact_phone"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="相关证书和编号:" prop="certificates">
                  <el-input
                    disabled
                    v-model="projectForm.certificates"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="投标文件信息" name="3">
          <el-form
            ref="form"
            :model="fileForm.values"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-form-item
                v-for="(item, index) in fieldNames"
                :key="index"
                :label="item['title']"
                :prop="item['field_name']"
              >
                <el-input
                  :disabled="fileForm.id ? true : false"
                  v-model="fileForm.values[item.field_name]"
                ></el-input>
              </el-form-item>
              <!-- <el-col :span="12">
                <el-form-item label="项目报价（元）:" prop="bid_price">
                  <el-input
                    :disabled="fileForm.id ? true : false"
                    v-model.number="fileForm.bid_price"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="项目工期（天）:" prop="duration">
                  <el-input
                    :disabled="fileForm.id ? true : false"
                    v-model.number="fileForm.duration"
                  ></el-input> </el-form-item
              ></el-col> -->
            </el-row>
          </el-form>
          <el-upload
            v-if="fileForm.id"
            ref="upload1"
            action="#"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
          <el-upload
            v-else
            ref="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">可以上传的文件后缀为zip</div>
          </el-upload>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{ "取 消" }}</el-button>
        <el-button type="primary" v-if="fileForm.id" @click="handleCancel"
          >撤销递交</el-button
        >
        <el-button type="primary" v-else @click="handleSubmit"
          >递交生效</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { getFiledNameValue } from "@/api/bid-manage/invite_bid";

export default {
  name: "ProjectForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    projectForm: {
      type: Object,
      default: () => {},
    },
    segmentForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    applicaList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        console.log(val);

        if (val) {
          this.fileForm = { ...val };
          this.fileForm.values = {};

          //   if (this.form.status == 2) {
          //     this.isDisabled = true;
          //   }
        }
        getFiledNameValue(this.segmentForm.id).then((res) => {
          this.fieldNames = res.data;
          this.fieldNames.forEach((item) => {
            this.$set(this.fileForm.values, item.field_name, item.value);
          });
          console.log(this.fieldNames);
        });
      },
      deep: true,
    },
  },
  created() {
    getFiledNameValue(this.segmentForm.id).then((res) => {
      this.fieldNames = res.data;
      this.fieldNames.forEach((item) => {
        this.$set(this.fileForm.values, item.field_name, item.value);
      });
      console.log(this.fieldNames);
    });
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      isDisabled: false,
      fileForm: {
        // bid_price: 0,
        // duration: 0,
        values: {},
        tender_document: [],
      },
      fieldNames: [],
      acceptTypes: [".zip"],
      collapse: ["1", "2", "3"],
      form: {
        bidding_project_id: null,
        bid_segment_id: null,
        attachment: [],
        registration_material: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      companyList: [],
      rules: {
        title: [
          {
            required: true,
            message: "请输入公告名称",
            trigger: ["blur", "change"],
          },
        ],
        modifier: [
          {
            required: true,
            message: "请输入公告名称",
            trigger: ["blur", "change"],
          },
        ],
        announcement_cate: [
          {
            required: true,
            message: "请选择公告类别",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  methods: {
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },

    handleSave() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.bidding_project_id = this.segmentForm.bidding_project.id;
          this.form.bid_segment_id = this.segmentForm.id;
          this.form.segment_content = this.segmentForm.segment_content;
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.fileForm);
        }
      });
    },
    handleCancel() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("delete", this.fileForm);
        }
      });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.fileForm.tender_document.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.fileForm.tender_document.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.fileForm.tender_document.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
