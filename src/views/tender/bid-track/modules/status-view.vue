<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :before-close="handleClose"
    >
      <el-row>
        <el-col :span="8"
          ><h3>标段编号: {{ code }}</h3>
        </el-col>
        <el-col :span="16"
          ><h3>标段名称: {{ name }}</h3>
        </el-col>
      </el-row>

      <el-steps
        :active="active"
        finish-status="success"
        simple
        style="margin-top: 20px"
      >
        <el-step title="投标"></el-step>
        <el-step title="开标"></el-step>
        <el-step title="定标"></el-step>
      </el-steps>
      <div class="status-list">
        <div>
          <p>
            <!-- <el-popover placement="right" title="" width="200" trigger="hover"> -->
            <!-- <div>
                <p>开始时间：</p>
                <p>
                  已递交<span style="font-size: 18px; padding: 0 10px">0/0</span
                  >需递交
                </p>
                <p>结束时间：</p>
              </div> -->
            <el-button
              v-if="fileData.status == 1"
              slot="reference"
              size="mini"
              type="primary"
              @click="handleClick('submitFile')"
              >投标文件递交</el-button
            >
            <el-button
              v-else
              slot="reference"
              size="mini"
              type="warning"
              @click="handleClick('cancelFile')"
              >撤销文件递交</el-button
            >
            <!-- </el-popover> -->
          </p>
        </div>
        <div>
          <p>
            <el-button size="mini" type="primary" @click="handleOpenHall()"
              >开标大厅</el-button
            >
          </p>
        </div>

        <div>
          <p>
            <el-button size="mini" type="primary" @click="handleViewNoctice(3)"
              >中标候选人公示</el-button
            >
          </p>
          <p>
            <el-button size="mini" type="primary" @click="handleViewNoctice(4)"
              >中标结果公告</el-button
            >
          </p>
        </div>
      </div>
      <!-- <add-form
        v-if="addShow"
        :visible="addShow"
        :editForm="editForm"
        type="add"
        @save="handleSave"
        @submit="handleSubmit"
        @close="addShow = false"
      ></add-form> -->
      <view-form
        v-if="addShow"
        :visible="addShow"
        :editForm="editForm"
        :fileList="fileList"
        @save="handleSave"
        @submit="handleSubmit"
        @close="addShow = false"
      ></view-form>
    </el-dialog>
    <el-dialog
      title="公告详情"
      width="80%"
      v-if="noticeShow"
      :visible.sync="noticeShow"
      :modal="false"
    >
      <div class="content-wrapper">
        <div class="announcement">
          <h2 style="text-align: center">
            {{ bidData.bidding_project.bidding_project_name }}
          </h2>
          <div class="details">
            <p>发布时间：{{ bidData.publish_date }}</p>
            <p>查看次数：{{ bidData.num }}</p>
            <p>
              招标/采购项目编号：{{
                bidData.bidding_project.bidding_project_code
              }}
            </p>
            <!-- <p>所属行业：{{}}</p> -->
            <p>业主单位：{{ bidData.bidding_project.owner_unit }}</p>
            <p>
              招标项目地址：{{
                bidData.bidding_project.bidding_project_location
              }}
            </p>
            <p>
              招标组织形式：{{ bidData.bidding_project.organizational_form }}
            </p>
          </div>
        </div>

        <div id="content"></div>
        <div id="file">
          附件:<a
            style="margin-right: 10px"
            v-for="item in bidData.attachments"
            :key="item.name"
            :href="'http://bidding.yztc2025.com/' + item.path"
            >{{ item.name }}</a
          >
        </div>
      </div>
    </el-dialog>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose"
    >
      <span>
        【温馨提示：尊敬的投标人您好，参与此次投标需要缴纳平台服务费300元；如您确认参加请点击去缴费，如不参与投标请点击关闭。】</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="handlePay">支付宝缴费</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { downloadCheck } from "@/api/bid-manage/bid-project";
import { addBidDocumnet } from "@/api/bid-manage/bid-doc";
import { getBidHalfDetailBySegmentId } from "@/api/bid-manage/bid-project";
import { getToken } from "@/utils/auth";
import AddForm from "@/views/bidding/bid-doc-review/modules/add-form.vue";
import ViewForm from "@/views/bidding/bid-half-announcement/modules/add-form.vue";

export default {
  name: "ProjectForm",
  components: {
    AddForm,
    ViewForm,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    code: {
      type: String,
      default: () => "",
    },
    name: {
      type: String,
      default: () => "",
    },
    fileData: {
      type: Object,
      default: () => {},
    },
  },
  created() {},
  data() {
    return {
      dialogVisible: false,
      noticeShow: false,
      bidData: {},
      title: "状态查看",
      formData: {},
      editForm: {},
      segment_id: "",
      header: {
        Authorization: "Bearer " + getToken(),
      },
      addShow: false,
      loading: false,
      active: 0,
      order: {},
    };
  },
  mounted() {
    if (this.fileData.flow_status == 2) {
      this.active = 1;
    }
    if (this.fileData.flow_status == 3) {
      this.active = 2;
    }
    if (this.fileData.flow_status == 5) {
      this.active = 3;
    }
  },
  methods: {
    handleOpenHall() {
      this.$router.push("/hall/bidder");
    },
    handleViewNoctice(val) {
      let params = {
        id: this.fileData.id,
        status: val,
      };
      this.noticeShow = true;
      getBidHalfDetailBySegmentId(params).then((res) => {
        if (res.data.length == 0) {
          return;
          // this.$router.push("/bidding/bid-half-announcement");
        } else {
          this.bidData = res.data;
          this.$nextTick(() => {
            var element = document.createElement("div");
            element.innerHTML = this.bidData.content;
            document.getElementById("content").appendChild(element);
          });
        }
      });
    },
    handleView(val) {
      // let params={
      //     id:,
      //     status:val,
      // }
      this.addShow = true;
      getBidHalfDetailBySegmentId().then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        if (this.editForm.attachments != null) {
          this.editForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },

    async handleClick(eventName) {
      console.log(this.fileData);

      if (eventName === "submitFile") {
        // 检查缴费状态
        try {
          const res = await downloadCheck(this.fileData.id);
          if (res.data.status === 1) {
            // 已缴费，继续原有操作
            this.$emit("click", eventName);
          } else {
            // 未缴费，弹出提示
            // this.$message.warning(res.data.message || "请先完成投标缴费");
            this.dialogVisible = true;
            this.order = res.data.order;
          }
        } catch (e) {
          this.$message.error("检查缴费状态失败");
        }
      } else {
        this.$emit("click", eventName);
      }
    },
    handlePay() {
      window.open(
        `http://bidding.yztc2025.com/alipay/pay?order_id=${this.order.id}`,
        "_blank"
      );
      // goAliPay(this.order.id).then((res) => {
      //   console.log(res);
      // });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      formData.bid_segment_id = this.segment_id;
      addBidDocumnet(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      formData.bid_segment_id = this.segment_id;
      addBidDocumnet(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleClose(done) {
      this.$emit("close");
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.$router.push({ path: "/bidding/bid-doc-review", query: {} });
        this.addShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
p {
  margin-bottom: 10px;
}
#content {
  margin-top: 20px;
  margin-bottom: 20px;
  width: 80%;
}
#file {
  width: 100%;
  background-color: #c6c6c6;
  height: 40px;
  line-height: 40px;
  padding: 00px 20px;
}
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
.status-list {
  display: flex;
}
.status-list > div {
  flex: 33%;
  text-align: center;
  border: 1px solid #f5f7fa;
  border-radius: 5px;
}
.el-button {
  width: 150px;
}
</style>
