<template>
  <div>
    <el-dialog
      title="保证金缴纳"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="标段信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目名称:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="投标保证金:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="截止缴纳倒计时:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="保证金缴纳方式:" prop="title">
                  <el-date-picker
                    v-model="form.modify_date"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="保证金缴纳状态:" prop="title">
                  <el-date-picker
                    v-model="form.modify_date"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker></el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="支付信息" name="2">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="汇款公司:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="汇款银行:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="汇款账号:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="税号:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="附件信息" name="3">
          <el-upload
            ref="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "ProjectForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          this.form.publish_type = "0";
          if (this.form.attachments == null) {
            this.form.attachments = [];
          }
        }
      },
      deep: true,
    },
  },
  created() {
    let user = this.$store.getters.user;
    let obj = {
      label: user.company_info.company_name,
      value: user.company_info.id,
    };
    this.companyList.push(obj);
    console.log(user);
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3"],
      form: {
        publish_type: "0",
        status: 0,
        title: "",
        publisher: this.$store.getters.name,
        company_info_id: null,
        announcement_cate: null,
        geo_area_id: 36,
        keywords: "",
        publish_date: "",
        modify_date: "",
        source: "",
        author: "",
        content: "",
        attachments: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      companyList: [],
      rules: {
        title: [
          {
            required: true,
            message: "请输入公告名称",
            trigger: ["blur", "change"],
          },
        ],
        modifier: [
          {
            required: true,
            message: "请输入公告名称",
            trigger: ["blur", "change"],
          },
        ],
        announcement_cate: [
          {
            required: true,
            message: "请选择公告类别",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },

    handleSave() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.form);
        }
      });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachments.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachments.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachments.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
