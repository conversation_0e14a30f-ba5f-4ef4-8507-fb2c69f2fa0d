<template>
  <div>
    <el-dialog
      title="报名与邀请"
      :visible.sync="visible"
      width="60%"
      :before-close="handleClose"
    >
      <el-table :data="list" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="company_name"
          label="公司名称"
        ></el-table-column>
        <el-table-column
          prop="company_short_name"
          show-overflow-tooltip
          label="公司简称"
        >
        </el-table-column>
        <el-table-column
          prop="company_phone"
          show-overflow-tooltip
          label="联系方式"
        >
        </el-table-column>

        <el-table-column
          width="180"
          prop="registered_capital"
          label="注册资金(元)"
        ></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose"> 关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "ProjectForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    list: {
      type: Array,
      default: () => {},
    },
  },
  watch: {},
  created() {},
  data() {
    return {};
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
