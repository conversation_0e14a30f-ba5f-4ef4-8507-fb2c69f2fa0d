<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 20:05:21
 * @FilePath: /bid/src/views/bidding/bid-half-announcement/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <div v-if="mode === 1">
      <el-card>
        <search-form
          :fields="fields"
          :itemsPerRow="2"
          @search="handleSearch"
        ></search-form>
      </el-card>
      <el-card class="table">
        <el-table :data="tableData" border style="margin-top: 10px">
          <!-- 表格列定义 -->
          <el-table-column
            type="index"
            label="序号"
            width="50"
            align="center"
          ></el-table-column>
          <el-table-column
            width="180"
            show-overflow-tooltip
            prop="bidding_project.bidding_project_code"
            label="招标/采购项目编号"
          ></el-table-column>
          <el-table-column
            prop="bidding_project.bidding_project_name"
            show-overflow-tooltip
            label="招标/采购项目名称"
          >
          </el-table-column>
          <el-table-column
            prop="segment_name"
            show-overflow-tooltip
            label="标段名称"
          >
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            width="180"
            prop="bid_document[0].document_obtain_start"
            label="报名开始时间"
          ></el-table-column>

          <el-table-column
            width="180"
            prop="bid_document[0].document_submission_deadline"
            label="报名截止日期"
          ></el-table-column>

          <el-table-column
            width="100"
            prop="status"
            label="状态"
            v-if="isAdmin()"
          >
            <template slot-scope="scope">
              <span>{{ setBidText(scope.row.flow_state) }}</span>
            </template>
          </el-table-column>

          <el-table-column width="100" prop="status" label="状态" v-else>
            <template slot-scope="scope">
              <span>{{ setRegText(scope.row.registration) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="340"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="handleUpdate(scope.row)"
                >{{ setBtnText(scope.row) }}</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click="handleView(scope.row)"
                >标书下载</el-button
              >
              <el-button
                v-if="!isAdmin()"
                size="mini"
                type="primary"
                @click="handleFileView(scope.row)"
                >招标文件澄清查看</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div style="text-align: center; padding: 20px 0px">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="queryForm.per_page"
            :current-page="queryForm.page"
            @size-change="handleChangeSize"
            @current-change="handleChangePage"
          >
          </el-pagination>
        </div>
      </el-card>

      <add-form
        v-if="addShow"
        :visible="addShow"
        :editForm="editForm"
        :segmentForm="segmentForm"
        :fileList="fileList"
        :applicaList="applicaList"
        @save="handleSave"
        @submit="handleSubmit"
        @close="addShow = false"
      ></add-form>
      <view-form
        v-if="viewShow"
        :visible="viewShow"
        :editForm="viewForm"
        :addressOptions="addressOptions"
        :fileList="viewFileList"
        :industryOptions="industryOptions"
        @close="viewShow = false"
      ></view-form>
      <company-list
        v-if="listShow"
        :visible="listShow"
        :list="companyList"
        @close="listShow = false"
      ></company-list>
    </div>
    <div v-else>
      <file-form
        :init="isInit"
        :segmentForm="segmentForm"
        @back="handleBack"
      ></file-form>
    </div>
  </div>
</template>

<script>
import {
  getRegistrationsList,
  getRegistrationsDetail,
  geInviteDetail,
  geInviteSegmentsDetail,
  bidRegistrations,
} from "@/api/bid-manage/invite_bid";
import { getGeoAreas, getIndustries } from "@/api/bid-manage/project-info";
import { getRegisterList } from "@/api/bid-manage/bid-project";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";
import ViewForm from "./modules/view-form.vue";
import CompanyList from "./modules/company-list.vue";
import FileForm from "./modules/file-form.vue";
import store from "@/store";
export default {
  components: {
    SearchForm,
    AddForm,
    ViewForm,
    CompanyList,
    FileForm,
  },
  data() {
    return {
      isInit: false,
      mode: 1, //1邀请列表、2文件澄清
      industryOptions: [],
      listShow: false,
      companyList: [],
      addShow: false,
      viewShow: false,
      total: 0,
      viewForm: {},
      editForm: {},
      segmentForm: {},
      fileList: [],
      applicaList: [],
      addressOptions: [],
      viewFileList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    isAdmin() {
      let user = store.getters.user;
      let index = user.roles.indexOf(2);
      if (index == -1) {
        return false;
      } else {
        return true;
      }
    },
    handleBack(val) {
      this.mode = val;
    },
    handleFileView(data) {
      console.log(data);
      this.isInit = false;
      geInviteSegmentsDetail(data.id).then((res) => {
        this.segmentForm = res.data;
      });
      this.mode = 2;
      this.isInit = true;
    },
    isAdmin() {
      let user = store.getters.user;
      let index = user.roles.indexOf(2);
      if (index == -1) {
        return false;
      } else {
        return true;
      }
    },
    setBidText(data) {
      if (data >= 3) {
        return "开标";
      } else {
        return "未开标";
      }
    },
    setBtnText(data) {
      let user = store.getters.user;
      if (user.roles.indexOf(2) != -1) {
        return "报名信息";
      } else {
        if (data.registration.length == 0) {
          return "报名";
        } else {
          if (data.registration[0].status == 1) {
            return "报名";
          } else {
            return "报名详情";
          }
        }
      }
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      this.addShow = true;
    },
    handleView(data) {
      this.viewShow = true;
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      getRegistrationsDetail(data.id).then((res) => {
        console.log(res.data);
        this.viewForm = { ...res.data[0].bidding_project };
        let documentForm = { ...res.data[0].bid_document[0] };
        Object.assign(this.viewForm, documentForm);
        this.viewForm.fileList = [];
        if (documentForm.attachments != null) {
          documentForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.viewForm.fileList.push(obj);
          });
        }
        console.log(this.viewForm);
      });
    },
    handleUpdate(data) {
      console.log(data);
      let text = this.setBtnText(data);
      console.log(text);

      this.addShow = true;
      if (text == "报名" || text == "报名详情") {
        geInviteSegmentsDetail(data.id).then((res) => {
          console.log(res);
          this.segmentForm = res.data;
          this.addShow = true;
        });
        if (data.registration.length !== 0) {
          this.fileList = [];
          this.applicaList = [];
          geInviteDetail(data.id).then((res) => {
            this.editForm = { ...res.data };
            this.editForm.registration[0].attachment.forEach((item) => {
              let obj = {
                name: item.name,
                url: item.path,
              };
              this.fileList.push(obj);
            });
            this.editForm.registration[0].registration_material.forEach(
              (item) => {
                let obj = {
                  name: item.name,
                  url: item.path,
                };
                this.applicaList.push(obj);
              }
            );
            console.log(this.editForm);
          });
        }
      } else {
        this.listShow = true;
        let params = {
          bid_segment_id: data.id,
        };
        getRegisterList(params).then((res) => {
          this.companyList = res.data;
          console.log(res);
        });
      }
    },
    getList() {
      getRegistrationsList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 1;
      bidRegistrations(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 2;
      bidRegistrations(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    setRegText(value) {
      let status = null;
      if (value.length == 0) {
        return "未报名";
      } else {
        status = value[0].status;
      }
      console.log(value);

      let text = "";
      switch (status) {
        case 1:
          text = "未报名";
          break;
        case 2:
          text = "已报名";
          break;
        case 3:
          text = "待审核";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    setTypeText(status) {
      let text = "";
      switch (status) {
        case 1:
          text = "招标公告";
          break;
        case 2:
          text = "变更公告";
          break;
        case 3:
          text = "招标候选人公示";
          break;
        case 4:
          text = "结果公告";
          break;
        case 5:
          text = "废标公告";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
    getIndustries().then((res) => {
      this.industryOptions = res.data;
    });
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
