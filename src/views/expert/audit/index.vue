<!--
 * @Author: wzc
 * @Date: 2024-10-28 23:34:09
 * @LastEditors: wzc
 * @LastEditTime: 2024-10-28 23:34:53
 * @FilePath: /bid/src/views/expert/audit/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleAdd">新建</el-button>
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column prop="bidder_name" label="招标项目"></el-table-column>
        <el-table-column prop="bidder_name" label="公告名称"></el-table-column>
        <el-table-column prop="bidder_name" label="项目名称"></el-table-column>
        <el-table-column
          prop="bidder_name"
          label="提交审核时间"
        ></el-table-column>
        <el-table-column
          prop="bidder_name"
          label="评标会人数"
        ></el-table-column>
        <el-table-column prop="bidder_name" label="开标时间"></el-table-column>
        <el-table-column prop="bidder_name" label="评标时间"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span>{{ scope.row.status == 0 ? "未生效" : "已生效" }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :fileList="fileList"
      :type="type"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import {
  getAdminDocumnet,
  detailAdminDocumnet,
  approvalDocumnet,
} from "@/api/bid-manage/bid-doc";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";
import moment from "moment";

export default {
  name: "Tab",
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      type: "",
      queryForm: {
        page: 1,
        per_page: 10,
      },
      formData: {},
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "公告名称：",
          prop: "social_credit_code",
          component: "el-input",
          props: {
            placeholder: "请输入公告名称",
          },
        },
        // {
        //   label: "状态",
        //   prop: "social_credit_code",
        //   component: "el-select",
        //   props: {
        //     placeholder: "请输入状态",
        //   },
        //   options: [
        //     { label: "全部", value: '' },
        //     { label: "待发布", value: '0' },
        //     { label: "待审核", value: '1' },
        //     { label: "审核通过", value: '2' },
        //     { label: "审核退回", value: '3' },
        //   ],
        // },
      ],
      tableData: [], // 过滤后的数据
      tabMapOptions: [
        { label: "未审核", key: "0" },
        { label: "已审核", key: "1" },
      ],
      total: 0,
      activeName2: "0",
      queryForm: {
        page: 1,
        per_page: 10,
      },
      createdTimes: 0,
    };
  },
  watch: {
    activeName(val) {
      this.$router.push(`${this.$route.path}?tab=${val}`);
    },
  },
  created() {
    // init the default selected tab
    const tab = this.$route.query.tab;
    if (tab) {
      this.activeName = tab;
    }
    this.getList();
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      this.addShow = true;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleDetail(data) {
      this.addShow = true;
      detailAdminDocumnet(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        if (this.editForm.attachments != null) {
          this.editForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },
    getList() {
      if (!this.queryForm.status) {
        delete this.queryForm.status;
      }
      getAdminDocumnet(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
        this.tableData.forEach((item) => {
          item.approval_time =
            item.approval_time &&
            moment(new Date(item.approval_time)).format("YYYY-MM-DD HH:mm:ss");
        });
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        delete this.formData.approvalLog;
        updateBidDocumnet(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidDocumnet(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      if (data.id) {
        delete this.formData.approvalLog;
        updateBidDocumnet(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidDocumnet(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deteleBidDocumnet(row.id).then((res) => {
            console.log(res);

            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.getList();
        this.addShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
.tab-container {
  margin: 30px;
}
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
