<template>
  <div>
    <el-dialog
      title="专家管理"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="150px"
        status-icon
        size="mini"
      >
        <el-collapse v-model="collapse">
          <el-collapse-item title="发布信息" name="1">
            <el-row>
              <el-col :span="12">
                <el-form-item label="专家姓名:" prop="name">
                  <el-input
                    :disabled="isView"
                    v-model="form.name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="身份证号:" prop="number_id">
                  <el-input
                    :disabled="isView"
                    v-model="form.number_id"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="性别:" prop="gender">
                  <el-select
                    :disabled="isView"
                    v-model="form.gender"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="男" :value="0"> </el-option>
                    <el-option key="1" label="女" :value="1"> </el-option>
                  </el-select> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="出生日期:" prop="birthday">
                  <el-date-picker
                    :disabled="isView"
                    v-model="form.birthday"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="民族:" prop="nation">
                  <el-input
                    :disabled="isView"
                    v-model="form.nation"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="政治面貌:" prop="political_status">
                  <el-input
                    :disabled="isView"
                    v-model="form.political_status"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="详情信息" name="2">
            <el-row>
              <el-col :span="12">
                <el-form-item label="专家类别:" prop="expert_type">
                  <el-cascader
                    :disabled="isView"
                    v-model="form.expert_type"
                    :props="expertProps"
                    :options="expertOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="所属地区:" prop="district">
                  <el-cascader
                    :disabled="isView"
                    v-model="form.district"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="专家专业:" prop="expert_major">
                  <el-input
                    :disabled="isView"
                    v-model="form.expert_major"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="专家职称:" prop="expert_title">
                  <el-select
                    :disabled="isView"
                    v-model="form.expert_title"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="正高级" value="0"> </el-option>
                    <el-option key="1" label="副高级" value="1"> </el-option>
                    <el-option key="2" label="中级" value="2"> </el-option>
                    <el-option key="3" label="初级" value="3"> </el-option>
                  </el-select> </el-form-item
              ></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="从业信息" name="3">
            <el-row>
              <el-col :span="12">
                <el-form-item label=" 从业单位:" prop="unit">
                  <el-input
                    :disabled="isView"
                    v-model="form.unit"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="组织机构代码:" prop="organization_code">
                  <el-input
                    :disabled="isView"
                    v-model="form.organization_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label=" 退休状态:" prop="retirement_status">
                  <el-select
                    :disabled="isView"
                    v-model="form.retirement_status"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="正常" value="0"> </el-option>
                    <el-option key="1" label="返聘" value="1"> </el-option>
                    <el-option key="2" label="退休" value="2"> </el-option>
                  </el-select> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="参加工作时间:" prop="work_time">
                  <el-date-picker
                    :disabled="isView"
                    v-model="form.work_time"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="联系方式" name="4">
            <el-row>
              <el-col :span="12">
                <el-form-item label=" 手机号码:" prop="phone">
                  <el-input
                    :disabled="isView"
                    v-model="form.phone"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电子邮箱:" prop="email">
                  <el-input
                    :disabled="isView"
                    v-model="form.email"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="通讯地址:" prop="mail_address">
                  <el-input
                    :disabled="isView"
                    v-model="form.mail_address"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-if="!isView" title="附件信息" name="5">
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label=" 职业证书:"
                  prop="professional_certificate"
                >
                  <el-upload
                    ref="upload1"
                    action="https://bidding.senmoio.cn/api/file"
                    :headers="header"
                    :accept="acceptTypes.join(',')"
                    :before-upload="beforeUpload"
                    :file-list="fileList1"
                    :on-remove="handleRemove1"
                    :on-success="handleSuccess1"
                  >
                    <el-button slot="trigger" size="small" type="primary"
                      >选取文件</el-button
                    >
                    <div slot="tip" class="el-upload__tip">
                      可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                    </div>
                  </el-upload></el-form-item
                ></el-col
              >
              <el-col :span="12">
                <el-form-item label="入驻证明:" prop="certificate_occupancy">
                  <el-upload
                    ref="upload2"
                    action="https://bidding.senmoio.cn/api/file"
                    :headers="header"
                    :accept="acceptTypes.join(',')"
                    :before-upload="beforeUpload"
                    :file-list="fileList2"
                    :on-remove="handleRemove2"
                    :on-success="handleSuccess2"
                  >
                    <el-button slot="trigger" size="small" type="primary"
                      >选取文件</el-button
                    >
                    <div slot="tip" class="el-upload__tip">
                      可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                    </div>
                  </el-upload>
                </el-form-item></el-col
              >
            </el-row>
          </el-collapse-item>
          <el-collapse-item v-else title="附件信息" name="5">
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label=" 职业证书:"
                  prop="professional_certificate"
                >
                  <el-upload
                    ref="upload1"
                    :file-list="fileList1"
                    :on-preview="handlePreview"
                    :before-remove="handleRemove"
                  >
                  </el-upload></el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="入驻证明:" prop="certificate_occupancy">
                  <el-upload
                    ref="upload2"
                    :file-list="fileList2"
                    :on-preview="handlePreview"
                    :before-remove="handleRemove"
                  >
                  </el-upload> </el-form-item
              ></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="审核记录" name="6">
            <el-table :data="form.expertLog" border style="margin-top: 10px">
              <!-- 表格列定义 -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              ></el-table-column>
              <el-table-column prop="status" label="审核结果">
                <template slot-scope="scope">
                  <span>{{ setStatusText(scope.row.status) }}</span>
                </template></el-table-column
              >
              <el-table-column
                prop="approval_view"
                label="审核备注"
              ></el-table-column>
              <el-table-column
                prop="approval_name"
                show-overflow-tooltip
                width="200"
                label="办理人姓名"
              ></el-table-column>

              <el-table-column
                prop="updated_at"
                label="办理时间"
              ></el-table-column>
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{
          isView ? "关 闭" : "取 消"
        }}</el-button>
        <el-button v-if="!isView" type="primary" @click="handleSave"
          >保存</el-button
        >
        <el-button v-if="!isView" type="primary" @click="handleSubmit"
          >递交生效</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "AddForm",
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
    addressOptions: {
      type: Array,
      default: () => [],
    }, // 这里应该是地址的级联选项，需要根据实际情况填充
    expertOptions: {
      type: Array,
      default: () => [],
    },
    fileList1: {
      type: Array,
      default: () => [],
    },
    fileList2: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    formData: {
      handler(val) {
        console.log(val);
        if (val) {
          this.form = { ...this.formData };
          if (this.form.status === 2) {
            this.isView = true;
          } else {
            this.isView = false;
          }
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      isView: false,
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5", "6"],
      header: {
        Authorization: "Bearer " + getToken(),
      },
      form: {
        status: 0,
        professional_certificate: [],
        certificate_occupancy: [],
      },
      addressProps: {
        label: "name",
        value: "id",
      },
      expertProps: {
        label: "name",
        value: "id",
        multiple: true,
      },
      loading: false,
      rules: {
        name: [{ required: true, message: "请输入专家姓名", trigger: "blur" }],
        number_id: [
          { required: true, message: "请输入身份证号", trigger: "blur" },
        ],
        expert_type: [
          {
            required: true,
            message: "请选择专家类别",
            trigger: ["change", "blur"],
          },
        ],
        district: [
          {
            required: true,
            message: "请选择所属地区",
            trigger: ["change", "blur"],
          },
        ],
        unit: [{ required: true, message: "请输入从业单位", trigger: "blur" }],
        retirement_status: [
          {
            required: true,
            message: "请选择退休状态",
            trigger: ["change", "blur"],
          },
        ],
        expert_title: [
          {
            required: true,
            message: "请选择专家职称",
            trigger: ["change", "blur"],
          },
        ],
        phone: [
          {
            required: true,
            message: "请输入手机号码",
            trigger: ["change", "blur"],
          },
        ],
        // email: [
        //   {
        //     required: true,
        //     message: "请输入邮箱",
        //     trigger: ["change", "blur"],
        //   },
        //   {
        //     type: "email",
        //     message: "请输入正确的邮箱格式",
        //     trigger: ["change", "blur"],
        //   },
        // ],
        // mail_address: [
        //   {
        //     required: true,
        //     message: "请输入通讯地址",
        //     trigger: ["change", "blur"],
        //   },
        // ],
        // professional_certificate: [
        //   {
        //     required: true,
        //     message: "请上传职业证书",
        //     trigger: ["change", "blur"],
        //   },
        // ],
        // certificate_occupancy: [
        //   {
        //     required: true,
        //     message: "请上传入驻证明",
        //     trigger: ["change", "blur"],
        //   },
        // ],
      },
    };
  },
  methods: {
    handleRemove() {
      return false;
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSave() {
      console.log("save", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$emit("submit", this.form);
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleRemove1(file, fileList) {
      let index = this.form.professional_certificate.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.professional_certificate.splice(index, 1);
      }
    },
    handleSuccess1(res, file, fileList) {
      this.form.professional_certificate.push(res.data);
      console.log(res, file, fileList);
    },
    handleSuccess2(res, file, fileList) {
      this.form.certificate_occupancy.push(res.data);
      console.log(file, fileList);
    },
    handleRemove2(file, fileList) {
      let index = this.form.certificate_occupancy.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.certificate_occupancy.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
