<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="order_no"
          width="260"
          label="订单编号"
        ></el-table-column>
        <el-table-column
          width="260"
          show-overflow-tooltip=""
          prop="company_info.company_name"
          label="公司名称"
        ></el-table-column>
        <el-table-column
          prop="user.name"
          width="160"
          label="联系人"
        ></el-table-column>
        <el-table-column
          prop="user.phone"
          width="160"
          label="联系方式"
        ></el-table-column>
        <el-table-column
          width="260"
          show-overflow-tooltip
          prop="bid_segment.bidding_project.bidding_project_name"
          label="项目名称"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bid_segment.segment_name"
          label="标段名称"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bid_segment.segment_number"
          label="标段编号"
        ></el-table-column>
        <!-- <el-table-column
          prop="name"
          width="160"
          label="中标通知书编号"
        ></el-table-column> -->
        <el-table-column
          prop="updated_at"
          width="260"
          label="缴费时间"
        ></el-table-column>
        <el-table-column prop="amount" label="支付金额"></el-table-column>
        <el-table-column prop="status" label="支付状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getPayLog } from "@/api/bid-manage/bid-project";

import SearchForm from "@/components/SearchForm/index.vue";

export default {
  components: {
    SearchForm,
  },
  data() {
    return {
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "订单编号：",
          prop: "order_no",
          component: "el-input",
          props: {
            placeholder: "请输入订单编号",
          },
        },
        {
          label: "公司名称：",
          prop: "company_name",
          component: "el-input",
          props: {
            placeholder: "请输入公司名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
      industryOptions: [],
      userId: null,
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 1:
          text = "待支付";
          break;
        case 2:
          text = "支付完成";
          break;
        case 3:
          text = "支付失败";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getPayLog(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
