<template>
  <div class="main">
    <el-steps simple :active="active" finish-status="success">
      <el-step title="提交认证信息"></el-step>
      <el-step title="认证信息审核"></el-step>
      <el-step title="完成认证"></el-step>
    </el-steps>
    <el-form
      size="mini"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="200px"
    >
      <el-collapse v-model="collapse">
        <el-form-item label="平台身份" prop="roles">
          <el-checkbox-group v-model="form.roles">
            <el-checkbox
              :disabled="form.roles.indexOf(3) !== -1 && active == 3"
              :label="3"
              >招标代理</el-checkbox
            >
            <el-checkbox
              :label="4"
              :disabled="form.roles.indexOf(4) !== -1 && active == 3"
              >投标人</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
        <!-- 公司信息 -->
        <el-collapse-item title="公司信息" name="1">
          <el-row>
            <el-col :span="12">
              <el-form-item label="企业名称" prop="company_name">
                <el-input v-model="form.company_name"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业简称" prop="company_short_name">
                <el-input v-model="form.company_short_name"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="所在地" prop="company_location">
                <el-cascader
                  v-model="form.company_location"
                  :props="addressProps"
                  :options="addressOptions"
                  placeholder="请选择"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司电话" prop="company_phone">
                <el-input v-model="form.company_phone"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="实际经营地址" prop="operating_address">
                <el-input v-model="form.operating_address"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="公司传真" prop="company_fax">
                <el-input v-model="form.company_fax"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="公司主页" prop="company_website">
                <el-input v-model="form.company_website"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="住所" prop="residence">
                <el-input v-model="form.residence"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="经营范围" prop="business_scope">
                <el-input
                  type="textarea"
                  v-model="form.business_scope"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="14">
                  <el-form-item label="营业期限" prop="business_term_start">
                    <el-date-picker
                      v-model="form.business_term_start"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="开始日期"
                    ></el-date-picker>
                    <el-checkbox
                      v-model="form.business_term_long"
                      true-label="1"
                      false-label="0"
                      >长期</el-checkbox
                    >
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item class="range">
                    <el-date-picker
                      v-model="form.business_term_end"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="结束日期"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="纳税人类型" prop="taxpayer_type">
                <el-select v-model="form.taxpayer_type" placeholder="请选择">
                  <el-option label="一般纳税人" :value="1"></el-option>
                  <el-option label="小规模纳税人" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业性质" prop="company_nature">
                <el-select v-model="form.company_nature" placeholder="请选择">
                  <el-option
                    v-for="item in companyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="基本户开户行" prop="basic_account_bank">
                <el-input v-model="form.basic_account_bank"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业注册日期" prop="registration_date">
                <el-date-picker
                  v-model="form.registration_date"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                label="基本户开户行账号"
                prop="basic_account_number"
              >
                <el-input v-model="form.basic_account_number"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册资金(元)" prop="registered_capital">
                <el-input v-model="form.registered_capital"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="所在行业" prop="industry">
                <el-cascader
                  v-model="form.industry"
                  :props="industryProps"
                  :options="industryOptions"
                  placeholder="请选择"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法人代表" prop="legal_representative">
                <el-input v-model="form.legal_representative"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="多证合一" prop="multi_certificate">
                <el-switch
                  active-value="2"
                  inactive-value="1"
                  v-model="form.multi_certificate"
                ></el-switch>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="统一社会信用代码" prop="social_credit_code">
                <el-input v-model="form.social_credit_code"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>

        <!-- 证照信息 -->
        <el-collapse-item title="证照信息" name="2">
          <el-row>
            <el-col :span="12">
              <el-form-item label="营业执照扫描件" prop="business_license_scan">
                <el-upload
                  action="https://bidding.senmoio.cn/api/file"
                  :headers="header"
                  :accept="acceptTypes.join(',')"
                  :before-upload="beforeUpload"
                  :file-list="fileList1"
                  :on-remove="handleRemove1"
                  :on-success="handleSuccess1"
                  :on-preview="handlePreview"
                >
                  <el-button
                    slot="trigger"
                    size="small"
                    type="primary"
                    :disabled="isDisabled"
                    >选取文件</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户许可证扫描件">
                <el-upload
                  action="https://bidding.senmoio.cn/api/file"
                  :headers="header"
                  :accept="acceptTypes.join(',')"
                  :before-upload="beforeUpload"
                  :file-list="fileList2"
                  :on-remove="handleRemove2"
                  :on-success="handleSuccess2"
                  :on-preview="handlePreview"
                >
                  <el-button
                    slot="trigger"
                    size="small"
                    type="primary"
                    :disabled="isDisabled"
                    >选取文件</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="企业法人身份证扫描件(正面)">
                <el-upload
                  action="https://bidding.senmoio.cn/api/file"
                  :headers="header"
                  :accept="acceptTypes.join(',')"
                  :before-upload="beforeUpload"
                  :file-list="fileList3"
                  :on-remove="handleRemove3"
                  :on-success="handleSuccess3"
                  :on-preview="handlePreview"
                >
                  <el-button
                    slot="trigger"
                    size="small"
                    type="primary"
                    :disabled="isDisabled"
                    >选取文件</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业法人身份证扫描件(反面)">
                <el-upload
                  action="https://bidding.senmoio.cn/api/file"
                  :headers="header"
                  :accept="acceptTypes.join(',')"
                  :before-upload="beforeUpload"
                  :file-list="fileList6"
                  :on-remove="handleRemove6"
                  :on-success="handleSuccess6"
                  :on-preview="handlePreview"
                >
                  <el-button
                    slot="trigger"
                    size="small"
                    type="primary"
                    :disabled="isDisabled"
                    >选取文件</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="验证公函" prop="verification_letter_scan">
                <el-upload
                  action="https://bidding.senmoio.cn/api/file"
                  :headers="header"
                  :accept="acceptTypes.join(',')"
                  :before-upload="beforeUpload"
                  :file-list="fileList4"
                  :on-remove="handleRemove4"
                  :on-success="handleSuccess4"
                  :on-preview="handlePreview"
                >
                  <el-button
                    slot="trigger"
                    size="small"
                    type="primary"
                    :disabled="isDisabled"
                    >选取文件</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                  </div>
                </el-upload>
                <el-button type="text" @click="handleDownload">下载</el-button>
                验证公函模板
                <a id="downloadLink" style="display: none"></a>
              </el-form-item>
            </el-col>
          </el-row>
        </el-collapse-item>

        <!-- 招标代理机构信息 -->
        <el-collapse-item
          v-if="isShow"
          title="您作为招标代理机构还需提供如下材料"
          name="3"
        >
          <el-form-item label="招标代理机构分类" prop="bidding_agency_category">
            <el-checkbox-group v-model="form.bidding_agency_category">
              <el-checkbox label="工程项目招标代理机构"></el-checkbox>
              <el-checkbox label="中央投资项目招标代理机构"></el-checkbox>
              <el-checkbox label="机电产品国际招标代理机构"></el-checkbox>
              <el-checkbox label="政府采购代理机构"></el-checkbox>
              <el-checkbox label="通信建设项目招标代理机构"></el-checkbox>
              <el-checkbox label="科技项目招标代理机构"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item
            label="招标代理资质等级"
            prop="bidding_agency_qualification_level"
          >
            <el-input
              v-model="form.bidding_agency_qualification_level"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="政府采购代理机构编号"
            prop="bidding_agency_certificate_number"
          >
            <el-input
              v-model="form.bidding_agency_certificate_number"
            ></el-input>
          </el-form-item>
          <el-form-item label="招标代理资质附件">
            <el-upload
              action="https://bidding.senmoio.cn/api/file"
              :headers="header"
              :accept="acceptTypes.join(',')"
              :before-upload="beforeUpload"
              :file-list="fileList5"
              :on-remove="handleRemove5"
              :on-success="handleSuccess5"
              :on-preview="handlePreview"
            >
              <el-button
                slot="trigger"
                size="small"
                type="primary"
                :disabled="isDisabled"
                >选取文件</el-button
              >
              <div slot="tip" class="el-upload__tip">
                可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
              </div>
            </el-upload>
          </el-form-item>
        </el-collapse-item>
        <el-collapse-item title="审核记录" name="4">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.approval_type) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <!-- 表单操作 -->
    </el-form>
    <div style="text-align: center; margin-top: 5px">
      <el-button :disabled="isDisabled" plain @click="handleSave"
        >保存</el-button
      >
      <el-button :disabled="isDisabled" type="primary" @click="handleSubmit"
        >提交认证</el-button
      >
    </div>
  </div>
</template>

<script>
import { getCompanyInfo, updateCompanyInfo } from "@/api/user";
import { getGeoAreas, getIndustries } from "@/api/bid-manage/project-info";
import { companyOptions } from "@/utils/data";
import { getToken } from "@/utils/auth";
export default {
  data() {
    return {
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      isShow: false,
      companyOptions: companyOptions,
      header: {
        Authorization: "Bearer " + getToken(),
      },
      addressProps: {
        label: "name",
        value: "id",
      },
      industryProps: {
        label: "name",
        value: "id",
      },
      addressOptions: [],
      industryOptions: [],
      collapse: ["1", "2", "3", "4"],
      active: 0,
      fileList1: [],
      fileList2: [],
      fileList3: [],
      fileList4: [],
      fileList5: [],
      fileList6: [],
      isDisabled: false,
      form: {
        roles: [],
        long: true,
        company_name: "",
        company_short_name: "",
        company_location: "",
        company_phone: "",
        company_fax: "",
        company_website: "",
        operating_address: "",
        residence: "",
        business_scope: "",
        business_term: "",
        taxpayer_type: 1,
        basic_account_bank: "",
        basic_account_number: "",
        industry: 0,
        multi_certificate: 1,
        social_credit_code: "",
        company_nature: "",
        registration_date: null,
        registered_capital: null,
        legal_representative: "",
        type: 1,
        created_at: "",
        updated_at: "",
        business_license_scan: [],
        bank_permit_scan: [],
        legal_id_front_scan: [],
        legal_id_back_scan: [],
        verification_letter_scan: [],
        bidding_agency_category: "",
        bidding_agency_qualification_level: "",
        bidding_agency_certificate_number: "",
        bidding_agency_attachment: null,
        logo: null,
      },
      rules: {
        roles: [
          {
            required: true,
            message: "请选择平台身份",
            trigger: ["blur", "change"],
          },
        ],
        company_name: [
          {
            required: true,
            message: "请输入企业名称",
            trigger: ["blur", "change"],
          },
        ],
        company_short_name: [
          {
            required: true,
            message: "请输入企业简称",
            trigger: ["blur", "change"],
          },
        ],
        company_phone: [
          {
            required: true,
            message: "请输入公司电话",
            trigger: ["blur", "change"],
          },
        ],
        operating_address: [
          {
            required: true,
            message: "请输入实际经营地址",
            trigger: ["blur", "change"],
          },
        ],
        // residence: [
        //   {
        //     required: true,
        //     message: "请输入住所",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // business_scope: [
        //   {
        //     required: true,
        //     message: "请输入经营范围",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // business_term_start: [
        //   {
        //     required: true,
        //     message: "请选择经营期限开始日期",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        taxpayer_type: [
          {
            required: true,
            message: "请选择纳税人类型",
            trigger: ["blur", "change"],
          },
        ],
        company_nature: [
          {
            required: true,
            message: "请选择企业性质",
            trigger: ["blur", "change"],
          },
        ],
        // basic_account_bank: [
        //   {
        //     required: true,
        //     message: "请输入基本户开户行",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        registration_date: [
          {
            required: true,
            message: "请选择企业注册日期",
            trigger: ["blur", "change"],
          },
        ],
        // basic_account_number: [
        //   {
        //     required: true,
        //     message: "请输入基本户开户行账号",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // registered_capital: [
        //   {
        //     required: true,
        //     message: "请输入注册资金",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        // industry: [
        //   {
        //     required: true,
        //     message: "请选择所在行业",
        //     trigger: ["blur", "change"],
        //   },
        // ],
        legal_representative: [
          {
            required: true,
            message: "请输入法人代表",
            trigger: ["blur", "change"],
          },
        ],
        social_credit_code: [
          {
            required: true,
            message: "请输入统一社会信用代码",
            trigger: ["blur", "change"],
          },
        ],
        business_license_scan: [
          {
            required: true,
            message: "请上传营业执照扫描件",
            trigger: "change",
          },
        ],
        bidding_agency_category: [
          {
            required: true,
            message: "请选择招标代理机构分类",
            trigger: ["blur", "change"],
          },
        ],
        verification_letter_scan: [
          {
            required: true,
            message: "请选择验证公函",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  created() {
    this.initData();
    getGeoAreas().then((res) => {
      this.addressOptions = res.data;
    });
    getIndustries().then((res) => {
      this.industryOptions = res.data;
    });
  },
  watch: {
    "form.roles": {
      handler(newVal, oldVal) {
        console.log(newVal);
        let index = newVal.indexOf(3);
        if (index == -1) {
          this.isShow = false;
          this.$set(this.rules, "bidding_agency_category", []);
        } else {
          this.isShow = true;
          this.$set(this.rules, "bidding_agency_category", [
            {
              required: true,
              message: "请选择招标代理机构分类",
              trigger: ["blur", "change"],
            },
          ]);
          this.$nextTick(() => {
            this.$refs.form.validateField("bidding_agency_category"); // 触发校验
          });
        }
        // 处理变化
      },
      deep: true, // 深度监听
      immediate: true, // 立即触发
    },
  },
  methods: {
    setStatusText(val) {
      let text = "";
      switch (val) {
        case 4:
          text = "待审核";
          break;
        case 5:
          text = "审核通过";
          break;
        case 6:
          text = "审核退回";
          break;
        default:
          break;
      }
      return text;
    },
    initData() {
      this.fileList1 = [];
      this.fileList2 = [];
      this.fileList3 = [];
      this.fileList4 = [];
      this.fileList5 = [];
      this.fileList6 = [];
      getCompanyInfo().then((res) => {
        let data = res.data;
        this.form = { ...res.data };
        if (data.bidding_agency_category == null) {
          this.form.bidding_agency_category = [];
        }
        if (data.business_license_scan == null) {
          this.form.business_license_scan = [];
        } else {
          this.form.business_license_scan.forEach((item) => {
            console.log(item);

            this.fileList1.push(item);
          });
        }
        if (data.bank_permit_scan == null) {
          this.form.bank_permit_scan = [];
        } else {
          this.form.bank_permit_scan.forEach((item) => {
            this.fileList2.push(item);
          });
        }
        if (data.legal_id_front_scan == null) {
          this.form.legal_id_front_scan = [];
        } else {
          this.form.legal_id_front_scan.forEach((item) => {
            this.fileList3.push(item);
          });
        }
        if (data.legal_id_back_scan == null) {
          this.form.legal_id_back_scan = [];
        } else {
          this.form.legal_id_back_scan.forEach((item) => {
            this.fileList6.push(item);
          });
        }
        if (data.verification_letter_scan == null) {
          this.form.verification_letter_scan = [];
        } else {
          this.form.verification_letter_scan.forEach((item) => {
            this.fileList4.push(item);
          });
        }
        if (data.bidding_agency_attachment == null) {
          this.form.bidding_agency_attachment = [];
        } else {
          this.form.bidding_agency_attachment.forEach((item) => {
            this.fileList5.push(item);
          });
        }
        this.active = this.form.type - 2;
        if (this.form.type == 4) {
          this.isDisabled = true;
        }
        console.log(res);
      });
    },
    handlePreview(file, fileList) {
      console.log(file, fileList);
      let urlTemp = "";
      if (!file.path) {
        urlTemp = file.response.data.path;
      } else {
        urlTemp = file.path;
      }
      let url = `https://bidding.senmoio.cn/${urlTemp}`;
      window.open(url, "_blank");
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.business_license_scan.push(res.data);
      console.log(res, file, fileList);
    },
    handleSuccess2(res, file, fileList) {
      this.form.bank_permit_scan.push(res.data);
      console.log(res, file, fileList);
    },
    handleSuccess3(res, file, fileList) {
      this.form.legal_id_front_scan.push(res.data);
      console.log(res, file, fileList);
    },
    handleSuccess4(res, file, fileList) {
      this.form.verification_letter_scan.push(res.data);
      console.log(res, file, fileList);
    },
    handleSuccess5(res, file, fileList) {
      this.form.bidding_agency_attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleSuccess6(res, file, fileList) {
      this.form.legal_id_back_scan.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.business_license_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.business_license_scan.splice(index, 1);
      }
    },
    handleRemove2(file, fileList) {
      let index = this.form.bank_permit_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.bank_permit_scan.splice(index, 1);
      }
    },
    handleRemove3(file, fileList) {
      let index = this.form.legal_id_front_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.legal_id_front_scan.splice(index, 1);
      }
    },
    handleRemove4(file, fileList) {
      let index = this.form.verification_letter_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.verification_letter_scan.splice(index, 1);
      }
    },
    handleRemove5(file, fileList) {
      let index = this.form.bidding_agency_attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.bidding_agency_attachment.splice(index, 1);
      }
    },
    handleRemove6(file, fileList) {
      let index = this.form.legal_id_back_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.legal_id_back_scan.splice(index, 1);
      }
    },
    handleSave() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.type = 3;
          updateCompanyInfo(this.form).then((res) => {
            this.message(res.status_code, res.message);
          });
        }
      });
    },
    handleSubmit() {
      this.$confirm("确认要提交认证吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.form.validate((valid) => {
            if (valid) {
              this.form.type = 4;
              updateCompanyInfo(this.form).then((res) => {
                this.message(res.status_code, res.message);
                if (res.status_code == 200) {
                  this.$alert(
                    "公司认证信息已提交，请耐心等待，管理员会在24小时内审核；如需加急请联系管理员，电话：0319-4562808",
                    "提交成功",
                    {
                      confirmButtonText: "确定",
                      type: "success",
                    }
                  );
                }
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handleDownload() {
      const link = document.getElementById("downloadLink");
      link.href = "/易招天成认证申请公函模板.doc";
      link.download = ""; // 可以设置下载文件的默认名称
      link.click();
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.initData();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
.range {
  margin-left: -150px;
}
.range /deep/ .el-form-item__content {
  margin-left: 10px;
}
.main {
  padding: 20px;
}
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
