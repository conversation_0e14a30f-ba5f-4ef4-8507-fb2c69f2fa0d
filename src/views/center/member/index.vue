<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleAdd">新建</el-button>
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="80">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column label="性别" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.gender == 0 ? "男" : "女" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="账号"></el-table-column>
        <el-table-column prop="role" label="角色">
          <template slot-scope="scope">
            <span>{{ setRoleName(scope.row.roles) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.state"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="2"
              @change="handleChangeStatus($event, scope.row.id)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleShowPwd(scope.row)"
              >修改密码</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :roles="roles"
      @save="handleSave"
      @close="addShow = false"
    ></add-form>
    <password
      v-if="pwdShow"
      :visible="pwdShow"
      @save="handleSavePwd"
      @close="pwdShow = false"
    ></password>
  </div>
</template>

<script>
import {
  getUserList,
  addUser,
  updateUserInfo,
  updatePassword,
  getUserById,
  updateState,
} from "@/api/user";
import { getRoles } from "@/api/role";

import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";
import Password from "./modules/password.vue";
export default {
  components: {
    SearchForm,
    AddForm,
    Password,
  },
  data() {
    return {
      editForm: {},
      pwdShow: false,
      addShow: false,
      oldPwd: "",
      total: 0,
      roles: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "账号：",
          prop: "phone",
          component: "el-input",
          props: {
            placeholder: "请输入账号",
          },
        },
        {
          label: "姓名：",
          prop: "name",
          component: "el-input",
          props: {
            placeholder: "请输入姓名",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
      industryOptions: [],
      userId: null,
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      this.addShow = true;
    },
    handleUpdate(data) {
      this.addShow = true;
      console.log(data);
      getUserById(data.id).then((res) => {
        this.editForm = res.data;
        console.log(res);
      });
    },
    getList() {
      getUserList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    setRoleName(data) {
      let text = "";
      data.forEach((item) => {
        text += item.description + ",";
      });
      return text.substring(0, text.length - 1);
    },
    handleSave(data) {
      console.log(data);
      let formData = { ...data };
      if (data.id) {
        updateUserInfo(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addUser(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      addUser(formData).then((res) => {
        this.message(res.status_code, res.message);
        console.log(res);
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          // deleteUser(row.id).then((res) => {
          //   console.log(res);
          //   this.message(res.status_code, res.message);
          //   this.getList();
          // });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.getList();
        this.addShow = false;
        this.pwdShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    handleSavePwd(data) {
      let formData = { ...data };
      formData.user_id = this.userId;
      updatePassword(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleShowPwd(data) {
      this.pwdShow = true;
      this.userId = data.id;
    },
    handleChangeStatus(val, id) {
      updateState(id, val).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
  },
  created() {
    this.getList();
    getRoles().then((res) => {
      this.roles = res.data.data;
    });
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
