<!--
 * @Author: wzc
 * @Date: 2024-10-25 23:02:56
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 12:35:26
 * @FilePath: /bid/src/views/center/member/modules/add-form.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-dialog
      title="添加用户"
      :visible.sync="visible"
      width="60%"
      :before-close="handleClose"
    >
      <el-row :gutter="50" style="margin-left: 10%">
        <el-col :span="12">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-form-item>
              <span style="font-weight: bold; color: red"
                >提示：默认密码:12345678</span
              >
            </el-form-item>
            <el-form-item label="姓名:" prop="name">
              <el-input v-model="form.name"></el-input>
            </el-form-item>
            <el-form-item label="性别:" prop="gender">
              <el-select v-model="form.gender" placeholder="请选择">
                <el-option key="0" label="男" :value="0"> </el-option>
                <el-option key="1" label="女" :value="1"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="身份证:" prop="id_card">
              <el-input v-model="form.id_card"></el-input>
            </el-form-item>
            <el-form-item label="手机号/用户名:" prop="phone">
              <el-input v-model="form.phone"></el-input>
            </el-form-item>
            <el-form-item label="固定联系电话:" prop="fixed_telephone">
              <el-input v-model="form.fixed_telephone"></el-input>
            </el-form-item>
            <el-form-item label="传真:" prop="fax">
              <el-input v-model="form.fax"></el-input>
            </el-form-item>
            <!-- <el-form-item label="部门:" prop="department">
            <el-input disabled v-model="form.department"></el-input>
          </el-form-item>
          <el-form-item label="职务:" prop="position">
            <el-input disabled v-model="form.position"></el-input>
          </el-form-item> -->
            <el-form-item label="角色:" prop="role">
              <el-checkbox-group v-model="role">
                <el-select v-model="role" placeholder="请选择">
                  <el-option
                    v-for="item in roles"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  >
                  </el-option>
                  <!-- 其他选项可以在这里添加 -->
                </el-select>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="handleSave">保存</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="4">
          <div style="display: inline-grid">
            <pan-thumb :image="image" />
            <el-button
              type="primary"
              icon="el-icon-upload"
              style="margin-top: 10px"
              @click="imgShow = true"
              >更换
            </el-button>
          </div>
          <image-cropper
            v-if="imgShow"
            :width="300"
            :height="300"
            field="file"
            url="https://bidding.senmoio.cn/api/file"
            lang-type="zh"
            @close="close"
            @crop-upload-success="cropSuccess"
          />
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import PanThumb from "@/components/PanThumb";
import ImageCropper from "@/components/ImageCropper";
import { getInfo, updateUser } from "@/api/user";
export default {
  name: "Tab",
  components: {
    ImageCropper,
    PanThumb,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    roles: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      role: "",
      form: {},
      imgShow: false,
      image: "",
      rules: {},
    };
  },
  watch: {
    editForm: {
      handler(val) {
        console.log(val);
        if (val) {
          this.form = { ...val };
          this.$set(this, "role", val.roles[0]);
          this.image = "http://bidding.senmoio.cn/" + val.user_img.path;
        }
      },
      deep: true,
    },
  },
  created() {},
  methods: {
    showCreatedTimes() {
      this.createdTimes = this.createdTimes + 1;
    },
    cropSuccess(resData) {
      console.log(resData);
      this.imgShow = false;
      this.image = "http://bidding.senmoio.cn/" + resData.path;
      this.form.user_img = resData;
      console.log(this.image);
    },
    close() {
      this.imgShow = false;
    },
    handleSave() {
      this.editForm = null;
      this.form.roles = [];
      this.form.roles.push(this.role);
      // this.form.roles[0] = roles;
      this.$emit("save", this.form);
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
  },
};
</script>

<style scoped>
.main {
  padding: 10px;
}
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
