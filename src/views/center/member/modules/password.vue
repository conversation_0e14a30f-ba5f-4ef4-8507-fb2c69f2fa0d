<!--
 * @Author: wzc
 * @Date: 2024-11-16 18:13:12
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-16 23:09:45
 * @FilePath: /bid/src/views/center/member/modules/password.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div>
    <el-dialog
      title="修改密码"
      :visible.sync="visible"
      width="30%"
      :before-close="handleClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="120px"
        status-icon
        size="mini"
      >
        <el-form-item label="旧密码:" prop="old_password">
          <el-input type="password" v-model="form.old_password"></el-input>
        </el-form-item>
        <el-form-item label="新密码:" prop="password">
          <el-input type="password" v-model="form.password"></el-input>
        </el-form-item>
        <el-form-item label="确认新密码:" prop="password_confirmation">
          <el-input
            type="password"
            v-model="form.password_confirmation"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "Password",
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      form: {},
      loading: false,
      rules: {
        password: [
          { required: true, message: "请输入新密码", trigger: "blur" },
        ],
        old_password: [
          { required: true, message: "请输入旧密码", trigger: "blur" },
        ],
        password_confirmation: [
          { required: true, message: "请再次输入新密码", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSave() {
      console.log("save", this.form);
      this.$emit("save", this.form);
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
</style>
