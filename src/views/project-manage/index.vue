<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 21:14:12
 * @FilePath: /bid/src/views/project-manage/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column prop="project_code" label="项目编号"></el-table-column>
        <el-table-column
          prop="project_name"
          width="240"
          show-overflow-tooltip
          label="项目名称"
        ></el-table-column>
        <el-table-column
          prop="project_legal_person"
          label="项目法人"
        ></el-table-column>
        <el-table-column
          prop="investment_amount"
          label="投资金额（元）"
        ></el-table-column>
        <el-table-column
          prop="project_initiation_date"
          label="立项时间"
        ></el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <project-form
      v-if="addShow"
      :visible="addShow"
      :addressOptions="addressOptions"
      :industryOptions="industryOptions"
      :editForm="editForm"
      @close="addShow = false"
    ></project-form>
  </div>
</template>

<script>
import {
  getAllProjectList,
  getIndustries,
  getGeoAreas,
  getProjectInfoById,
} from "@/api/bid-manage/project-info";
import SearchForm from "@/components/SearchForm/index.vue";
import ProjectForm from "./modules/project-form.vue";

export default {
  components: {
    SearchForm,
    ProjectForm,
  },
  data() {
    return {
      addShow: false,
      editForm: {},
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
      industryOptions: [],
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleUpdate(data) {
      getProjectInfoById(data.id).then((res) => {
        this.editForm = res.data;
        console.log(res);
      });
      this.addShow = true;
    },
    getList() {
      getAllProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
  },
  created() {
    this.getList();
    getIndustries().then((res) => {
      this.industryOptions = res.data;
    });
    getGeoAreas().then((res) => {
      this.addressOptions = res.data;
    });
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
