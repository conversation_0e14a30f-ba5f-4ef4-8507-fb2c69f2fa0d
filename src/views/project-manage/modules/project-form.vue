<template>
  <div>
    <el-dialog
      title="项目信息表单"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="基本信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目编号:" prop="project_code">
                  <el-input
                    disabled
                    v-model="form.project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="项目名称:" prop="project_name">
                  <el-input
                    disabled
                    v-model="form.project_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="是否依法招标:" prop="legally_bidding">
                  <el-radio-group disabled v-model="form.legally_bidding">
                    <el-radio :label="1">依法招标</el-radio>
                    <el-radio :label="2">非依法招标</el-radio>
                  </el-radio-group>
                </el-form-item></el-col
              >
              <el-col :span="12">
                <el-form-item label="进场交易:" prop="entering_transaction">
                  <el-radio-group disabled v-model="form.entering_transaction">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item></el-col
              >
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目地址:" prop="project_address">
                  <el-cascader
                    disabled
                    v-model="form.project_address"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="项目行业分类:"
                  prop="project_industry_classification"
                >
                  <el-cascader
                    disabled
                    v-model="form.project_industry_classification"
                    :props="industryProps"
                    :options="industryOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item label="详细地址:" prop="detailed_address">
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.detailed_address"
                ></el-input> </el-form-item
            ></el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="项目法人:" prop="project_legal_person">
                  <el-input
                    disabled
                    v-model="form.project_legal_person"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="出资比例(%):" prop="investment_ratio">
                  <el-input
                    disabled
                    v-model="form.investment_ratio"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="投资金额(元):" prop="investment_amount">
                  <el-input
                    disabled
                    v-model="form.investment_amount"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item label="资金来源:" prop="source_of_funds">
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.source_of_funds"
                ></el-input>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="项目规模:" prop="project_scale">
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.project_scale"
                ></el-input>
              </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目分类:" prop="project_cate">
                  <el-select
                    v-model="form.project_cate"
                    placeholder="请选择"
                    disabled
                  >
                    <el-option key="1" label="工程" :value="1"> </el-option>
                    <el-option key="2" label="采购" :value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.project_cate === 1">
                <el-form-item label="批文编号:" prop="approval_document_number">
                  <el-input
                    disabled
                    v-model="form.approval_document_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row v-if="form.project_cate === 1">
              <el-col :span="12">
                <el-form-item label="批文名称:" prop="approval_document_name">
                  <el-input
                    disabled
                    v-model="form.approval_document_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="批准单位:" prop="approval_authority">
                  <el-input
                    disabled
                    v-model="form.approval_authority"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row v-if="form.project_cate === 1">
              <el-col :span="12">
                <el-form-item label="立项时间:" prop="project_initiation_date">
                  <el-date-picker
                    disabled
                    v-model="form.project_initiation_date"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标方案核准号:"
                  prop="bidding_plan_approval_number"
                >
                  <el-input
                    disabled
                    v-model="form.bidding_plan_approval_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="项目负责人" name="2">
          <div>
            <el-table
              :data="form.project_managers"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="项目负责人" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.name"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="联系电话" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.phone"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="备注">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.remark"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="核准扫描件" name="3">
          <el-upload
            ref="upload1"
            :file-list="fileList1"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>
        <el-collapse-item
          title="批文扫描件"
          name="4"
          v-if="form.project_cate === 1"
        >
          <el-upload
            ref="upload2"
            :file-list="fileList2"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "ProjectForm",
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    addressOptions: {
      type: Array,
    }, // 这里应该是地址的级联选项，需要根据实际情况填充
    industryOptions: {
      type: Array,
      default: () => [],
    }, // 这里应该是行业分类的级联选项，需要根据实际情况填充
    editForm: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    editForm: {
      handler(val) {
        console.log(val);
        if (val) {
          this.form = { ...val };
          this.fileList1 = [];
          this.fileList2 = [];
          this.form.approval_document_scan.forEach((item) => {
            console.log(item);
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList1.push(obj);
            console.log(this.form.approval_document_scan);
          });
          this.form.approval_number_scan.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList2.push(obj);
            console.log(this.form.approval_number_scan);
          });
        } else {
          this.fileList1 = [];
          this.fileList2 = [];
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      url: "",
      collapse: ["1", "2", "3", "4"],
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      form: {
        project_code: "",
        project_name: "",
        legally_bidding: "",
        entering_transaction: true,
        project_address: [],
        project_industry_classification: [],
        detailed_address: "",
        project_legal_person: "",
        investment_ratio: "",
        investment_amount: "",
        source_of_funds: "",
        project_scale: "",
        approval_document_number: "",
        approval_document_name: "",
        approval_authority: "",
        project_initiation_date: "",
        bidding_plan_approval_number: "",
        project_managers: [],
        approval_document_scan: [],
        approval_number_scan: [],
      },
      fileList2: [],
      fileList1: [],
      addressProps: {
        label: "name",
        value: "id",
      },
      industryProps: {
        label: "name",
        value: "id",
      },
      selectedRows: [],
      loading: false,
      rules: {},
    };
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
  },
};
</script>

<style scoped>
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
/* 这里可以添加一些CSS样式来美化表单 */
</style>
