<template>
  <div>
    <el-dialog
      title="招标文件"
      :visible.sync="visible"
      width="80%"
      append-to-body
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="归属信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目名称:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="segment_name">
                  <el-input
                    disabled
                    v-model="form.segment_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="文件信息" name="2">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col>
                <el-form-item label="标题:" prop="title">
                  <el-input
                    v-model="form.title"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取开始时间:"
                  prop="document_obtain_start"
                >
                  <el-date-picker
                    v-model="form.document_obtain_start"
                    type="datetime"
                    disabled
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取截止时间:"
                  prop="document_obtain_deadline"
                >
                  <el-date-picker
                    v-model="form.document_obtain_deadline"
                    disabled
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="投标文件递交截止时间:"
                  prop="document_submission_deadline"
                >
                  <el-date-picker
                    v-model="form.document_submission_deadline"
                    disabled
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="投标有效期(天):"
                  prop="bid_validity_period"
                >
                  <el-input
                    v-model="form.bid_validity_period"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item
                  label="是否需要清单评审:"
                  prop="requires_list_review"
                >
                  <el-radio-group v-model="form.requires_list_review" disabled>
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
              <el-col :span="8">
                <el-form-item label="投标报价价款形式:" prop="bid_price_form">
                  <el-radio-group v-model="form.bid_price_form" disabled>
                    <el-radio :label="1">金额</el-radio>
                    <el-radio :label="2">费率</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
              <el-col :span="8">
                <el-form-item label="是否需要报名:" prop="is_need_sign_up">
                  <el-radio-group v-model="form.is_need_sign_up" disabled>
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
            </el-row>
            <el-row>
              <el-form-item label="投标文件提交方法:" prop="submission_method">
                <el-input
                  type="textarea"
                  disabled
                  v-model="form.submission_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标地点:" prop="bid_opening_location">
                <el-input
                  type="textarea"
                  disabled
                  v-model="form.bid_opening_location"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标方式:" prop="bid_opening_method">
                <el-input
                  type="textarea"
                  disabled
                  v-model="form.bid_opening_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="评标方法:" prop="evaluation_method">
                <el-input
                  type="textarea"
                  disabled
                  v-model="form.evaluation_method"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="附件信息" name="5">
          <el-upload
            ref="upload1"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="审核记录" name="6">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <!-- <div style="margin-top: 10px">
        <el-form
          size="mini"
          ref="auditForm"
          :model="auditForm"
          :rules="rules"
          label-width="180px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="审核备注" prop="approval_view">
                <el-input
                  :disabled="isDisabled"
                  type="textarea"
                  v-model="auditForm.approval_view"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核状态" prop="status">
                <el-radio-group
                  :disabled="isDisabled"
                  v-model="auditForm.status"
                >
                  <el-radio :label="2">审核通过</el-radio>
                  <el-radio :label="3">审核退回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>

        <!-- <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :disabled="isDisabled" @click="handleSubmit"
          >提交审核</el-button
        > -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import Tinymce from "@/components/Tinymce";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    type: {
      type: String,
      default: () => "",
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          if (val.status !== 1) {
            this.isDisabled = true;
          }
        }
      },
      deep: true,
    },
  },
  created() {},
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      isDisabled: false,
      auditForm: {},
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        bid_segment_id: null,
        bidding_project_name: null,
        segment_name: null,
        title: null,
        document_obtain_start: null,
        document_obtain_deadline: null,
        document_submission_deadline: null,
        bid_validity_period: null,
        requires_list_review: 1,
        bid_price_form: 1,
        submission_method: null,
        bid_opening_location: null,
        bid_opening_method: null,
        evaluation_method: null,
        attachments: [],
        status: 1,
      },
      innerVisible: false,
      selectSegmentShow: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        title: [
          {
            required: true,
            message: "请输入标题",
            trigger: ["blur", "change"],
          },
        ],
        document_obtain_start: [
          {
            required: true,
            message: "请选择招标文件获取开始时间",
            trigger: ["blur", "change"],
          },
        ],
      },
      selectProject: [],
      tableDataSegment: [],
      totalSegment: 0,
    };
  },
  methods: {
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              console.log(this.form);
              this.auditForm.document_id = this.form.id;
              this.auditForm.title = this.form.title;
              this.auditForm.company_id = this.form.bid_segment.company_info_id;
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
