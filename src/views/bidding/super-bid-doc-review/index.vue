<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 23:21:09
 * @FilePath: /bid/src/views/hall/bidder/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="title"
          label="标题"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bidding_project_name"
          label="招标/采购项目名称"
        ></el-table-column>
        <el-table-column
          prop="segment_name"
          label="标段名称"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="company_name"
          label="招标代理机构"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="bidder_name"
          label="招标文件附件"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <a
              v-if="downloadFn(scope.row).name"
              download
              :href="downloadFn(scope.row).url"
              >{{ downloadFn(scope.row).name }}</a
            >
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="approval_name"
          label="平台审核人"
        ></el-table-column>
        <el-table-column
          prop="approval_time"
          label="平台审核时间"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="80">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :fileList="fileList"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import {
  getAdminDocumnet,
  detailAdminDocumnet,
  approvalDocumnet,
} from "@/api/bid-manage/bid-doc";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";
import moment from "moment";

export default {
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      formData: {},
      fields: [
        {
          label: "标题：",
          prop: "title",
          component: "el-input",
          props: {
            placeholder: "请输入标题",
          },
        },
        // {
        //   label: "招标/采购项目名称：",
        //   prop: "social_credit_code",
        //   component: "el-input",
        //   props: {
        //     placeholder: "请输入招标项目名称",
        //   },
        // },
        // {
        //   label: "招标代理机构：",
        //   prop: "social_credit_code",
        //   component: "el-input",
        //   props: {
        //     placeholder: "请输入招标代理机构",
        //   },
        // },
        {
          label: "状态",
          prop: "status",
          component: "el-select",
          props: {
            placeholder: "请输入状态",
          },
          options: [
            { label: "全部", value: "" },
            { label: "已提交", value: "1" },
            { label: "审核通过", value: "2" },
            { label: "审核退回", value: "3" },
          ],
        },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
      industryOptions: [],
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "已提交";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    downloadFn(row) {
      let url = `http://bidding.senmoio.cn/${
        row.attachments?.length && row.attachments[0]?.path
      }`;
      let name = row.attachments?.length ? `${row.attachments[0]?.name}` : "";
      console.log("name, url", name, url);
      return { name, url };
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleDetail(data) {
      this.addShow = true;
      detailAdminDocumnet(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        if (this.editForm.attachments != null) {
          this.editForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },
    getList() {
      if (!this.queryForm.status) {
        delete this.queryForm.status;
      }
      getAdminDocumnet(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
        this.tableData.forEach((item) => {
          item.approval_time =
            item.approval_time &&
            moment(new Date(item.approval_time)).format("YYYY-MM-DD HH:mm:ss");
        });
      });
    },
    handleSubmit(data) {
      let formData = { ...data };
      approvalDocumnet(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.getList();
        this.addShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
