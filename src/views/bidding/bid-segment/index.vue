<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: 王子超
 * @LastEditTime: 2024-12-04 00:26:03
 * @FilePath: /bidding-web/src/views/bidding/bid-segment/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div>
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleBack">返回</el-button>
      <el-button type="primary" @click="handleAdd">新建标段</el-button>
      <!-- <el-button type="primary" @click="handleDownload">批量下载</el-button> -->
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <!-- <el-table-column label="编辑" fixed align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.$index, scope.row)"
              >编辑</el-button
            >
          </template>
        </el-table-column> -->
        <el-table-column
          prop="segment_number"
          label="标段编号"
        ></el-table-column>
        <el-table-column
          prop="segment_name"
          show-overflow-tooltip
          label="标段名称"
        ></el-table-column>

        <el-table-column
          prop="bidding_project.bidding_project_type"
          label="招标/采购项目类型"
        >
          <template slot-scope="scope">
            {{ setProjectText(scope.row) }}
          </template>
        </el-table-column>
        <!-- <el-table-column
          prop="segment_number"
          label="标段分类代码"
        ></el-table-column> -->
        <!-- <el-table-column
          prop="qualification_review"
          label="标段轮次"
        ></el-table-column> -->

        <el-table-column prop="status" label="标段状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleStatusManage(scope.row)"
              >状态查看</el-button
            >
            <!-- <el-button
              size="mini"
              type="primary"
              @click="handleFileManage(scope.row)"
              >档案管理</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <file-form
      v-if="fileShow"
      :visible="fileShow"
      :data="fileData"
      :formData="formData"
      @save="handleSaveFile"
      @close="fileShow = false"
    ></file-form>
    <status-info
      v-if="statusShow"
      :visible="statusShow"
      :infoForm="statusInfo"
      @close="statusShow = false"
      @init="initData"
    ></status-info>
  </div>
</template>

<script>
import {
  getBidProjectSegments,
  addBidProject,
  deleteBidProject,
  updateBidProject,
  addBidProjectSegments,
  getBidSegmentFiles,
  addBidSegmentFiles,
} from "@/api/bid-manage/bid-project";
import { projectTypeOptions, buyProjectTypeOptions } from "@/utils/data";
import SearchForm from "@/components/SearchForm/index.vue";
import StatusInfo from "@/components/StatusInfo/index.vue";
import AddForm from "./modules/add-form.vue";
import FileForm from "./modules/file-form.vue";

export default {
  components: {
    SearchForm,
    AddForm,
    FileForm,
    StatusInfo,
  },
  props: {
    id: {
      type: Number,
      default: () => 0,
    },
    name: {
      type: String,
      default: () => "",
    },
  },
  data() {
    return {
      addShow: false,
      fileShow: false,
      statusShow: false,
      statusInfo: {},
      formData: {
        doc: [],
      },
      bid_segment_id: "",
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fileData: {
        bidding_project_name: null,
        segment_name: null,
      },
      projectTypeOptions: projectTypeOptions,
      buyProjectTypeOptions: buyProjectTypeOptions,
      fields: [
        {
          label: "标段编号",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入招标段编号",
          },
        },
        {
          label: "标段名称",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "已提交";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleUpdate() {
      this.addShow = true;
    },
    handleAdd() {
      //   this.addShow = true;
      this.$emit("show", this.name, this.id);
    },
    handleSaveFile(data) {
      data.bid_segment_id = this.bid_segment_id;
      addBidSegmentFiles(data).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    getList() {
      getBidProjectSegments(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      addBidProjectSegments(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      addBidProject(formData).then((res) => {
        console.log(res);
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteBidProject(row.id).then((res) => {
            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    setProjectText(data) {
      let value = data.bidding_project.bidding_project_type;
      let projectCate = data.bidding_project.bidding_project_cate;
      if (projectCate == 1) {
        let obj = this.projectTypeOptions.filter((item) => {
          return item.value == value;
        });
        return obj[0].label;
      } else {
        let obj = this.buyProjectTypeOptions.filter((item) => {
          return item.value == value;
        });
        return obj[0].label;
      }
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
        this.fileShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    handleStatusManage(data) {
      this.statusInfo = data;
      this.statusShow = true;
    },
    initData() {
      this.statusShow = false;
      this.queryForm.bidding_project_id = this.id;
      this.getList();
    },
    handleFileManage(data) {
      console.log("teste", data);
      this.fileShow = true;
      this.fileData.bidding_project_name = this.name;
      this.fileData.segment_name = data.segment_name;
      this.bid_segment_id = data.id;
      getBidSegmentFiles(data.id).then((res) => {
        this.formData.bidding_project_name = this.name;
        this.formData.segment_name = data.segment_name;
        this.formData.doc = res.data.data;
        this.formData.doc.forEach((item) => {
          console.log(item);
          item.fileList = [];
          let obj = {
            name: item.file_path.name,
            url: item.file_path.path,
          };
          item.fileList.push(obj);
        });
        console.log(res);
      });
    },
    handleBack() {
      this.$emit("back", 0);
    },
    handleDownload() {},
  },
  created() {
    console.log(this.name);

    this.queryForm.bidding_project_id = this.id;
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
