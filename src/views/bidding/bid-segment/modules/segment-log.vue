<template>
  <div>
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleBack">返回</el-button>
      <el-button type="primary" @click="handleAdd">新建</el-button>
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>

        <el-table-column
          show-overflow-tooltip
          prop="bidding_project.bidding_project_name"
          label="招标/采购项目名称"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bid_segments[0].segment_name"
          label="标段名称"
        ></el-table-column>
        <el-table-column prop="user.name" label="创建人"></el-table-column>

        <el-table-column prop="created_at" label="创建时间"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >

            <el-button
              size="mini"
              type="danger"
              :disabled="scope.row.status == 1 || scope.row.status == 2"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <add-form
      v-if="addShow"
      :visible="addShow"
      :name="bidProjectName"
      :editForm="editForm"
      :fileList="fileList"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import {
  getBidSegmentsLog,
  addBidSegmentsLog,
  deleteBidSegmentsLog,
  updateBidSegmentsLog,
  getBidSegmentLogId,
} from "@/api/bid-manage/bid-project";
import SearchForm from "@/components/SearchForm/index.vue";
import BidSegments from "../../bid-segment/index.vue";
import AddForm from "./add-form.vue";
export default {
  components: {
    SearchForm,
    AddForm,
    BidSegments,
  },
  props: {
    name: {
      type: String,
      default: () => "",
    },
    bidProjectId: {
      type: Number,
      default: () => null,
    },
  },
  data() {
    return {
      editForm: {},
      fileList: [],
      addShow: false,
      pageModel: 0,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "招标/采购项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目编号",
          },
        },
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      bidProjectName: "",
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "已提交";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      console.log(this.name);
      this.bidProjectName = this.name;
      this.fileList = [];
      this.addShow = true;
    },
    handleBack() {
      this.$emit("back", 1);
    },
    handleUpdate(data) {
      this.addShow = true;
      getBidSegmentLogId(data.id).then((res) => {
        this.editForm = res.data;
        this.editForm.bidding_project_name = this.name;
        console.log(this.name);
        this.fileList = [];
        this.editForm.agency_contract_scan.forEach((item) => {
          let obj = {
            name: item.name,
            url: item.path,
          };
          this.fileList.push(obj);
        });
      });
    },
    getList() {
      this.queryForm.bidding_project_id = this.bidProjectId;
      getBidSegmentsLog(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      console.log(data);
      let formData = { ...data };
      formData.status = 0;
      formData.bidding_project_id = this.bidProjectId;
      if (data.id) {
        updateBidSegmentsLog(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidSegmentsLog(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      formData.bidding_project_id = this.bidProjectId;
      if (data.id) {
        updateBidSegmentsLog(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidSegmentsLog(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteBidSegmentsLog(row.id).then((res) => {
            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    console.log(this.name);
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
