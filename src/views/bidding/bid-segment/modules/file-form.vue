<template>
  <div>
    <el-dialog
      title="档案管理"
      :visible.sync="visible"
      width="90%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collList">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="segment_name">
                  <el-input
                    disabled
                    v-model="form.segment_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="归档信息" name="2">
          <div>
            <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addFiles"
                >添加</el-button
              >
              <el-button size="mini" type="danger" @click="handleBatchDelete()"
                >删除</el-button
              >
            </div>
            <el-table
              :data="form.doc"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="流程节点" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.process_node"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="上传人" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.user.name"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.upload_time"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="文件名">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.fileName"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column> -->
              <el-table-column label="附件">
                <template slot-scope="scope">
                  <el-upload
                    ref="upload1"
                    action="https://bidding.senmoio.cn/api/file"
                    :headers="header"
                    multiple
                    :limit="1"
                    :file-list="scope.row.fileList"
                    :accept="acceptTypes.join(',')"
                    :before-upload="beforeUpload"
                    :on-remove="handleRemove1"
                    :on-success="
                      (response, file, fileList) =>
                        handleSuccess1(
                          response,
                          file,
                          fileList,
                          scope.row.index
                        )
                    "
                  >
                    <el-button
                      slot="trigger"
                      :id="scope.row.index"
                      size="small"
                      type="primary"
                      >选取文件</el-button
                    >
                    <div slot="tip" class="el-upload__tip">
                      可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
                    </div>
                  </el-upload>
                </template>
              </el-table-column>

              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="120"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import moment from "moment";
export default {
  name: "FileForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    data: {
      type: Object,
      default: () => {},
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    formData: {
      handler(val) {
        if (val) {
          this.form.bidding_project_name = this.data.bidding_project_name;
          this.form.segment_name = this.data.segment_name;
          this.form = { ...val };
        }
      },
      deep: true,
    },
  },
  created() {},
  data() {
    return {
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collList: ["1", "2"],
      form: {
        doc: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      selectedRows: [],
    };
  },
  methods: {
    handleSelectionChange(data) {
      this.selectedRows = data;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    addFiles() {
      this.form.doc.push({
        process_node: null,
        user_id: null,
        user: {
          name: "",
        },
        upload_time: "",
        file_path: null,
        index: this.generateUniqueId(),
      });
    },
    generateUniqueId() {
      return Date.now().toString(36);
    },
    handleDelete(index, row) {
      this.form.doc.splice(index, 1);
    },
    handleBatchDelete() {
      this.selectedRows.forEach((item) => {
        let index = this.form.doc.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.doc.splice(index, 1);
        }
      });
    },

    handleSave() {
      console.log("save", this.form);
      this.$emit("save", this.form);
    },
    handleSuccess1(res, file, fileList, index) {
      console.log(this.$store.getters.user);
      this.form.doc.forEach((item, num) => {
        if (item.index == index) {
          console.log(this.$store.getters.name);
          this.form.doc[num].file_path = res.data;
          this.form.doc[num].user_id = this.$store.getters.user.id;
          this.form.doc[num].user.name = this.$store.getters.name;
          this.form.doc[num].upload_time = moment(new Date()).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
      });

      console.log(this.form.doc);
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleRemove1(file, fileList) {
      console.log(file, fileList);
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
