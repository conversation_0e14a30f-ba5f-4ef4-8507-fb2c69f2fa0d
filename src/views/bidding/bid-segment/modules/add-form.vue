<template>
  <div>
    <el-dialog
      title="招标项目标段表单"
      :visible.sync="visible"
      width="90%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="委托代理协议扫描件" name="2" v-if="edit">
          <el-upload
            ref="upload1"
            action="https://bidding.yztc2025.com/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="委托代理协议扫描件" name="2" v-else>
          <el-upload
            ref="upload1"
            action="#"
            :file-list="fileList"
            :on-preview="handlePreview"
          >
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="标段" name="3">
          <div>
            <div style="text-align: right">
              <el-button
                size="mini"
                type="primary"
                @click="addBidSegment"
                v-if="edit"
                >添加</el-button
              >
              <el-button
                size="mini"
                type="danger"
                @click="handleBatchDelete()"
                v-if="edit"
                >删除</el-button
              >
            </div>
            <el-table
              :data="form.bid_segments"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" width="180">
                <template slot-scope="scope">
                  <el-input
                    :disabled="!edit"
                    v-model="scope.row.segment_number"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="标段名称" width="180">
                <template slot-scope="scope">
                  <el-input
                    :disabled="!edit"
                    v-model="scope.row.segment_name"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="标段分类代码" width="180">
                <template slot-scope="scope">
                  <el-input
                    :disabled="!edit"
                    v-model="scope.row.segment_code"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column> -->
              <el-table-column label="是否双盲" width="180">
                <template slot-scope="scope">
                  <el-select
                    :disabled="!edit"
                    v-model="scope.row.is_blind"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="否" :value="0"></el-option>
                    <el-option label="是" :value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="标段内容">
                <template slot-scope="scope">
                  <el-input
                    :disabled="!edit"
                    v-model="scope.row.segment_content"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="合同估价（元）">
                <template slot-scope="scope">
                  <el-input
                    :disabled="!edit"
                    v-model="scope.row.estimated_contract_price"
                    size="small"
                    @input="
                      handlePriceInput(
                        $event,
                        scope.row,
                        'estimated_contract_price'
                      )
                    "
                    @keypress="handlePriceKeypress"
                    placeholder="请输入金额"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="预开工日期" width="200">
                <template slot-scope="scope">
                  <el-date-picker
                    :disabled="!edit"
                    v-model="scope.row.start_date"
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column label="工期（天）">
                <template slot-scope="scope">
                  <el-input
                    :disabled="!edit"
                    v-model="scope.row.duration_days"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="投标资格">
                <template slot-scope="scope">
                  <el-input
                    :disabled="!edit"
                    v-model="scope.row.bid_qualification"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="120"
              >
                <template v-if="edit" slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDeleteBid(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button
          type="primary"
          :disabled="form.status === 2"
          @click="handleSave"
          >保存</el-button
        >
        <el-button
          type="primary"
          :disabled="form.status === 2"
          @click="handleSubmit"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "AddForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    name: {
      type: String,
      default: () => "",
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          if (this.form.status == 1 || this.form.status == 2) {
            this.edit = false;
          }
        }
      },
      deep: true,
    },
  },
  created() {
    console.log(this.name);
    if (!this.form.id) {
      this.form.bidding_project_name = this.name;
    }
  },
  data() {
    return {
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3"],
      edit: true,
      form: {
        agency_contract_scan: [],
        approval_department: null,
        bid_segments: [],
        bidding_agent_scope: null,
        bidding_method: null,
        bidding_project_code: null,
        bidding_project_establishment_date: null,
        bidding_project_implementation_location: [],
        bidding_project_industry_classification: null,
        bidding_project_location: [],
        bidding_project_name: null,
        bidding_project_type: null,
        investment_amount: null,
        joint_bidding: null,
        owner_unit: null,
        project_code: null,
        project_industry_classification: [],
        project_initiation_date: null,
        project_legal_person: null,
        project_name: null,
        project_qualification_overview: null,
        project_team_assignment: [],
        qualification_review: null,
        status: null,
        supervisory_department: null,
        supervisory_department_type: null,
        organizational_form: "委托招标",
        bidding_agent: "招标公司",
        bidder: null,
        bidding_agent_scope: null,
        bidding_agent_authority: null,
        task_plan: null,
        bidding_project_id: null,
      },
      titlesOptions: [],
      divisionsOptions: [],
      innerVisible: false,
      total: 0,
      selectedRows: [],
      queryForm: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },

      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        bidding_project_name: [
          { required: true, message: "请输入招标项目名称", trigger: "blur" },
        ],
      },
      selectProject: [],
    };
  },
  methods: {
    // 处理价格输入，限制只能输入数字和最多两位小数
    handlePriceInput(value, row, field) {
      // 移除非数字和小数点的字符
      let cleanValue = value.replace(/[^\d.]/g, "");

      // 确保只有一个小数点
      const parts = cleanValue.split(".");
      if (parts.length > 2) {
        cleanValue = parts[0] + "." + parts.slice(1).join("");
      }

      // 限制小数点后最多两位
      if (parts.length === 2 && parts[1].length > 2) {
        cleanValue = parts[0] + "." + parts[1].substring(0, 2);
      }

      // 更新值
      row[field] = cleanValue;
    },

    // 处理按键输入，阻止非法字符
    handlePriceKeypress(event) {
      const char = String.fromCharCode(event.which);
      const value = event.target.value;

      // 允许数字
      if (/\d/.test(char)) {
        return true;
      }

      // 允许小数点，但只能有一个，且不能在开头
      if (char === "." && value.indexOf(".") === -1 && value.length > 0) {
        return true;
      }

      // 阻止其他字符
      event.preventDefault();
      return false;
    },

    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.yztc2025.com/${file.url}`;
      window.open(url, "_blank");
    },
    handleSelectionChange(data) {
      this.selectedRows = data;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    addBidSegment() {
      this.form.bid_segments.push({
        segment_number: null,
        segment_name: null,
        segment_code: null,
        is_blind: null,
        segment_content: null,
        estimated_contract_price: null,
        start_date: "",
        duration_days: null,
        bid_qualification: null,
        index: this.generateUniqueId(),
      });
    },
    handleBatchDelete() {
      this.selectedRows.forEach((item) => {
        let index = this.form.bid_segments.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.bid_segments.splice(index, 1);
        }
      });
    },
    generateUniqueId() {
      return Date.now().toString(36);
    },
    handleDeleteBid(index, row) {
      this.form.bid_segments.splice(index, 1);
    },
    handleSave() {
      console.log("save", this.form);
      this.$emit("save", this.form);
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$emit("submit", this.form);
    },
    handleSuccess(res, file, fileList) {
      this.form.agency_contract_scan.push(res.data);
      console.log(res, file, fileList);
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleRemove(file, fileList) {
      let index = this.form.agency_contract_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.agency_contract_scan.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
