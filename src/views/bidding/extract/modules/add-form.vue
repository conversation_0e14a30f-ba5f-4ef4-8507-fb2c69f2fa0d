<template>
  <div>
    <el-dialog
      title="专家抽取审核"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-dialog
        v-if="expertVisible"
        width="80%"
        title="选择专家"
        :visible.sync="expertVisible"
        append-to-body
      >
        <!-- <el-card>
          <search-form
            :fields="fields"
            :itemsPerRow="2"
            @search="handleSearch"
          ></search-form>
        </el-card> -->
        <el-card style="margin-top: 10px">
          <el-table
            ref="expertTable"
            :data="tableDataExpert"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectExpert"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="name" label="专家名称"></el-table-column>
          </el-table>
          <!-- <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div> -->
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCloseExpert">取 消</el-button>
          <el-button type="primary" @click="handleSaveExpert">确定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        v-if="this.innerVisible"
        width="80%"
        title="选择招标项目"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fields"
            :itemsPerRow="2"
            @search="handleSearch"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableData"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectProject"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="bidding_project_code"
              label="招标/采购项目编号"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="bidding_project_name"
              label="招标/采购项目名称"
            ></el-table-column>
            <el-table-column
              prop="project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSaveProject">确定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        v-if="selectSegmentShow"
        width="80%"
        title="选择标段"
        :visible.sync="selectSegmentShow"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fieldsSegment"
            :itemsPerRow="2"
            @search="handleSearchSegment"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableDataSegment"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectSegment"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="segment_number"
              label="标段编号"
            ></el-table-column>
            <el-table-column
              prop="segment_name"
              label="标段名称"
            ></el-table-column>
            <el-table-column
              prop="bidding_project.bidding_project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryFormSegment.per_page"
              :current-page="queryFormSegment.page"
              @size-change="handleChangeSizeSegment"
              @current-change="handleChangePageSegment"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCloseSegment">取 消</el-button>
          <el-button type="primary" @click="handleSaveSegment">确定</el-button>
        </span>
      </el-dialog>
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                    <el-button
                      v-if="!isDisabled"
                      size="mini"
                      @click="handleShowProject"
                      >选择</el-button
                    >
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="标段" name="2">
          <div>
            <div style="text-align: right" v-if="!isDisabled">
              <el-button size="mini" type="primary" @click="addBidSegment"
                >选择标段</el-button
              >
            </div>
            <el-table
              :data="form.segments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" prop="segment_number">
              </el-table-column>
              <el-table-column label="标段名称" prop="segment_name">
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="开评标信息" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="开标时间:" prop="opening_date">
                  <div style="display: flex">
                    <el-date-picker
                      :disabled="isDisabled"
                      v-model="form.opening_date"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开标地点:" prop="opening_location">
                  <el-select
                    :disabled="isDisabled"
                    v-model="form.opening_location"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="评标时间:" prop="evaluation_date">
                  <div style="display: flex">
                    <el-date-picker
                      :disabled="isDisabled"
                      v-model="form.evaluation_date"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评标地点:" prop="evaluation_location">
                  <el-select
                    :disabled="isDisabled"
                    v-model="form.evaluation_location"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="注意事项:" prop="notes">
                  <el-input
                    :disabled="isDisabled"
                    type="textarea"
                    v-model="form.notes"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="单位信息" name="4">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位:" prop="owner_unit">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.owner_unit"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="owner_representative">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.owner_representative"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="owner_representative_phone">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.owner_representative_phone"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标代理机构:" prop="tender_agency">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.tender_agency"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="agency_representative">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.agency_representative"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="agency_representative_phone">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.agency_representative_phone"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="监督单位:" prop="supervision_unit">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.supervision_unit"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="supervisor">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.supervisor"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="supervisor_phone">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.supervisor_phone"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="招标内容:" prop="tender_content">
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.tender_content"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="抽取要求" name="5">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="260px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目所在地:" prop="project_location">
                  <el-cascader
                    :disabled="isDisabled"
                    v-model="form.project_location"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="抽取终端:" prop="extraction_terminal">
                  <el-select
                    :disabled="isDisabled"
                    v-model="form.extraction_terminal"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="随机抽取" value="0"> </el-option>
                    <el-option key="1" label="逐条抽取" value="1"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="本次抽取项目或标段投资额（元）:"
                  prop="investment_amount"
                >
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.investment_amount"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="拟抽取日期:"
                  prop="intended_selection_date"
                >
                  <div style="display: flex">
                    <el-date-picker
                      v-model="form.intended_selection_date"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item
                  label="评标委员会人数:"
                  prop="committee_member_count"
                >
                  <el-input
                    :disabled="isDisabled"
                    v-model="form.committee_member_count"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
          <div>
            <div style="text-align: right" v-if="!isDisabled">
              <el-button size="mini" type="primary" @click="addPerson"
                >添加</el-button
              >
              <el-button size="mini" type="danger" @click="handleBatchDelete1"
                >删除</el-button
              >
            </div>
            <el-table
              :data="form.expert_selection_conditions"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange1"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="地区" width="180">
                <template slot-scope="scope">
                  <el-select
                    :disabled="isDisabled"
                    v-model="scope.row.geo_area_id"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="专家类别" width="180">
                <template slot-scope="scope">
                  <el-cascader
                    :disabled="isDisabled"
                    v-model="scope.row.expert_type"
                    :props="expertProps"
                    :options="expertOptions"
                    placeholder="请选择"
                  ></el-cascader>
                </template>
              </el-table-column>
              <el-table-column label="专家人数" width="180">
                <template slot-scope="scope">
                  <el-input-number
                    :disabled="isDisabled"
                    :min="1"
                    :max="100"
                    v-model.number="scope.row.expert_count"
                    size="small"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="报到时间" width="230">
                <template slot-scope="scope">
                  <div style="display: flex">
                    <el-date-picker
                      v-model="scope.row.report_time"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报到地点" width="180">
                <template slot-scope="scope"
                  ><el-input
                    :disabled="isDisabled"
                    v-model="scope.row.report_location"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="评标用时（天）" width="180">
                <template slot-scope="scope">
                  <el-input-number
                    :disabled="isDisabled"
                    :min="1"
                    :max="9999"
                    v-model.number="scope.row.evaluation_duration"
                    size="small"
                  ></el-input-number>
                </template>
              </el-table-column>
              <!-- <el-table-column label="抽取状态" width="180">
                <template slot-scope="scope">
                  {{ scope.row.selection_status ? "已完成" : "未完成" }}
                </template>
              </el-table-column> -->
              <el-table-column label="专家姓名" width="180">
                <template slot-scope="scope">
                  <el-input
                    :disabled="isDisabled"
                    readonly
                    v-model="scope.row.expertList"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="180"
              >
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="handleGetExpert(scope.$index, scope.row)"
                    >抽取</el-button
                  >
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDeleteTeam(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer" v-if="!isDisabled">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="primary" @click="handleSubmit">递交生效</el-button>
      </span>
      <span slot="footer" class="dialog-footer" v-else>
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBidProjectList,
  getBidProjectSegments,
  generateAnnouncement,
} from "@/api/bid-manage/bid-project";
import { getGeoAreas } from "@/api/bid-manage/project-info";
import { getExpertTypes } from "@/api/expert/expert";
import { extractExpertList, getCity } from "@/api/bid-manage/extract";
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import Tinymce from "@/components/Tinymce";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    type: {
      type: String,
      default: () => "",
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          val = JSON.parse(JSON.stringify(val));
          val.expert_selection_conditions?.forEach((item) => {
            item.experts = item.expert_selection_logs.filter((v) => {
              return (v.expert_selection_condition_id = item.id);
            });
            item.expertList = item.experts
              ?.map((j) => {
                return j.expert_name;
              })
              .join(",");
          });
          this.form = { ...val };
          console.log(this.form, "val.expert_selection_conditions");
          if (val.project_detail?.length) {
            this.form.bidding_project_name =
              val.project_detail[0].bidding_project_name;
            this.form.bidding_project_code =
              val.project_detail[0].bidding_project_code;
          }
          this.queryFormSegment.bidding_project_id =
            this.form.bidding_project_id;
          console.log(this.type);

          if (this.type == "view") {
            this.isDisabled = true;
          } else {
            this.isDisabled = false;
          }
        }
      },
      immediate: true,
      deep: true,
    },
    // type: {
    //   handler(val) {
    //     console.log(val);

    //     if (val == "edit") {
    //       this.isDisabled = false;
    //     } else {
    //       this.isDisabled = true;
    //     }
    //   },
    // },
    innerVisible: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
    selectSegmentShow: {
      handler(val) {
        if (val) {
          this.handleSearchSegment();
        }
      },
    },
  },
  created() {},
  data() {
    return {
      isDisabled: false,
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      addressProps: {
        label: "name",
        value: "id",
      },
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        bidding_project_name: null,
        bidding_project_code: null,
        opening_date: null,
        opening_location: null,
        evaluation_date: null,
        evaluation_location: null,
        notes: null,
        owner_unit: null,
        owner_representative: null,
        owner_representative_phone: null,
        tender_agency: null,
        agency_representative: null,
        agency_representative_phone: null,
        supervision_unit: null,
        supervisor: null,
        supervisor_phone: null,
        tender_content: null,
        project_location: null,
        extraction_terminal: null,
        investment_amount: null,
        intended_selection_date: null,
        committee_member_count: null,
        segments: [],
        expert_selection_conditions: [],
        submission_status: 0,
      },
      innerVisible: false,
      selectSegmentShow: false,
      expertVisible: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      expertIndex: 0,
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      tableDataExpert: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        bidding_project_name: [
          {
            required: true,
            message: "请选择招标项目",
            trigger: ["blur", "change"],
          },
        ],
        bidding_project_code: [
          {
            required: true,
            message: "请输入招标项目编号",
            trigger: ["blur", "change"],
          },
        ],
        opening_date: [
          {
            required: true,
            message: "请选择开标时间:",
            trigger: ["blur", "change"],
          },
        ],
        opening_location: [
          {
            required: true,
            message: "请选择开标地点:",
            trigger: ["blur", "change"],
          },
        ],
        opening_location: [
          {
            required: true,
            message: "请选择开标地点:",
            trigger: ["blur", "change"],
          },
        ],
        evaluation_date: [
          {
            required: true,
            message: "请选择评标时间",
            trigger: ["blur", "change"],
          },
        ],
        evaluation_location: [
          {
            required: true,
            message: "请选择评标地点",
            trigger: ["blur", "change"],
          },
        ],
        notes: [
          {
            required: true,
            message: "请输入注意事项",
            trigger: ["blur", "change"],
          },
        ],
        owner_unit: [
          {
            required: true,
            message: "请输入业主单位",
            trigger: ["blur", "change"],
          },
        ],
        owner_representative: [
          {
            required: true,
            message: "请输入负责人",
            trigger: ["blur", "change"],
          },
        ],
        tender_agency: [
          {
            required: true,
            message: "请输入招标代理机构",
            trigger: ["blur", "change"],
          },
        ],
        agency_representative: [
          {
            required: true,
            message: "请输入负责人",
            trigger: ["blur", "change"],
          },
        ],
        supervision_unit: [
          {
            required: true,
            message: "请输入监督单位",
            trigger: ["blur", "change"],
          },
        ],
        supervisor: [
          {
            required: true,
            message: "请输入负责人",
            trigger: ["blur", "change"],
          },
        ],
        tender_content: [
          {
            required: true,
            message: "请输入招标内容",
            trigger: ["blur", "change"],
          },
        ],
        owner_representative_phone: [
          {
            required: true,
            message: "请输入电话",
            trigger: ["blur", "change"],
          },
        ],
        agency_representative_phone: [
          {
            required: true,
            message: "请输入电话",
            trigger: ["blur", "change"],
          },
        ],
        supervisor_phone: [
          {
            required: true,
            message: "请输入电话",
            trigger: ["blur", "change"],
          },
        ],
        project_location: [
          {
            required: true,
            message: "请选择招标项目所在地",
            trigger: ["blur", "change"],
          },
        ],
        extraction_terminal: [
          {
            required: true,
            message: "请选择抽取终端",
            trigger: ["blur", "change"],
          },
        ],
        investment_amount: [
          {
            required: true,
            message: "请输入本次抽取项目或标段投资额",
            trigger: ["blur", "change"],
          },
        ],
        intended_selection_date: [
          {
            required: true,
            message: "请选择拟抽取日期",
            trigger: ["blur", "change"],
          },
        ],
        committee_member_count: [
          {
            required: true,
            message: "请输入评标委员会人数",
            trigger: ["blur", "change"],
          },
        ],
      },
      expertProps: {
        label: "name",
        value: "id",
      },
      selectProject: [],
      selectExpert: [],
      tableDataSegment: [],
      cityOptions: [],
      expertOptions: [],
      addressOptions: [],
      totalSegment: 0,
    };
  },
  created() {
    this.getGeoExpert();
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSelectionChange() {},
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.handleGetProjectList();
    },
    handleSearchSegment(filters) {
      Object.assign(this.queryFormSegment, filters);
      this.handleGetSegmentList();
    },
    handleGetSegmentList() {
      getBidProjectSegments(this.queryFormSegment).then((res) => {
        this.tableDataSegment = res.data.data;
        this.totalSegment = res.data.total;
      });
    },
    getGeoExpert() {
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      getExpertTypes().then((res) => {
        this.expertOptions = res.data;
      });
      getCity().then((res) => {
        this.cityOptions = res.data;
      });
    },
    handleGetProjectList() {
      getBidProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleGetExpert(index, row) {
      // 校验必填字段
      const validationErrors = [];

      // 校验地区
      if (
        !row.geo_area_id ||
        (Array.isArray(row.geo_area_id) && row.geo_area_id.length === 0)
      ) {
        validationErrors.push("地区");
      }

      // 校验专家类别
      if (
        !row.expert_type ||
        (Array.isArray(row.expert_type) && row.expert_type.length === 0)
      ) {
        validationErrors.push("专家类别");
      }

      // 校验专家人数
      if (!row.expert_count || row.expert_count <= 0) {
        validationErrors.push("专家人数");
      }

      // 如果有校验错误，显示提示信息并返回
      if (validationErrors.length > 0) {
        this.$message.warning(`请先填写或选择：${validationErrors.join("、")}`);
        return;
      }

      this.expertIndex = index;
      let params = {
        geo_area_id: row.geo_area_id,
        expert_type: row.expert_type,
        expert_count: row.expert_count,
      };

      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: "正在获取专家列表...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      extractExpertList(params)
        .then((res) => {
          loading.close();
          if (res.data && res.data.expert && res.data.expert.data) {
            this.tableDataExpert = res.data.expert.data;
            console.log(this.tableDataExpert, "this.tableDataExpert");

            if (this.tableDataExpert.length === 0) {
              this.$message.warning("未找到符合条件的专家，请调整筛选条件");
              return;
            }

            this.expertVisible = true;
            this.$message.success(
              `找到 ${this.tableDataExpert.length} 位符合条件的专家`
            );
          } else {
            this.$message.error("获取专家列表失败，请重试");
          }
        })
        .catch((error) => {
          loading.close();
          console.error("获取专家列表失败:", error);
          this.$message.error("获取专家列表失败，请检查网络连接或联系管理员");
        });
    },
    handleSelectExpert(data) {
      // 获取当前行的专家人数限制
      const currentRow =
        this.form.expert_selection_conditions[this.expertIndex];
      const maxExpertCount = currentRow.expert_count || 0;

      // 检查选中的专家数量是否超过限制
      if (data.length > maxExpertCount) {
        this.$message.warning(`最多只能选择 ${maxExpertCount} 位专家`);
        // 只保留前面符合数量限制的专家
        const limitedData = data.slice(0, maxExpertCount);
        this.selectExpert = limitedData;

        // 更新表格选中状态
        this.$nextTick(() => {
          const table = this.$refs.expertTable;
          if (table && table.clearSelection) {
            table.clearSelection();
            limitedData.forEach((expert) => {
              const tableData = this.tableDataExpert;
              const index = tableData.findIndex(
                (item) => item.id === expert.id
              );
              if (index !== -1) {
                table.toggleRowSelection(tableData[index], true);
              }
            });
          }
        });
        return;
      }

      this.selectExpert = data;
      console.log(data);
    },
    handleSaveExpert() {
      // 获取当前行的专家人数限制
      const currentRow =
        this.form.expert_selection_conditions[this.expertIndex];
      const maxExpertCount = currentRow.expert_count || 0;

      // 最终验证选中的专家数量
      if (this.selectExpert.length > maxExpertCount) {
        this.$message.error(
          `选中的专家数量(${this.selectExpert.length})不能超过设定的专家人数(${maxExpertCount})`
        );
        return;
      }

      if (this.selectExpert.length === 0) {
        this.$message.warning("请至少选择一位专家");
        return;
      }

      let name = this.selectExpert.map((item) => {
        return item.name;
      });
      this.form.expert_selection_conditions[this.expertIndex].expertList =
        name.join(",");
      let arr = this.selectExpert.map((item) => {
        return { expert_id: item.id, expert_name: item.name };
      });
      console.log(arr, "arr");
      this.form.expert_selection_conditions[this.expertIndex].experts = arr;
      this.expertVisible = false;
      this.$message.success(`成功选择了 ${this.selectExpert.length} 位专家`);
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetProjectList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.handleGetProjectList();
    },

    handleChangeSizeSegment(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetSegmentList();
    },
    handleChangePageSegment(page) {
      this.queryForm.page = page;
      this.handleGetSegmentList();
    },

    handleSelectProject(data) {
      this.selectProject = data;
      console.log(data);
    },
    handleSelectSegment(data) {
      if (data.length > 1) {
        this.$message.warning("只能选择一个标段");
        return;
      }
      this.form.segments = data;
    },
    handleSelectionChange1(data) {
      this.selectedRows1 = data;
    },
    handleSaveProject() {
      if (this.selectProject.length != 1) {
        this.$message.warning("只能选择一个项目");
        return;
      }
      this.$set(
        this.form,
        "bidding_project_name",
        this.selectProject[0].bidding_project_name
      );
      this.$set(
        this.form,
        "bidding_project_code",
        this.selectProject[0].bidding_project_code
      );
      // this.form.bidding_project_name =
      //   this.selectProject[0].bidding_project_name;
      // this.form.bidding_project_code =
      //   this.selectProject[0].bidding_project_code;
      this.form.bidding_project_id = this.selectProject[0].id;
      this.queryFormSegment.bidding_project_id = this.selectProject[0].id;
      this.innerVisible = false;
    },

    handleSaveSegment() {
      // this.form.segments;
      // if (this.form.segments.length === 1) {
      //   this.$message.warning("只可以选择一个标段！");
      //   return;
      // }
      this.selectSegmentShow = false;
    },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleCloseExpert() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.expertVisible = false;
          //   done();
        })
        .catch((_) => {});
    },
    handleCloseSegment() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.selectSegmentShow = false;
          //   done();
        })
        .catch((_) => {});
    },
    addPerson() {
      this.form.expert_selection_conditions.push({
        geo_area_id: null,
        expert_type: null,
        expert_count: null,
        report_time: null,
        report_location: null,
        evaluation_duration: null,
        expertList: null,
        experts: [],
      });
    },

    handleDeleteTeam(index, row) {
      this.form.expert_selection_conditions.splice(index, 1);
    },
    handleBatchDelete1() {
      this.selectedRows1.forEach((item) => {
        let index = this.form.expert_selection_conditions.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.expert_selection_conditions.splice(index, 1);
        }
      });
    },
    addBidSegment() {
      console.log(this.form.bidding_project_name);

      if (
        this.form.bidding_project_name == "" ||
        this.form.bidding_project_name == null
      ) {
        this.$message.warning("请先选择招标项目");
        return;
      }
      this.selectSegmentShow = true;
    },

    handleSave() {
      console.log("save", this.form);
      // this.form.bid_segment_ids = [10];
      this.$emit("save", this.form);
    },
    handleSubmit() {
      console.log("submit", this.form);

      this.$emit("submit", this.form);
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachment.splice(index, 1);
      }
    },
    handleShowProject() {
      this.innerVisible = true;
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
