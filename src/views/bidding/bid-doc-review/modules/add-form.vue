<template>
  <div>
    <el-dialog
      title="招标文件"
      :visible.sync="visible"
      width="80%"
      append-to-body
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="归属信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目名称:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="segment_name">
                  <el-input
                    disabled
                    v-model="form.segment_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="文件信息" name="2">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col>
                <el-form-item label="标题:" prop="title">
                  <el-input
                    v-model="form.title"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取开始时间:"
                  prop="document_obtain_start"
                >
                  <el-date-picker
                    v-model="form.document_obtain_start"
                    type="datetime"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取截止时间:"
                  prop="document_obtain_deadline"
                >
                  <el-date-picker
                    v-model="form.document_obtain_deadline"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="投标文件递交截止时间:"
                  prop="document_submission_deadline"
                >
                  <el-date-picker
                    v-model="form.document_submission_deadline"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="投标有效期(天):"
                  prop="bid_validity_period"
                >
                  <el-input
                    v-model="form.bid_validity_period"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item
                  label="是否需要清单评审:"
                  prop="requires_list_review"
                >
                  <el-radio-group
                    v-model="form.requires_list_review"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                  >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
              <el-col :span="8">
                <el-form-item label="投标报价价款形式:" prop="bid_price_form">
                  <el-radio-group
                    v-model="form.bid_price_form"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                  >
                    <el-radio :label="1">金额</el-radio>
                    <el-radio :label="2">费率</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
              <el-col :span="8">
                <el-form-item label="是否需要报名:" prop="is_need_sign_up">
                  <el-radio-group
                    v-model="form.is_need_sign_up"
                    :disabled="
                      ['detail', 'superDetail'].includes(type) || isView
                    "
                  >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
            </el-row>
            <el-row>
              <el-form-item label="投标文件提交方法:" prop="submission_method">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type) || isView"
                  v-model="form.submission_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标地点:" prop="bid_opening_location">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type) || isView"
                  v-model="form.bid_opening_location"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标方式:" prop="bid_opening_method">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type) || isView"
                  v-model="form.bid_opening_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="评标方法:" prop="evaluation_method">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type) || isView"
                  v-model="form.evaluation_method"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="招标文件" name="3">
          <el-upload
            ref="upload1"
            action="#"
            v-if="['detail'].includes(type) || isView"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
          <el-upload
            ref="upload1"
            v-else
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">可以上传的文件后缀为zip</div>
          </el-upload>
        </el-collapse-item>
        <!-- v-if="['detail'].includes(type) || isView" -->
        <el-collapse-item title="审核记录" name="4" v-if="false">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button
          v-if="['add', 'edit'].includes(type) && !isView"
          @click="handleClose"
          >取 消</el-button
        >
        <el-button v-else @click="handleClose">关 闭</el-button>
        <el-button
          type="primary"
          @click="handleSave"
          v-if="['add', 'edit'].includes(type) && !isView"
          >保存</el-button
        >
        <el-button
          type="primary"
          @click="handleSubmit"
          v-if="['add', 'edit'].includes(type) && !isView"
          >提交</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import Tinymce from "@/components/Tinymce";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    type: {
      type: String,
      default: () => "",
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    isView: {
      type: Boolean,
      default: () => false,
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          if (this.type != "add") {
            this.form = { ...val };
          }
        }
      },
      deep: true,
    },
  },
  created() {
    if (this.type == "add") {
      this.form.bidding_project_name =
        this.editForm.bidding_project.bidding_project_name;
      this.form.segment_name = this.editForm.segment_name;
    }
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      acceptTypes: [".zip"],
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        bid_segment_id: null,
        bidding_project_name: null,
        segment_name: null,
        title: null,
        document_obtain_start: null,
        document_obtain_deadline: null,
        document_submission_deadline: null,
        bid_validity_period: null,
        requires_list_review: 1,
        bid_price_form: 1,
        submission_method: null,
        bid_opening_location: null,
        bid_opening_method: null,
        evaluation_method: null,
        attachments: [],
        status: 1,
      },
      innerVisible: false,
      selectSegmentShow: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        title: [
          {
            required: true,
            message: "请输入标题",
            trigger: ["blur", "change"],
          },
        ],
        document_obtain_start: [
          {
            required: true,
            message: "请选择招标文件获取开始时间",
            trigger: ["blur", "change"],
          },
        ],
        document_obtain_deadline: [
          {
            required: true,
            message: "请选择招标文件获取截止时间",
            trigger: ["blur", "change"],
          },
        ],
        document_submission_deadline: [
          {
            required: true,
            message: "请选择招标文件递交截止时间",
            trigger: ["blur", "change"],
          },
        ],
        bid_validity_period: [
          {
            required: true,
            message: "请输入投标有效期",
            trigger: ["blur", "change"],
          },
        ],
        requires_list_review: [
          {
            required: true,
            message: "请选择是否需要清单评审",
            trigger: ["blur", "change"],
          },
        ],
        bid_price_form: [
          {
            required: true,
            message: "请选择投标报价价款形式",
            trigger: ["blur", "change"],
          },
        ],
        is_need_sign_up: [
          {
            required: true,
            message: "请选择是否需要报名",
            trigger: ["blur", "change"],
          },
        ],
        submission_method: [
          {
            required: true,
            message: "请输入投标文件提交方法",
            trigger: ["blur", "change"],
          },
        ],
        bid_opening_location: [
          {
            required: true,
            message: "请输入开标地点",
            trigger: ["blur", "change"],
          },
        ],
        bid_opening_method: [
          {
            required: true,
            message: "请输入开标方式",
            trigger: ["blur", "change"],
          },
        ],
        evaluation_method: [
          {
            required: true,
            message: "请输入评标方法",
            trigger: ["blur", "change"],
          },
        ],
        attachments: [
          {
            required: true,
            message: "请选择附件",
            trigger: ["blur", "change"],
          },
        ],
      },
      selectProject: [],
      tableDataSegment: [],
      totalSegment: 0,
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSave() {
      console.log("save", this.form);
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.$emit("save", this.form);
        } else {
          return false;
        }
      });
    },
    handleSubmit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.$emit("submit", this.form);
        } else {
          return false;
        }
      });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachments.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachments.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachments.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
