<template>
  <div>
    <el-dialog
      title="招标项目表单"
      :visible.sync="visible"
      width="90%"
      :before-close="handleClose"
    >
      <el-dialog
        v-if="this.innerVisible"
        width="80%"
        title="选择项目"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fields"
            :itemsPerRow="2"
            @search="handleSearch"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableData"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectProject"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="project_code"
              label="项目编号"
            ></el-table-column>
            <el-table-column
              prop="project_name"
              label="项目名称"
            ></el-table-column>
            <el-table-column
              prop="project_legal_person"
              label="项目法人"
            ></el-table-column>
            <el-table-column
              prop="investment_amount"
              label="投资金额（元）"
            ></el-table-column>
            <el-table-column
              prop="project_initiation_date"
              label="立项时间"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSaveProject">确定</el-button>
        </span>
      </el-dialog>
      <el-collapse v-model="collapse">
        <el-collapse-item title="基本信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="24">
                <el-form-item label="所属项目:" prop="project_name">
                  <div style="display: flex">
                    <el-input v-model="form.project_name" disabled></el-input>
                    <el-button size="mini" @click="handleShowProject"
                      >选择</el-button
                    >
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目分类:" prop="bidding_project_cate">
                  <el-select
                    v-model="form.bidding_project_cate"
                    placeholder="请选择"
                  >
                    <el-option key="1" label="工程" :value="1"> </el-option>
                    <el-option key="2" label="采购" :value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 2"
                  :label="labelText + '方式'"
                  prop="bidding_project_purchase_method"
                >
                  <el-select
                    v-model="form.bidding_project_purchase_method"
                    placeholder="请选择"
                  >
                    <el-option key="1" label="竞争性谈判" :value="1">
                    </el-option>
                    <el-option key="2" label="竞争性磋商" :value="2">
                    </el-option>
                    <el-option key="3" label="询比采购" :value="3"> </el-option>
                    <el-option key="4" label="单一来源" :value="4"> </el-option>
                    <el-option key="5" label="公开招标" :value="5"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  :label="labelText + '项目编号:'"
                  prop="bidding_project_code"
                >
                  <el-input
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  :label="labelText + '项目名称:'"
                  prop="bidding_project_name"
                >
                  <el-input
                    v-model="form.bidding_project_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 1"
                  :label="labelText + '项目类型:'"
                  prop="bidding_project_type"
                >
                  <el-select
                    v-model="form.bidding_project_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in projectTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                    <!-- 其他选项可以在这里添加 -->
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-else
                  :label="labelText + '项目类型:'"
                  prop="bidding_project_type"
                >
                  <el-select
                    v-model="form.bidding_project_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in buyProjectTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                    <!-- 其他选项可以在这里添加 -->
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item
                  :label="labelText + '项目行业分类:'"
                  prop="bidding_project_industry_classification"
                >
                  <el-select
                    v-model="form.bidding_project_industry_classification"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in industryProjectOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item
                  label="项目行业分类:"
                  prop="bidding_project_industry_classification"
                >
                  <el-cascader
                    v-model="form.bidding_project_industry_classification"
                    :props="industryProps"
                    :options="industryOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 1"
                  :label="labelText + '形式:'"
                  prop="bidding_method"
                >
                  <el-select v-model="form.bidding_method" placeholder="请选择">
                    <el-option key="0" label="公开招标" value="0"> </el-option>
                    <el-option key="1" label="邀请招标" value="1"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资格审查:" prop="qualification_review">
                  <el-select
                    v-model="form.qualification_review"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="资格后审" :value="1"> </el-option>
                    <el-option key="1" label="资格预审" :value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联合体招标:" prop="joint_bidding">
                  <el-radio-group v-model="form.joint_bidding">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item></el-col
              >
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 1"
                  label="监督部门类型："
                  prop="supervisory_department_type"
                >
                  <el-select
                    v-model="form.supervisory_department_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in departmentTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-else
                  label="监督部门类型："
                  prop="supervisory_department_type"
                >
                  <el-select
                    v-model="form.supervisory_department_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in buyDepartmentTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="监督部门:" prop="supervisory_department">
                  <el-input
                    v-model="form.supervisory_department"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="审核部门:" prop="approval_department">
                  <el-input
                    v-model="form.approval_department"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="业主单位:" prop="owner_unit">
                  <el-input v-model="form.owner_unit"></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标项目地点:"
                  prop="bidding_project_location"
                >
                  <el-cascader
                    v-model="form.bidding_project_location"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标项目建立时间:"
                  prop="bidding_project_establishment_date"
                >
                  <el-date-picker
                    v-model="form.bidding_project_establishment_date"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标项目实施地点:"
                  prop="bidding_project_implementation_location"
                >
                  <el-cascader
                    v-model="form.bidding_project_implementation_location"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item
                label="项目资格概况:"
                prop="project_qualification_overview"
              >
                <el-input
                  type="textarea"
                  v-model="form.project_qualification_overview"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item
                label="招标内容与范围及招标方案说明:"
                prop="bidding_scope_and_plan_description"
              >
                <el-input
                  type="textarea"
                  v-model="form.bidding_scope_and_plan_description"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="组织形式" name="2">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标组织形式:" prop="organizational_form">
                  <el-input
                    disabled
                    v-model="form.organizational_form"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="招标代理:" prop="bidding_agent">
                  <el-input
                    v-model="form.bidding_agent"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标人:" prop="bidder">
                  <div style="display: flex">
                    <el-input v-model="form.bidder"></el-input>
                    <el-button
                      type="primary"
                      size="mini"
                      @click="handleCheckBidder"
                      >查验</el-button
                    >
                    <el-button type="primary" size="mini" @click="handleAdd"
                      >新建招标人</el-button
                    >
                  </div>
                </el-form-item></el-col
              >
            </el-row>
            <el-row>
              <el-form-item
                label="招标代理内容与范围:"
                prop="bidding_agent_scope"
              >
                <el-input
                  type="textarea"
                  v-model="form.bidding_agent_scope"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item
                label="招标代理权限:"
                prop="bidding_agent_authority"
              >
                <el-input
                  type="textarea"
                  v-model="form.bidding_agent_authority"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="成员与计划" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-form-item label="工作任务及计划:" prop="task_plan">
                <el-input
                  type="textarea"
                  v-model="form.task_plan"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="团队成员及分工" name="4">
          <div>
            <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addPerson"
                >添加</el-button
              >
              <el-button size="mini" type="danger" @click="handleBatchDelete1"
                >删除</el-button
              >
            </div>
            <el-table
              :data="form.project_team_assignments"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange1"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="姓名" width="180">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="联系电话" width="180">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.phone" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="性别" width="180">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.gender" placeholder="请选择">
                    <el-option label="男" :value="0"></el-option>
                    <el-option label="女" :value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="职务">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.position"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="组长" value="0"></el-option>
                    <el-option label="组员" value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="职称">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.title_id" placeholder="请选择">
                    <el-option
                      v-for="item in titlesOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="职称等级" width="180">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.title_level"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="初级" value="0"></el-option>
                    <el-option label="中级" value="1"></el-option>
                    <el-option label="高级" value="2"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <!-- <el-table-column label="分工明细" width="180">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.division_id"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option
                      v-for="item in divisionsOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="业绩要求">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.performance_requirements"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column> -->
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="120"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDeleteTeam(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="委托代理协议扫描件" name="5">
          <el-upload
            ref="upload1"
            action="https://bidding.yztc2025.com/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="标段" name="6">
          <div>
            <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addBidSegment"
                >添加</el-button
              >
              <el-button size="mini" type="danger" @click="handleBatchDelete2"
                >删除</el-button
              >
            </div>
            <el-table
              :data="form.bid_segments"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange2"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.segment_number"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="标段名称" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.segment_name"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="标段分类代码" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.segment_code"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column> -->
              <el-table-column label="是否双盲" width="180">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.is_blind"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="否" :value="0"></el-option>
                    <el-option label="是" :value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="标段内容">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.segment_content"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="合同估价（元）">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.estimated_contract_price"
                    size="small"
                    @input="
                      handlePriceInput(
                        $event,
                        scope.row,
                        'estimated_contract_price'
                      )
                    "
                    @keypress="handlePriceKeypress"
                    placeholder="请输入金额"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="预开工日期" width="200">
                <template slot-scope="scope">
                  <el-date-picker
                    v-model="scope.row.start_date"
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column label="工期（天）">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.duration_days"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="投标资格">
                <template slot-scope="scope">
                  <el-input
                    v-model="scope.row.bid_qualification"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="120"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDeleteBid(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="操作记录" name="7">
          <el-table :data="modificationRecords" border style="margin-top: 10px">
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="modifier_name"
              label="操作人"
            ></el-table-column>
            <el-table-column
              prop="created_at"
              label="操作时间"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="modificationTotal"
              :page-size="modificationQueryForm.per_page"
              :current-page="modificationQueryForm.page"
              @size-change="handleModificationChangeSize"
              @current-change="handleModificationChangePage"
            >
            </el-pagination>
          </div>
        </el-collapse-item>
        <el-collapse-item title="审核记录" name="8" v-if="false">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{
          form.status !== 2 ? "取 消" : "关 闭"
        }}</el-button>
        <el-button v-if="form.status !== 2" type="primary" @click="handleSave"
          >保存</el-button
        >
        <el-button v-if="form.status !== 2" type="primary" @click="handleSubmit"
          >提交</el-button
        >
      </span>
    </el-dialog>

    <add-form
      v-if="addShow"
      :visible="addShow"
      :formData="formData"
      @submit="handleAddSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import { addBidder } from "@/api/hall/bidder";
import { getProjectList } from "@/api/bid-manage/project-info";
import AddForm from "@/views/bidding/bidder/modules/add-form.vue";
import {
  getTitles,
  getDivisions,
  getBidderByName,
} from "@/api/bid-manage/bid-project";
import {
  projectTypeOptions,
  industryProjectOptions,
  departmentTypeOptions,
  buyProjectTypeOptions,
  buyDepartmentTypeOptions,
} from "@/utils/data";
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import { getModificationRecords } from "@/api/hall/bidder";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    AddForm,
  },
  props: {
    editForm: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    addressOptions: {
      type: Array,
    },
    industryOptions: {
      type: Array,
      default: () => [],
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          // 获取操作记录
          this.getModificationRecords();
        }
      },
      deep: true,
    },
    innerVisible: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
    "form.bidding_project_cate": {
      handler(val) {
        if (val == 1) {
          this.labelText = "招标";
        } else {
          this.labelText = "采购";
        }
      },
    },
  },
  created() {
    getTitles().then((res) => {
      this.titlesOptions = res.data.data;
    });
    getDivisions().then((res) => {
      this.divisionsOptions = res.data.data;
    });
  },
  data() {
    return {
      addShow: false,
      formData: {},
      labelText: "招标",
      fileList1: [],
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],

      collapse: ["1", "2", "3", "4", "5", "6", "7", "8"],
      form: {
        agency_contract_scan: [],
        approval_department: null,
        bid_segments: [],
        bidding_agent_scope: null,
        bidding_method: null,
        bidding_project_code: null,
        bidding_project_establishment_date: null,
        bidding_project_implementation_location: [],
        bidding_project_industry_classification: null,
        bidding_project_location: [],
        bidding_project_name: null,
        bidding_project_type: null,
        investment_amount: null,
        joint_bidding: null,
        owner_unit: null,
        project_code: null,
        project_industry_classification: [],
        project_initiation_date: null,
        project_legal_person: null,
        project_name: null,
        project_qualification_overview: null,
        project_team_assignments: [],
        qualification_review: 1,
        status: null,
        supervisory_department: null,
        supervisory_department_type: null,
        organizational_form: "委托招标",
        bidding_agent: "招标公司",
        bidder: null,
        bidding_agent_scope: null,
        bidding_agent_authority: null,
        task_plan: null,
      },
      titlesOptions: [],
      divisionsOptions: [],
      innerVisible: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      logData: [],
      totalLog: 0,
      queryFormLog: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      projectTypeOptions: projectTypeOptions,
      buyProjectTypeOptions: buyProjectTypeOptions,
      industryProjectOptions: industryProjectOptions,
      departmentTypeOptions: departmentTypeOptions,
      buyDepartmentTypeOptions: buyDepartmentTypeOptions,
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      addressProps: {
        label: "name",
        value: "id",
      },
      industryProps: {
        label: "name",
        value: "id",
      },
      selectedRows1: [],
      selectedRows2: [],
      loading: false,
      modificationRecords: [],
      modificationTotal: 0,
      modificationQueryForm: {
        page: 1,
        per_page: 10,
        entity_type: "bidding_project",
      },
      rules: {
        bidder: [
          {
            required: true,
            message: "请选择招标人",
            trigger: ["blur", "change"],
          },
        ],
        project_name: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: ["blur", "change"],
          },
        ],
        bidding_project_name: [
          {
            required: true,
            message: `请输入`,
            trigger: "blur",
          },
        ],
        bidding_project_code: [
          {
            required: true,
            message: `请输入`,
            trigger: "blur",
          },
        ],

        bidding_project_industry_classification: [
          {
            required: true,
            message: `请选择`,
            trigger: "change",
          },
        ],
        // supervisory_department_type: [
        //   {
        //     required: true,
        //     message: "请选择监督部门类型",
        //     trigger: "change",
        //   },
        // ],

        qualification_review: [
          {
            required: true,
            message: "请选择资格审核",
            trigger: "change",
          },
        ],
        bidding_project_type: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],

        // supervisory_department: [
        //   { required: true, message: "请输入监督部门", trigger: "blur" },
        // ],
        // approval_department: [
        //   { required: true, message: "请输入审核部门", trigger: "blur" },
        // ],
        owner_unit: [
          { required: true, message: "请输入业主单位", trigger: "blur" },
        ],
        bidding_project_location: [
          { required: true, message: "请选择招标项目地点", trigger: "change" },
        ],
        // bidding_project_establishment_date: [
        //   {
        //     required: true,
        //     message: "请选择招标项目建立时间",
        //     trigger: "change",
        //   },
        // ],
        // bidding_project_implementation_location: [
        //   {
        //     required: true,
        //     message: "请选择招标项目实施地点",
        //     trigger: "change",
        //   },
        // ],
        project_qualification_overview: [
          { required: true, message: "请输入项目资格概况", trigger: "blur" },
        ],
        bidding_agent_scope: [
          {
            required: true,
            message: "请输入招标内容与范围及招标方案说明",
            trigger: "blur",
          },
        ],
        bidding_project_cate: [
          {
            required: true,
            message: "请选择项目分类",
            trigger: "change",
          },
        ],
        bidding_project_purchase_method: [
          {
            required: true,
            message: `请选择${this.labelText}方式`,
            trigger: "change",
          },
        ],
      },
      selectProject: [],
    };
  },
  methods: {
    // 处理价格输入，限制只能输入数字和最多两位小数
    handlePriceInput(value, row, field) {
      // 移除非数字和小数点的字符
      let cleanValue = value.replace(/[^\d.]/g, "");

      // 确保只有一个小数点
      const parts = cleanValue.split(".");
      if (parts.length > 2) {
        cleanValue = parts[0] + "." + parts.slice(1).join("");
      }

      // 限制小数点后最多两位
      if (parts.length === 2 && parts[1].length > 2) {
        cleanValue = parts[0] + "." + parts[1].substring(0, 2);
      }

      // 更新值
      row[field] = cleanValue;
    },

    // 处理按键输入，阻止非法字符
    handlePriceKeypress(event) {
      const char = String.fromCharCode(event.which);
      const value = event.target.value;

      // 允许数字
      if (/\d/.test(char)) {
        return true;
      }

      // 允许小数点，但只能有一个，且不能在开头
      if (char === "." && value.indexOf(".") === -1 && value.length > 0) {
        return true;
      }

      // 阻止其他字符
      event.preventDefault();
      return false;
    },

    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSelectionChange1(data) {
      this.selectedRows1 = data;
    },
    handleSelectionChange2(data) {
      this.selectedRows2 = data;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.handleGetProjectList();
    },
    handleGetProjectList() {
      getProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetProjectList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.handleGetProjectList();
    },
    handleSelectProject(data) {
      this.selectProject = data;
      console.log(data);
    },
    handleAdd() {
      this.addShow = true;
    },
    handleSaveProject() {
      if (this.selectProject.length > 1) {
        this.$message.warning("只能选择一个项目");
        return;
      }
      this.form.project_name = this.selectProject[0].project_name;
      this.form.project_id = this.selectProject[0].id;
      this.innerVisible = false;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    addPerson() {
      this.form.project_team_assignments.push({
        name: null,
        phone: null,
        gender: null,
        position: null,
        title_id: null,
        title_level: null,
        division_id: null,
        performance_requirements: null,
        index: this.generateUniqueId(),
      });
    },
    addBidSegment() {
      this.form.bid_segments.push({
        segment_number: null,
        segment_name: null,
        segment_code: null,
        is_blind: null,
        segment_content: null,
        estimated_contract_price: null,
        start_date: "",
        duration_days: null,
        bid_qualification: null,
        index: this.generateUniqueId(),
      });
    },

    handleDeleteTeam(index, row) {
      this.form.project_team_assignments.splice(index, 1);
    },

    handleBatchDelete1() {
      this.selectedRows1.forEach((item) => {
        let index = this.form.project_team_assignments.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.project_team_assignments.splice(index, 1);
        }
      });
    },
    handleBatchDelete2() {
      this.selectedRows2.forEach((item) => {
        let index = this.form.bid_segments.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.bid_segments.splice(index, 1);
        }
      });
    },
    generateUniqueId() {
      return Date.now().toString(36);
    },
    handleDeleteBid(index, row) {
      this.form.bid_segments.splice(index, 1);
    },

    handleSave() {
      console.log("save", this.form);
      this.$emit("save", this.form);
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$emit("submit", this.form);
    },
    handleFileChange1(file, fileList) {
      console.log(this.form.agency_contract_scan);
      console.log(file, fileList);
    },
    handleSuccess1(res, file, fileList) {
      this.form.agency_contract_scan.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.agency_contract_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.agency_contract_scan.splice(index, 1);
      }
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleShowProject() {
      this.innerVisible = true;
    },
    handleCheckBidder() {
      if (this.form.bidder == null || this.form.bidder == "") {
        this.$message.warning("请输入招标人");
        return;
      }
      getBidderByName(this.form.bidder).then((res) => {
        this.message(res.status_code, "查验成功");
      });
    },
    // handleSaveAdd(data) {
    //   let formData = { ...data };
    //   formData.status = 0;
    //   if (data.id) {
    //     updateBidder(data.id, formData).then((res) => {
    //       this.message(res.status_code, res.message);
    //     });
    //   } else {
    //     addBidder(formData).then((res) => {
    //       this.message(res.status_code, res.message);
    //     });
    //   }
    // },
    handleAddSubmit(data) {
      let formData = { ...data };
      formData.status = 1;

      addBidder(formData).then((res) => {
        this.addShow = false;
        this.message(res.status_code, res.message);
      });
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    handleModificationChangePage(page) {
      this.modificationQueryForm.page = page;
      this.getModificationRecords();
    },
    handleModificationChangeSize(number) {
      this.modificationQueryForm.page = 1;
      this.modificationQueryForm.per_page = number;
      this.getModificationRecords();
    },
    getModificationRecords() {
      if (this.form.id) {
        this.modificationQueryForm.entity_id = this.form.id;
        getModificationRecords(this.modificationQueryForm)
          .then((res) => {
            this.modificationRecords = res.data.data || [];
            this.modificationTotal = res.data.total || 0;
          })
          .catch((error) => {
            console.error("获取操作记录失败:", error);
          });
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
