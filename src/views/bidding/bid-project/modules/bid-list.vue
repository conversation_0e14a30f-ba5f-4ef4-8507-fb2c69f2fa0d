<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 20:35:13
 * @FilePath: /bid/src/views/bidding/bid-project/modules/bid-list.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div>
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleAdd">新建</el-button>
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          fixed="left"
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="编辑" fixed align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >

            <el-button
              size="mini"
              type="danger"
              :disabled="scope.row.status == 2"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="bidding_project_code"
          width="200"
          label="招标/采购项目编号"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bidding_project_name"
          width="200"
          label="招标/采购项目名称"
        ></el-table-column>
        <el-table-column
          prop="project_name"
          show-overflow-tooltip
          width="200"
          label="项目名称"
        ></el-table-column>
        <el-table-column
          prop="bidding_project_type"
          width="180"
          label="招标/采购项目类型"
        >
          <template slot-scope="scope">
            {{ setProjectText(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="organizational_form"
          label="招标组织形式"
          width="200"
        ></el-table-column>
        <el-table-column prop="qualification_review" label="资格审查">
          <template slot-scope="scope">
            {{ scope.row.qualification_review == 1 ? "资格后审" : "资格预审" }}
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="user"
          label="操作人"
        ></el-table-column>

        <el-table-column prop="status" label="招标状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right" align="center" width="280">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleShow(scope.$index, scope.row)"
              >公告信息</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="handleShowSegment(scope.$index, scope.row)"
              >标段信息</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <project-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :addressOptions="addressOptions"
      :industryOptions="industryOptions"
      :fileList="fileList"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></project-form>
  </div>
</template>

<script>
import {
  getBidProjectList,
  addBidProject,
  deleteBidProject,
  updateBidProject,
  getBidProjectById,
} from "@/api/bid-manage/bid-project";
import { getIndustries, getGeoAreas } from "@/api/bid-manage/project-info";
import { projectTypeOptions, buyProjectTypeOptions } from "@/utils/data";
import SearchForm from "@/components/SearchForm/index.vue";
import ProjectForm from "./bid-form.vue";
import BidSegments from "../../bid-segment/index.vue";
export default {
  components: {
    SearchForm,
    ProjectForm,
    BidSegments,
  },
  data() {
    return {
      editForm: {},
      addShow: false,
      pageModel: 0,
      fileList: [],
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      projectTypeOptions: projectTypeOptions,
      buyProjectTypeOptions: buyProjectTypeOptions,
      fields: [
        {
          label: "招标/采购项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
      industryOptions: [],
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "已提交";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      getIndustries().then((res) => {
        this.industryOptions = res.data;
      });
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      this.addShow = true;
      this.fileList = [];
    },
    handleUpdate(data) {
      getIndustries().then((res) => {
        this.industryOptions = res.data;
      });
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      getBidProjectById(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        this.editForm.agency_contract_scan.forEach((item) => {
          let obj = {
            name: item.name,
            url: item.path,
          };
          this.fileList.push(obj);
        });
      });
      this.addShow = true;
    },
    getList() {
      getBidProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      console.log(data);
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        updateBidProject(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidProject(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 1;
      if (data.id) {
        updateBidProject(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidProject(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteBidProject(row.id).then((res) => {
            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    setProjectText(data) {
      let value = data.bidding_project_type;
      let projectCate = data.bidding_project_cate;
      if (projectCate == 1) {
        let obj = this.projectTypeOptions.filter((item) => {
          return item.value == value;
        });
        return obj[0].label;
      } else {
        let obj = this.buyProjectTypeOptions.filter((item) => {
          return item.value == value;
        });
        return obj[0].label;
      }
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
    handleShowSegment(index, data) {
      this.$emit("show", 1, data);
    },
    handleShow() {
      this.$router.push("/bidding/bid-announcement");
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
