<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 20:32:28
 * @FilePath: /bid/src/views/bidding/bid-project/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <div v-if="pageModel == 0">
      <bid-list @show="handleShowSegment"></bid-list>
    </div>
    <div v-else-if="pageModel == 1">
      <bid-segments
        :id="bidProjectId"
        :name="bidProjectName"
        @show="handleShowBidLog"
        @back="handleBack"
      ></bid-segments>
    </div>
    <div v-else-if="pageModel == 2">
      <segment-log
        :name="bidProjectName"
        :bidProjectId="bidProjectId"
        @back="handleBack"
      ></segment-log>
    </div>
  </div>
</template>

<script>
import BidSegments from "../bid-segment/index.vue";
import BidList from "./modules/bid-list.vue";
import SegmentLog from "../bid-segment/modules/segment-log.vue";
export default {
  components: {
    BidSegments,
    BidList,
    SegmentLog,
  },
  data() {
    return {
      pageModel: 0,
      bidProjectId: null,
      bidProjectName: "",
    };
  },
  methods: {
    handleShowBidLog(name, id) {
      this.bidProjectId = id;
      this.bidProjectName = name;
      this.pageModel = 2;
    },
    handleShowSegment(index, data) {
      console.log(data);
      this.bidProjectId = data.id;
      this.bidProjectName = data.bidding_project_name;
      this.pageModel = index;
    },
    handleBack(indx) {
      this.pageModel = indx;
    },
  },
  created() {},
};
</script>
<style scoped>
.main {
  padding: 10px;
}
</style>
