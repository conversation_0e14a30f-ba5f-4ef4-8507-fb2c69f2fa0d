<template>
  <div>
    <el-dialog
      title="招标人维护"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标人" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标人名称:" prop="bidder_name">
                  <el-input
                    v-model="form.bidder_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="统一社会信用代码:"
                  prop="social_credit_code"
                >
                  <el-input
                    v-model="form.social_credit_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="单位性质:" prop="unit_type">
                  <el-select v-model="form.unit_type" placeholder="请选择">
                    <el-option key="0" label="企业" value="0"> </el-option>
                    <el-option key="1" label="机关法人" value="1"> </el-option>
                    <el-option key="2" label="事业单位" value="2"> </el-option>
                    <el-option key="3" label="社会组织" value="3"> </el-option>
                    <el-option key="99" label="其他" value="99"> </el-option>
                  </el-select> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="法人代表:" prop="legal_representative">
                  <el-input
                    v-model="form.legal_representative"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="法人身份证:" prop="legal_id_card">
                  <el-input
                    v-model="form.legal_id_card"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="地址:" prop="address">
                  <el-input v-model="form.address"></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系人:" prop="contact_person">
                  <el-input
                    v-model="form.contact_person"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="联系电话:" prop="contact_phone">
                  <el-input
                    v-model="form.contact_phone"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="电子邮箱:" prop="email">
                  <el-input v-model="form.email"></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="基本户开户银行:" prop="basic_account_bank">
                  <el-input
                    v-model="form.basic_account_bank"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="基本户账号:" prop="basic_account_number">
                  <el-input
                    v-model="form.basic_account_number"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label=" 注册资本:" prop="registered_capital">
                  <el-input
                    v-model="form.registered_capital"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
        <el-button type="primary" @click="handleSubmit">递交生效</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "AddForm",
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    formData: {
      handler(val) {
        console.log(val);
        if (val) {
          this.form = { ...this.formData };
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      collapse: ["1"],
      form: {
        status: 0,
      },
      loading: false,
      rules: {
        bidder_name: [
          { required: true, message: "请输入招标人名称", trigger: "blur" },
        ],
        social_credit_code: [
          {
            required: true,
            message: "请输入统一社会信用代码",
            trigger: "blur",
          },
        ],
        unit_type: [
          { required: true, message: "请选择单位性质", trigger: "change" },
        ],
        legal_representative: [
          { required: true, message: "请输入法人代表", trigger: "blur" },
        ],

        address: [{ required: true, message: "请输入地址", trigger: "blur" }],
        contact_person: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],
        contact_phone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSave() {
      console.log("save", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.form);
        }
      });
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
