<!--
 * @Author: wzc
 * @Date: 2024-10-28 23:34:09
 * @LastEditors: wzc
 * @LastEditTime: 2024-10-28 23:34:53
 * @FilePath: /bid/src/views/expert/audit/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          width="150"
          prop="bidding_project_name"
          label="招标/采购项目名称"
        >
          <template slot-scope="scope">
            {{ scope.row.project_detail[0].bidding_project_name }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bidding_project_code"
          width="150"
          label="招标/采购项目编号"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{ scope.row.project_detail[0].bidding_project_code }}
          </template>
        </el-table-column>
        <el-table-column
          prop="segment_name"
          label="标段名称"
          width="180"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{
              scope.row.bid_segments.length > 0
                ? scope.row.bid_segments[0].segment_name
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="extraction_terminal"
          label="标段编码"
          width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            {{
              scope.row.bid_segments.length > 0
                ? scope.row.bid_segments[0].segment_number
                : ""
            }}
          </template>
        </el-table-column>
        <el-table-column
          prop="investment_amount"
          label="本次抽取项目投资额（元）"
          width="210"
        ></el-table-column>
        <el-table-column
          width="150"
          prop="intended_selection_date"
          label="拟抽取时间"
        ></el-table-column>
        <el-table-column
          width="150"
          prop="committee_member_count"
          label="评标委员会人数"
        ></el-table-column>
        <el-table-column
          width="150"
          prop="opening_date"
          label="开标时间"
        ></el-table-column>
        <el-table-column
          width="150"
          prop="evaluation_date"
          label="评标时间"
        ></el-table-column>
        <el-table-column prop="submission_status" label="状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.submission_status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="280">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleDetail(scope.row)"
              >查看</el-button
            >
            <el-button size="mini" type="primary" @click="handleView(scope.row)"
              >专家邀请详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :fileList="fileList"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
    <el-dialog title="专家邀请详情" :visible.sync="expertVisible" width="50%">
      <el-table :data="expertList" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column prop="expert_name" label="专家姓名"></el-table-column>
        <el-table-column prop="selection_status" label="邀请详情">
          <template slot-scope="scope">
            <span>{{ setText(scope.row.selection_status) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getApprovalList,
  approvalExpertList,
  updateExtraction,
  detailApproval,
  addExtraction,
} from "@/api/bid-manage/extract";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";
import moment from "moment";
import { updateExpert } from "@/api/expert/expert";

export default {
  name: "Tab",
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      expertVisible: false,
      expertList: [],
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      formData: {},
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
        {
          label: "状态",
          prop: "status",
          component: "el-select",
          props: {
            placeholder: "请输入状态",
          },
          options: [
            { label: "全部", value: "" },
            { label: "待审核", value: "1" },
            { label: "审核通过", value: "2" },
            { label: "审核退回", value: "3" },
          ],
        },
      ],
      tableData: [], // 过滤后的数据
      tabMapOptions: [
        { label: "未审核", key: "0" },
        { label: "已审核", key: "1" },
      ],
      total: 0,
      activeName2: "0",
      queryForm: {
        page: 1,
        per_page: 10,
      },
      createdTimes: 0,
    };
  },
  watch: {
    activeName(val) {
      this.$router.push(`${this.$route.path}?tab=${val}`);
    },
  },
  created() {
    // init the default selected tab
    const tab = this.$route.query.tab;
    if (tab) {
      this.activeName = tab;
    }
    this.getList();
  },
  methods: {
    handleView(data) {
      this.expertVisible = true;
      this.expertList = data.expert_selection_log;
    },
    setText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "未接受";
          break;
        case 1:
          text = "已接受";
          break;
        case 2:
          text = "拒绝";
          break;

        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    showCreatedTimes() {
      this.createdTimes = this.createdTimes + 1;
    },
    handleAdd() {
      this.addShow = true;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    downloadFn(row) {
      let url = `http://bidding.yztc2025.com/${
        row.attachments?.length && row.attachments[0]?.path
      }`;
      let name = row.attachments?.length ? `${row.attachments[0]?.name}` : "";
      console.log("name, url", name, url);
      return { name, url };
    },
    handleDetail(data) {
      this.addShow = true;
      detailAdminDocumnet(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        if (this.editForm.attachments != null) {
          this.editForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },
    handleDetail(data) {
      this.addShow = true;
      detailApproval(data.id).then((res) => {
        let data = res.data;
        this.editForm = data;
        this.editForm.segments = data.bid_segments;
      });
    },
    getList() {
      getApprovalList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
        this.tableData.forEach((item) => {
          item.approval_time =
            item.approval_time &&
            moment(new Date(item.approval_time)).format("YYYY-MM-DD HH:mm:ss");
        });
      });
    },
    handleSubmit(data) {
      let formData = { ...data };
      approvalExpertList(formData).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        delete formData.bid_segments;
        delete formData.project_detail;
        delete formData.expert_selection_log;
        console.log();
        updateExtraction(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addExtraction(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deteleExtraction(row.id).then((res) => {
            console.log(res);

            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.getList();
        this.addShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
.tab-container {
  margin: 30px;
}
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
