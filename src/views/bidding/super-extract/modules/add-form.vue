<template>
  <div>
    <el-dialog
      title="专家抽取表单"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-dialog
        v-if="expertVisible"
        width="80%"
        title="选择专家"
        :visible.sync="expertVisible"
        append-to-body
      >
        <!-- <el-card>
          <search-form
            :fields="fields"
            :itemsPerRow="2"
            @search="handleSearch"
          ></search-form>
        </el-card> -->
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableDataExpert"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectExpert"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="name" label="专家名称"></el-table-column>
          </el-table>
          <!-- <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div> -->
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCloseExpert">取 消</el-button>
          <el-button type="primary" @click="handleSaveExpert">确定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        v-if="this.innerVisible"
        width="80%"
        title="选择招标项目"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fields"
            :itemsPerRow="2"
            @search="handleSearch"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableData"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectProject"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="bidding_project_code"
              label="招标/采购项目编号"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="bidding_project_name"
              label="招标/采购项目名称"
            ></el-table-column>
            <el-table-column
              prop="project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSaveProject">确定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        v-if="selectSegmentShow"
        width="80%"
        title="选择标段"
        :visible.sync="selectSegmentShow"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fieldsSegment"
            :itemsPerRow="2"
            @search="handleSearchSegment"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableDataSegment"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectSegment"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="segment_number"
              label="标段编号"
            ></el-table-column>
            <el-table-column
              prop="segment_name"
              label="标段名称"
            ></el-table-column>
            <el-table-column
              prop="bidding_project.bidding_project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryFormSegment.per_page"
              :current-page="queryFormSegment.page"
              @size-change="handleChangeSizeSegment"
              @current-change="handleChangePageSegment"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCloseSegment">取 消</el-button>
          <el-button type="primary" @click="handleSaveSegment">确定</el-button>
        </span>
      </el-dialog>
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="标段" name="2">
          <div>
            <!-- <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addBidSegment"
                >选择标段</el-button
              >
            </div> -->
            <el-table
              :data="form.segments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" prop="segment_number">
              </el-table-column>
              <el-table-column label="标段名称" prop="segment_name">
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="开评标信息" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="开标时间:" prop="opening_date">
                  <div style="display: flex">
                    <el-date-picker
                      v-model="form.opening_date"
                      type="datetime"
                      disabled
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开标地点:" prop="opening_location">
                  <el-select
                    v-model="form.opening_location"
                    placeholder="请选择"
                    disabled
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="评标时间:" prop="evaluation_date">
                  <div style="display: flex">
                    <el-date-picker
                      disabled
                      v-model="form.evaluation_date"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="评标地点:" prop="evaluation_location">
                  <el-select
                    v-model="form.evaluation_location"
                    placeholder="请选择"
                    disabled
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="注意事项:" prop="notes">
                  <el-input
                    type="textarea"
                    disabled
                    v-model="form.notes"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="单位信息" name="4">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="业主单位:" prop="owner_unit">
                  <el-input
                    v-model="form.owner_unit"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="owner_representative">
                  <el-input
                    v-model="form.owner_representative"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="owner_representative_phone">
                  <el-input
                    v-model="form.owner_representative_phone"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标代理机构:" prop="tender_agency">
                  <el-input
                    v-model="form.tender_agency"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="agency_representative">
                  <el-input
                    v-model="form.agency_representative"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="agency_representative_phone">
                  <el-input
                    v-model="form.agency_representative_phone"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="监督单位:" prop="supervision_unit">
                  <el-input
                    v-model="form.supervision_unit"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="负责人:" prop="supervisor">
                  <el-input
                    v-model="form.supervisor"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电话:" prop="supervisor_phone">
                  <el-input
                    v-model="form.supervisor_phone"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="招标内容:" prop="tender_content">
                  <el-input
                    v-model="form.tender_content"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="抽取要求" name="5">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="240px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目所在地:" prop="project_location">
                  <el-cascader
                    v-model="form.project_location"
                    :props="addressProps"
                    :options="addressOptions"
                    disabled
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="抽取终端:" prop="extraction_terminal">
                  <el-select
                    v-model="form.extraction_terminal"
                    disabled
                    placeholder="请选择"
                  >
                    <el-option key="0" label="随机抽取" value="0"> </el-option>
                    <el-option key="1" label="逐条抽取" value="1"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="本次抽取项目或标段投资额（元）:"
                  prop="investment_amount"
                >
                  <el-input
                    v-model="form.investment_amount"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="拟抽取日期:"
                  prop="intended_selection_date"
                >
                  <div style="display: flex">
                    <el-date-picker
                      v-model="form.intended_selection_date"
                      disabled
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item
                  label="评标委员会人数:"
                  prop="committee_member_count"
                >
                  <el-input
                    v-model="form.committee_member_count"
                    disabled
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
          <div>
            <!-- <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addPerson"
                >添加</el-button
              >
              <el-button size="mini" type="danger" @click="handleBatchDelete1"
                >删除</el-button
              >
            </div> -->
            <el-table
              :data="form.expert_selection_conditions"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange1"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="地区" width="180">
                <template slot-scope="scope">
                  <el-select
                    v-model="scope.row.geo_area_id"
                    disabled
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option
                      v-for="item in cityOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="专家类别" width="180">
                <template slot-scope="scope">
                  <el-cascader
                    v-model="scope.row.expert_type"
                    disabled
                    :props="expertProps"
                    :options="expertOptions"
                    placeholder="请选择"
                  ></el-cascader>
                </template>
              </el-table-column>
              <el-table-column label="专家人数" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model.number="scope.row.expert_count"
                    disabled
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="报到时间" width="230">
                <template slot-scope="scope">
                  <div style="display: flex">
                    <el-date-picker
                      v-model="scope.row.report_time"
                      disabled
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="选择时间"
                      :picker-options="pickerOptions"
                    ></el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="报到地点" width="180">
                <template slot-scope="scope"
                  ><el-input
                    v-model="scope.row.report_location"
                    disabled
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="评标用时（天）" width="180">
                <template slot-scope="scope">
                  <el-input
                    v-model.number="scope.row.evaluation_duration"
                    disabled
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="抽取状态" width="180">
                <template slot-scope="scope">
                  {{ scope.row.selection_status ? "已完成" : "未完成" }}
                </template>
              </el-table-column>
              <el-table-column label="专家姓名" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.expertList"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="操作"   fixed="right" align="center" width="180">
                <template slot-scope="scope">
                  <el-button
                    type="primary"
                    size="mini"
                    @click="handleGetExpert(scope.$index, scope.row)"
                    >抽取</el-button
                  >
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDeleteTeam(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </el-collapse-item>
        <!-- <el-collapse-item title="审核记录" name="6">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
         
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item> -->
      </el-collapse>
      <!-- <div style="margin-top: 10px">
        <el-form
          size="mini"
          ref="auditForm"
          :model="auditForm"
          :rules="rules"
          label-width="180px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="审核备注" prop="approval_view">
                <el-input
                  :disabled="isDisabled"
                  type="textarea"
                  v-model="auditForm.approval_view"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核状态" prop="status">
                <el-radio-group
                  :disabled="isDisabled"
                  v-model="auditForm.status"
                >
                  <el-radio :label="2">审核通过</el-radio>
                  <el-radio :label="3">审核退回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
        <!-- <el-button type="primary" :disabled="isDisabled" @click="handleSubmit"
          >提交审核</el-button
        > -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBidProjectList,
  getBidProjectSegments,
  generateAnnouncement,
} from "@/api/bid-manage/bid-project";
import { getGeoAreas } from "@/api/bid-manage/project-info";
import { getExpertTypes } from "@/api/expert/expert";
import { extractExpertList, getCity } from "@/api/bid-manage/extract";
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import Tinymce from "@/components/Tinymce";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          val.expert_selection_conditions?.forEach((item) => {
            console.log(item);

            item.experts = item.expert_selection_logs.filter((v) => {
              return (v.expert_selection_condition_id = item.id);
            });
            item.expertList = item.experts
              ?.map((j) => {
                return j.expert_name;
              })
              .join(",");
          });
          this.form = { ...val };
          this.form.bidding_project_name =
            val.project_detail[0].bidding_project_name;
          this.form.bidding_project_code =
            val.project_detail[0].bidding_project_code;
          this.queryFormSegment.bidding_project_id =
            this.form.bidding_project_id;
          if (val.submission_status !== 1) {
            this.isDisabled = true;
          }
        }
      },
      deep: true,
    },
    // innerVisible: {
    //   handler(val) {
    //     if (val) {
    //       this.handleSearch();
    //     }
    //   },
    // },
    // selectSegmentShow: {
    //   handler(val) {
    //     if (val) {
    //       this.handleSearchSegment();
    //     }
    //   },
    // },
  },
  created() {},
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      auditForm: {},
      isDisabled: false,
      addressProps: {
        label: "name",
        value: "id",
      },
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        bidding_project_name: null,
        bidding_project_code: null,
        opening_date: null,
        opening_location: null,
        evaluation_date: null,
        evaluation_location: null,
        notes: null,
        owner_unit: null,
        owner_representative: null,
        owner_representative_phone: null,
        tender_agency: null,
        agency_representative: null,
        agency_representative_phone: null,
        supervision_unit: null,
        supervisor: null,
        supervisor_phone: null,
        tender_content: null,
        project_location: null,
        extraction_terminal: null,
        investment_amount: null,
        intended_selection_date: null,
        committee_member_count: null,
        segments: [],
        expert_selection_conditions: [],
        submission_status: 0,
      },
      innerVisible: false,
      selectSegmentShow: false,
      expertVisible: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      expertIndex: 0,
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      tableDataExpert: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        title: [
          {
            required: true,
            message: "请输入招标公告",
            trigger: ["blur", "change"],
          },
        ],
        joint_bidding: [
          {
            required: true,
            message: "请输入联合体投标",
            trigger: ["blur", "change"],
          },
        ],
        announcement_start_time: [
          {
            required: true,
            message: "请选择公告发布时间",
            trigger: ["blur", "change"],
          },
        ],
        announcement_end_time: [
          {
            required: true,
            message: "请选择公告截止时间",
            trigger: ["blur", "change"],
          },
        ],
        file_obtain_start_time: [
          {
            required: true,
            message: "请选择招标文件获取时间",
            trigger: ["blur", "change"],
          },
        ],
        file_obtain_end_time: [
          {
            required: true,
            message: "请选择招标文件获取截止时间",
            trigger: ["blur", "change"],
          },
        ],
        submission_deadline: [
          {
            required: true,
            message: "请选择投标文件递交截止时间",
            trigger: ["blur", "change"],
          },
        ],
        announcement_category: [
          {
            required: true,
            message: "请选择公告发布类别",
            trigger: ["blur", "change"],
          },
        ],
        file_obtain_method: [
          {
            required: true,
            message: "请输入招标文件获取方法",
            trigger: ["blur", "change"],
          },
        ],
        submission_method: [
          {
            required: true,
            message: "请输入投标文件递交方法",
            trigger: ["blur", "change"],
          },
        ],
        opening_method: [
          {
            required: true,
            message: "请输入开标方式",
            trigger: ["blur", "change"],
          },
        ],
      },
      expertProps: {
        label: "name",
        value: "id",
      },
      selectProject: [],
      selectExpert: [],
      tableDataSegment: [],
      cityOptions: [],
      expertOptions: [],
      addressOptions: [],
      totalSegment: 0,
    };
  },
  created() {
    this.getGeoExpert();
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSelectionChange() {},
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.handleGetProjectList();
    },
    handleSearchSegment(filters) {
      Object.assign(this.queryFormSegment, filters);
      this.handleGetSegmentList();
    },
    handleGetSegmentList() {
      getBidProjectSegments(this.queryFormSegment).then((res) => {
        this.tableDataSegment = res.data.data;
        this.totalSegment = res.data.total;
      });
    },
    getGeoExpert() {
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      getExpertTypes().then((res) => {
        this.expertOptions = res.data;
      });
      getCity().then((res) => {
        this.cityOptions = res.data;
      });
    },
    handleGetProjectList() {
      getBidProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleGetExpert(index, row) {
      this.expertIndex = index;
      let params = {
        geo_area_id: row.geo_area_id,
        expert_type: row.expert_type,
        expert_count: row.expert_count,
      };
      extractExpertList(params).then((res) => {
        this.tableDataExpert = res.data.expert.data;
        console.log(this.tableDataExpert, "this.tableDataExpert");
        this.expertVisible = true;
      });
    },
    handleSelectExpert(data) {
      this.selectExpert = data;
      console.log(data);
    },
    handleSaveExpert() {
      let name = this.selectExpert.map((item) => {
        return item.name;
      });
      this.form.expert_selection_conditions[this.expertIndex].expertList =
        name.join(",");
      this.form.expert_selection_conditions[this.expertIndex].experts =
        this.selectExpert;
      this.expertVisible = false;
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetProjectList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.handleGetProjectList();
    },

    handleChangeSizeSegment(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetSegmentList();
    },
    handleChangePageSegment(page) {
      this.queryForm.page = page;
      this.handleGetSegmentList();
    },

    handleSelectProject(data) {
      this.selectProject = data;
      console.log(data);
    },
    handleSelectSegment(data) {
      if (data.length > 1) {
        this.$message.warning("只能选择一个标段");
        return;
      }
      this.form.segments = data;
    },
    handleSelectionChange1(data) {
      this.selectedRows1 = data;
    },
    handleSaveProject() {
      if (this.selectProject.length != 1) {
        this.$message.warning("只能选择一个项目");
        return;
      }
      this.form.bidding_project_name =
        this.selectProject[0].bidding_project_name;
      this.form.bidding_project_code =
        this.selectProject[0].bidding_project_code;
      this.form.bidding_project_id = this.selectProject[0].id;
      this.queryFormSegment.bidding_project_id = this.selectProject[0].id;
      this.innerVisible = false;
      // this.handleCreate();
    },
    handleCreate() {
      let params = { ...this.form };
      params.bidding_project = this.selectProject[0];
      delete params.details;
      generateAnnouncement(params).then((res) => {
        console.log(res);
      });
      let name = "";
      let num = "";
      console.log(this.form);

      if (this.form.bidding_project_name == null) {
        name = "【项目名称】";
        num = "(招标编号:&nbsp;)";
      } else {
        name = `${this.form.bidding_project_name}`;
        num = `招标编号:${this.form.bidding_project_code}`;
      }
      let wordText = text;
      this.form.details = wordText
        .replace("【项目名称】", `${name}`)
        .replace("(招标编号:&nbsp;)", `${num}`);
    },
    handleSaveSegment() {
      this.form.segments;
      this.selectSegmentShow = false;
    },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleCloseExpert() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.expertVisible = false;
          //   done();
        })
        .catch((_) => {});
    },
    handleCloseSegment() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.selectSegmentShow = false;
          //   done();
        })
        .catch((_) => {});
    },
    addPerson() {
      this.form.expert_selection_conditions.push({
        geo_area_id: null,
        expert_type: null,
        expert_count: null,
        report_time: null,
        report_location: null,
        evaluation_duration: null,
        expertList: null,
        experts: [],
      });
    },

    handleDeleteTeam(index, row) {
      this.form.expert_selection_conditions.splice(index, 1);
    },
    handleBatchDelete1() {
      this.selectedRows1.forEach((item) => {
        let index = this.form.expert_selection_conditions.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.expert_selection_conditions.splice(index, 1);
        }
      });
    },
    addBidSegment() {
      console.log(this.form.bidding_project_name);

      if (
        this.form.bidding_project_name == "" ||
        this.form.bidding_project_name == null
      ) {
        this.$message.warning("请先选择招标项目");
        return;
      }
      this.selectSegmentShow = true;
    },

    handleSave() {
      console.log("save", this.form);
      // this.form.bid_segment_ids = [10];
      this.$emit("save", this.form);
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              console.log(this.form);
              this.auditForm.project_expert_extraction_id = this.form.id;
              this.auditForm.company_id =
                this.form.bid_segments[0].company_info_id;
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachment.splice(index, 1);
      }
    },
    handleShowProject() {
      this.innerVisible = true;
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
