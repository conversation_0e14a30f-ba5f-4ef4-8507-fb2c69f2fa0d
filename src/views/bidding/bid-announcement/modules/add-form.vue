<template>
  <div>
    <el-dialog
      title="招标公告表单"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-dialog
        v-if="this.innerVisible"
        width="80%"
        title="选择招标项目"
        :visible.sync="innerVisible"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fields"
            :itemsPerRow="2"
            @search="handleSearch"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableData"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectProject"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="bidding_project_code"
              label="招标/采购项目编号"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="bidding_project_name"
              label="招标/采购项目名称"
            ></el-table-column>
            <el-table-column
              prop="project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleSaveProject">确定</el-button>
        </span>
      </el-dialog>
      <el-dialog
        v-if="this.selectSegmentShow"
        width="80%"
        title="选择标段"
        :visible.sync="selectSegmentShow"
        append-to-body
      >
        <el-card>
          <search-form
            :fields="fieldsSegment"
            :itemsPerRow="2"
            @search="handleSearchSegment"
          ></search-form>
        </el-card>
        <el-card style="margin-top: 10px">
          <el-table
            :data="tableDataSegment"
            border
            style="margin-top: 10px"
            @selection-change="handleSelectSegment"
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>

            <el-table-column
              prop="segment_number"
              label="标段编号"
            ></el-table-column>
            <el-table-column
              prop="segment_name"
              label="标段名称"
            ></el-table-column>
            <el-table-column
              prop="bidding_project.bidding_project_name"
              label="项目名称"
            ></el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryFormSegment.per_page"
              :current-page="queryFormSegment.page"
              @size-change="handleChangeSizeSegment"
              @current-change="handleChangePageSegment"
            >
            </el-pagination>
          </div>
        </el-card>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleCloseSegment">取 消</el-button>
          <el-button type="primary" @click="handleSaveSegment">确定</el-button>
        </span>
      </el-dialog>
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      v-model="form.bidding_project_name"
                      disabled
                    ></el-input>
                    <el-button size="mini" @click="handleShowProject"
                      >选择</el-button
                    >
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="标段" name="2">
          <div>
            <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addBidSegment"
                >选择标段</el-button
              >
            </div>
            <el-table
              :data="form.bid_segments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" prop="segment_number">
              </el-table-column>
              <el-table-column label="标段名称" prop="segment_name">
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="时间设置" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标公告:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="联合体投标:" prop="joint_bidding">
                  <el-select v-model="form.joint_bidding" placeholder="请选择">
                    <el-option key="0" label="接受" value="0"> </el-option>
                    <el-option key="1" label="不接受" value="1">
                    </el-option> </el-select></el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="公告发布时间:"
                  prop="announcement_start_time"
                >
                  <el-date-picker
                    v-model="form.announcement_start_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="公告截至时间:"
                  prop="announcement_end_time"
                >
                  <el-date-picker
                    v-model="form.announcement_end_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取时间:"
                  prop="file_obtain_start_time"
                >
                  <el-date-picker
                    v-model="form.file_obtain_start_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取截止时间:"
                  prop="file_obtain_end_time"
                >
                  <el-date-picker
                    v-model="form.file_obtain_end_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <!-- <el-col :span="12">
                <el-form-item label="保证金截止时间:" prop="bidder">
                  <el-date-picker
                    v-model="form.announcement_start_time"
                    type="datetime"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col> -->
              <el-col :span="12">
                <el-form-item
                  label="投标文件递交截止时间:"
                  prop="submission_deadline"
                >
                  <el-date-picker
                    v-model="form.submission_deadline"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="详细内容" name="4">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="公告发布类别:"
                  prop="announcement_category"
                >
                  <el-select
                    v-model="form.announcement_category"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="工程" value="0"> </el-option>
                    <el-option key="1" label="货物" value="1"> </el-option>
                    <el-option key="2" label="服务" value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资审方式:" prop="qualification_review">
                  <el-select
                    disabled
                    v-model="form.qualification_review"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="资格后审" value="0"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="招标文件获取方法:" prop="file_obtain_method">
                <el-input
                  type="textarea"
                  v-model="form.file_obtain_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="投标文件递交方法:" prop="submission_method">
                <el-input
                  type="textarea"
                  v-model="form.submission_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标方式:" prop="opening_method">
                <el-input
                  type="textarea"
                  v-model="form.opening_method"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="附件" name="5">
          <el-upload
            ref="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="公告详情" name="6">
          <el-button type="primary" @click="handleCreate">重新生成</el-button>
          <Tinymce ref="editor" v-model="form.details" :height="400" />
        </el-collapse-item>
        <!-- <el-collapse-item title="是否业主审核" name="7">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-form-item label="业主审核:" prop="task_plan">
                <el-select
                  v-model="form.qualification_review"
                  placeholder="请选择"
                >
                  <el-option key="0" label="否" value="0"> </el-option>
                  <el-option key="1" label="是" value="1"> </el-option>
                </el-select> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item> -->
        <el-collapse-item title="审核记录" name="7">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">{{
          form.status !== 2 ? "取 消" : "关 闭"
        }}</el-button>
        <el-button v-if="form.status !== 2" type="primary" @click="handleSave"
          >保存</el-button
        >
        <el-button v-if="form.status !== 2" type="primary" @click="handleSubmit"
          >提交内部审核</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBidProjectList,
  getBidProjectSegments,
  generateAnnouncement,
} from "@/api/bid-manage/bid-project";
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import Tinymce from "@/components/Tinymce";
import { text } from "./text";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          this.form.bidding_project_name =
            val.bidding_project.bidding_project_name;
          this.form.bidding_project_code =
            val.bidding_project.bidding_project_code;
          this.form.qualification_review = "0";
          this.queryFormSegment.bidding_project_id =
            this.form.bidding_project_id;
        }
      },
      deep: true,
    },
    innerVisible: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
    selectSegmentShow: {
      handler(val) {
        if (val) {
          this.handleSearchSegment();
        }
      },
    },
  },
  created() {},
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        bidding_project_name: null,
        bidding_project_code: null,
        bidding_project_id: null,
        title: null,
        joint_bidding: null,
        announcement_start_time: null,
        announcement_end_time: null,
        file_obtain_start_time: null,
        file_obtain_end_time: null,
        submission_deadline: null,
        announcement_category: null,
        qualification_review: "0",
        file_obtain_method: null,
        submission_method: null,
        opening_method: null,
        bid_segments: [],
        details: text,
        attachment: [],
        status: 0,
      },
      innerVisible: false,
      selectSegmentShow: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        title: [
          {
            required: true,
            message: "请输入招标公告",
            trigger: ["blur", "change"],
          },
        ],
        joint_bidding: [
          {
            required: true,
            message: "请输入联合体投标",
            trigger: ["blur", "change"],
          },
        ],
        announcement_start_time: [
          {
            required: true,
            message: "请选择公告发布时间",
            trigger: ["blur", "change"],
          },
        ],
        announcement_end_time: [
          {
            required: true,
            message: "请选择公告截止时间",
            trigger: ["blur", "change"],
          },
        ],
        file_obtain_start_time: [
          {
            required: true,
            message: "请选择招标文件获取时间",
            trigger: ["blur", "change"],
          },
        ],
        file_obtain_end_time: [
          {
            required: true,
            message: "请选择招标文件获取截止时间",
            trigger: ["blur", "change"],
          },
        ],
        submission_deadline: [
          {
            required: true,
            message: "请选择投标文件递交截止时间",
            trigger: ["blur", "change"],
          },
        ],
        announcement_category: [
          {
            required: true,
            message: "请选择公告发布类别",
            trigger: ["blur", "change"],
          },
        ],
        file_obtain_method: [
          {
            required: true,
            message: "请输入招标文件获取方法",
            trigger: ["blur", "change"],
          },
        ],
        submission_method: [
          {
            required: true,
            message: "请输入投标文件递交方法",
            trigger: ["blur", "change"],
          },
        ],
        opening_method: [
          {
            required: true,
            message: "请输入开标方式",
            trigger: ["blur", "change"],
          },
        ],
      },
      selectProject: [],
      tableDataSegment: [],
      totalSegment: 0,
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSelectionChange() {},
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.handleGetProjectList();
    },
    handleSearchSegment(filters) {
      Object.assign(this.queryFormSegment, filters);
      this.handleGetSegmentList();
    },
    handleGetSegmentList() {
      getBidProjectSegments(this.queryFormSegment).then((res) => {
        this.tableDataSegment = res.data.data;
        this.totalSegment = res.data.total;
      });
    },
    handleGetProjectList() {
      getBidProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetProjectList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.handleGetProjectList();
    },
    handleSelectProject(data) {
      this.selectProject = data;
      console.log(data);
    },
    handleSelectSegment(data) {
      if (data.length > 1) {
        this.$message.warning("只能选择一个标段");
        return;
      }
      this.form.bid_segments = data;
    },
    handleSaveProject() {
      if (this.selectProject.length > 1) {
        this.$message.warning("只能选择一个项目");
        return;
      }
      this.form.bidding_project_name =
        this.selectProject[0].bidding_project_name;
      this.form.bidding_project_code =
        this.selectProject[0].bidding_project_code;
      this.form.bidding_project_id = this.selectProject[0].id;
      this.queryFormSegment.bidding_project_id = this.selectProject[0].id;
      this.innerVisible = false;
      // this.handleCreate();
    },
    handleCreate() {
      let params = { ...this.form };
      params.bidding_project = this.selectProject[0];
      delete params.details;

      generateAnnouncement(params).then((res) => {
        this.$refs.editor.setContent(res.data);
      });
      // let name = "";
      // let num = "";
      // console.log(this.form);

      // if (this.form.bidding_project_name == null) {
      //   name = "【项目名称】";
      //   num = "(招标编号:&nbsp;)";
      // } else {
      //   name = `${this.form.bidding_project_name}`;
      //   num = `招标编号:${this.form.bidding_project_code}`;
      // }
      // let wordText = text;
      // this.form.details = wordText
      //   .replace("【项目名称】", `${name}`)
      //   .replace("(招标编号:&nbsp;)", `${num}`);
    },
    handleSaveSegment() {
      this.selectSegmentShow = false;
    },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleCloseSegment() {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.selectSegmentShow = false;
          //   done();
        })
        .catch((_) => {});
    },
    addPerson() {
      this.form.project_team_assignment.push({
        name: null,
        phone: null,
        gender: null,
        position: null,
        title_id: null,
        title_level: null,
        division_id: null,
        performance_requirements: null,
      });
    },
    addBidSegment() {
      console.log(this.form.bidding_project_name);

      if (
        this.form.bidding_project_name == "" ||
        this.form.bidding_project_name == null
      ) {
        this.$message.warning("请先选择招标项目");
        return;
      }
      this.selectSegmentShow = true;
    },

    handleSave() {
      console.log("save", this.form);
      this.form.bid_segment_ids = [this.form.bid_segments[0].id];
      this.$emit("save", this.form);
    },
    handleSubmit() {
      console.log("submit", this.form);

      this.$emit("submit", this.form);
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachment.splice(index, 1);
      }
    },
    handleShowProject() {
      this.innerVisible = true;
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
