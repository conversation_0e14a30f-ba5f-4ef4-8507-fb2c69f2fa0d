<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 11:23:44
 * @FilePath: /bid/src/views/bidding/bid-announcement/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleAdd">新建</el-button>
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>

        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project.bidding_project_name"
          label="招标/采购项目名称"
        ></el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project.bidding_project_code"
          label="招标/采购项目编号"
        ></el-table-column>

        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="title"
          label="公告名称"
        ></el-table-column>
        <el-table-column prop="announcement_category" label="公告类型">
          <template slot-scope="scope">
            {{ setTypeText(scope.row.announcement_category) }}
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          prop="announcement_start_time"
          label="公告发布日期"
        ></el-table-column>
        <el-table-column
          width="180"
          prop="announcement_end_time"
          label="公告截止日期"
        ></el-table-column>

        <el-table-column
          width="180"
          prop="file_obtain_start_time"
          label="标文件获取时间"
        ></el-table-column>
        <el-table-column
          width="180"
          prop="file_obtain_end_time"
          label="招标文件获取截止时间"
        ></el-table-column>
        <el-table-column
          width="180"
          prop="submission_deadline"
          label="投标文件递交截止时间"
        ></el-table-column>

        <el-table-column prop="qualification_review" label="资审方式">
          <template>
            {{ "资格后审" }}
          </template>
        </el-table-column>
        <el-table-column prop="status" width="150px" label="发布状态">
          <template slot-scope="scope">
            {{ setStatusText(scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="user"
          show-overflow-tooltip
          width="120"
          label="操作人"
        ></el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >

            <el-button
              size="mini"
              type="danger"
              :disabled="scope.row.status == 2 || scope.row.status == 1"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>

    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :fileList="fileList"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import {
  getBidAnnouncements,
  addBidAnnouncements,
  deleteBidAnnouncements,
  updateBidAnnouncements,
  getBidAnnouncementById,
} from "@/api/bid-manage/bid-project";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";

export default {
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "公告名称：",
          prop: "announcement_name",
          component: "el-input",
          props: {
            placeholder: "请输入公告名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      this.fileList = [];
      this.addShow = true;
    },
    handleUpdate(data) {
      this.addShow = true;
      getBidAnnouncementById(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        this.editForm.attachment.forEach((item) => {
          let obj = {
            name: item.name,
            url: item.path,
          };
          this.fileList.push(obj);
        });
      });
    },
    getList() {
      getBidAnnouncements(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        updateBidAnnouncements(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidAnnouncements(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 4;
      if (data.id) {
        updateBidAnnouncements(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidAnnouncements(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteBidAnnouncements(row.id).then((res) => {
            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待平台审核";
          break;
        case 2:
          text = "平台审核通过";
          break;
        case 3:
          text = "平台审核退回";
          break;
        case 4:
          text = "待内部审核";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    setTypeText(status) {
      let text = "";
      switch (status) {
        case "0":
          text = "工程";
          break;
        case "1":
          text = "货物";
          break;
        case "2":
          text = "服务";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
