<template>
  <div>
    <el-dialog
      title="项目信息表单"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="基本信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目编号:" prop="project_code">
                  <el-input
                    v-model="form.project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="项目名称:" prop="project_name">
                  <el-input
                    v-model="form.project_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="是否依法招标:" prop="legally_bidding">
                  <el-radio-group v-model="form.legally_bidding">
                    <el-radio :label="1">依法招标</el-radio>
                    <el-radio :label="2">非依法招标</el-radio>
                  </el-radio-group>
                </el-form-item></el-col
              >
              <el-col :span="12">
                <el-form-item label="进场交易:" prop="entering_transaction">
                  <el-radio-group v-model="form.entering_transaction">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item></el-col
              >
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目地址:" prop="project_address">
                  <el-cascader
                    v-model="form.project_address"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="项目行业分类:"
                  prop="project_industry_classification"
                >
                  <el-cascader
                    v-model="form.project_industry_classification"
                    :props="industryProps"
                    :options="industryOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item label="详细地址:" prop="detailed_address">
                <el-input
                  type="textarea"
                  v-model="form.detailed_address"
                ></el-input> </el-form-item
            ></el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="项目法人:" prop="project_legal_person">
                  <el-input
                    v-model="form.project_legal_person"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="出资比例(%):" prop="investment_ratio">
                  <el-input-number
                    v-model="form.investment_ratio"
                    :precision="2"
                    :step="0.1"
                    :max="100"
                  ></el-input-number> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="投资金额(元):" prop="investment_amount">
                  <el-input
                    v-model="form.investment_amount"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item label="资金来源:" prop="source_of_funds">
                <el-input
                  type="textarea"
                  v-model="form.source_of_funds"
                ></el-input>
              </el-form-item>
            </el-row>
            <el-row>
              <el-form-item label="项目规模:" prop="project_scale">
                <el-input
                  type="textarea"
                  v-model="form.project_scale"
                ></el-input>
              </el-form-item>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目分类:" prop="project_cate">
                  <el-select
                    v-model="form.project_cate"
                    placeholder="请选择"
                    @change="handleProjectCateChange"
                  >
                    <el-option key="1" label="工程" :value="1"> </el-option>
                    <el-option key="2" label="采购" :value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.project_cate === 1">
                <el-form-item label="批文编号:" prop="approval_document_number">
                  <el-input
                    v-model="form.approval_document_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row v-if="form.project_cate === 1">
              <el-col :span="12">
                <el-form-item label="批文名称:" prop="approval_document_name">
                  <el-input
                    v-model="form.approval_document_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="批准单位:" prop="approval_authority">
                  <el-input
                    v-model="form.approval_authority"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row v-if="form.project_cate === 1">
              <el-col :span="12">
                <el-form-item label="立项时间:" prop="project_initiation_date">
                  <el-date-picker
                    v-model="form.project_initiation_date"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标方案核准号:"
                  prop="bidding_plan_approval_number"
                >
                  <el-input
                    v-model="form.bidding_plan_approval_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="项目负责人" name="2">
          <div>
            <div style="text-align: right">
              <el-button size="mini" type="primary" @click="addPerson"
                >添加</el-button
              >
              <el-button size="mini" type="danger" @click="handleBatchDelete()"
                >删除</el-button
              >
            </div>
            <el-table
              :data="form.project_managers"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="项目负责人" width="180">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.name" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="联系电话" width="180">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.phone" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column label="备注">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.remark" size="small"></el-input>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                fixed="right"
                align="center"
                width="120"
              >
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="danger"
                    @click="handleDelete(scope.$index, scope.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="核准扫描件" name="3">
          <el-upload
            ref="upload1"
            key="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList1"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
            :on-preview="handlePreview"
          >
            <el-button key="btn2" slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
        <el-collapse-item
          title="批文扫描件"
          name="4"
          v-if="form.project_cate === 1"
        >
          <el-upload
            ref="upload2"
            key="upload2"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :file-list="fileList2"
            :before-upload="beforeUpload"
            :on-remove="handleRemove2"
            :on-success="handleSuccess2"
            :on-preview="handlePreview"
          >
            <el-button key="btn2" sot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <!-- <el-button type="primary" @click="handleSave">保存</el-button> -->
        <el-button type="primary" @click="handleSubmit">递交生效</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "ProjectForm",
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    addressOptions: {
      type: Array,
    }, // 这里应该是地址的级联选项，需要根据实际情况填充
    industryOptions: {
      type: Array,
      default: () => [],
    }, // 这里应该是行业分类的级联选项，需要根据实际情况填充
    editForm: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    editForm: {
      handler(val) {
        console.log(val);
        if (val) {
          this.form = { ...val };
          this.fileList1 = [];
          this.fileList2 = [];
          this.form.approval_document_scan.forEach((item) => {
            console.log(item);
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList1.push(obj);
            console.log(this.form.approval_document_scan);
          });
          this.form.approval_number_scan.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList2.push(obj);
            console.log(this.form.approval_number_scan);
          });
        } else {
          this.fileList1 = [];
          this.fileList2 = [];
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      url: "",
      collapse: ["1", "2", "3", "4"],
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      form: {
        project_code: "",
        project_name: "",
        legally_bidding: "",
        entering_transaction: true,
        project_address: [],
        project_industry_classification: [],
        detailed_address: "",
        project_legal_person: "",
        investment_ratio: "",
        investment_amount: "",
        source_of_funds: "",
        project_scale: "",
        approval_document_number: "",
        approval_document_name: "",
        approval_authority: "",
        project_initiation_date: "",
        bidding_plan_approval_number: "",
        project_managers: [],
        approval_document_scan: [],
        approval_number_scan: [],
        project_cate: 1,
      },
      fileList2: [],
      fileList1: [],
      header: {
        Authorization: "Bearer " + getToken(),
      },
      addressProps: {
        label: "name",
        value: "id",
      },
      industryProps: {
        label: "name",
        value: "id",
      },
      selectedRows: [],
      loading: false,
      rules: {
        project_name: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        project_address: [
          { required: true, message: "请选择项目地址", trigger: "change" },
        ],
        project_industry_classification: [
          { required: true, message: "请选择项目行业分类", trigger: "change" },
        ],
        detailed_address: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        project_legal_person: [
          { required: true, message: "请输入项目法人", trigger: "blur" },
        ],
        // investment_ratio: [
        //   { required: true, message: "请输入出资比例", trigger: "blur" },
        // ],
        investment_amount: [
          { required: true, message: "请输入投资金额", trigger: "blur" },
        ],
        source_of_funds: [
          { required: true, message: "请输入资金来源", trigger: "blur" },
        ],
        project_scale: [
          { required: true, message: "请输入项目规模", trigger: "blur" },
        ],
        approval_document_number: [
          { required: true, message: "请输入批文编号", trigger: "blur" },
        ],
        approval_document_name: [
          { required: true, message: "请输入批文名称", trigger: "blur" },
        ],
        approval_authority: [
          { required: true, message: "请输入批准单位", trigger: "blur" },
        ],
        project_initiation_date: [
          { required: true, message: "请选择立项时间", trigger: "change" },
        ],
        bidding_plan_approval_number: [
          { required: true, message: "请输入招标方案核准号", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handlePreview(file, fileList) {
      console.log(file, fileList);
      let urlTemp = "";
      if (!file.url) {
        urlTemp = file.response.data.path;
      } else {
        urlTemp = file.url;
      }
      let url = `https://bidding.senmoio.cn/${urlTemp}`;
      window.open(url, "_blank");
    },
    handleSelectionChange(data) {
      console.log(data);
      this.selectedRows = data;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    addPerson() {
      this.form.project_managers.push({
        name: "",
        phone: "",
        remark: "",
        index: this.generateUniqueId(),
      });
    },
    generateUniqueId() {
      return Date.now().toString(36);
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.form.project_managers.splice(index, 1);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleBatchDelete() {
      this.selectedRows.forEach((item) => {
        let index = this.form.project_managers.filter((data) => {
          return (item.index = data.index);
        });
        if (index != -1) {
          this.form.project_managers.splice(index, 1);
        }
      });
      this.selectedRows.forEach;
    },
    handleSave() {
      console.log("save", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.project_managers.length == 0) {
            this.$message.warning("请添加项目负责人");
            return;
          }
          this.$emit("submit", this.form);
        }
      });
    },
    handleFileChange1(file, fileList) {
      console.log(this.form.approval_document_scan);
      console.log(file, fileList);
    },
    handleFileChange2(file, fileList) {
      console.log(this.form.approval_number_scan);
      console.log(file, fileList);
    },
    handleSuccess1(res, file, fileList) {
      this.form.approval_document_scan.push(res.data);
    },
    handleSuccess2(res, file, fileList) {
      this.form.approval_number_scan.push(res.data);
    },
    handleRemove1(file, fileList) {
      let index = this.form.approval_document_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.approval_document_scan.splice(index, 1);
      }
    },
    handleRemove2(file, fileList) {
      let index = this.form.approval_number_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.approval_number_scan.splice(index, 1);
      }
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleProjectCateChange(val) {
      if (val === 2) {
        // 采购类型，移除必填校验
        this.rules.approval_document_number = [];
        this.rules.approval_document_name = [];
        this.rules.approval_authority = [];
        this.rules.project_initiation_date = [];
        this.rules.bidding_plan_approval_number = [];
      } else {
        // 工程类型，添加必填校验
        this.rules.approval_document_number = [
          { required: true, message: "请输入批文编号", trigger: "blur" },
        ];
        this.rules.approval_document_name = [
          { required: true, message: "请输入批文名称", trigger: "blur" },
        ];
        this.rules.approval_authority = [
          { required: true, message: "请输入批准单位", trigger: "blur" },
        ];
        this.rules.project_initiation_date = [
          { required: true, message: "请选择立项时间", trigger: "change" },
        ];
        this.rules.bidding_plan_approval_number = [
          { required: true, message: "请输入招标方案核准号", trigger: "blur" },
        ];
      }
      // 重新进行表单校验
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
  },
};
</script>

<style scoped>
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
/* 这里可以添加一些CSS样式来美化表单 */
</style>
