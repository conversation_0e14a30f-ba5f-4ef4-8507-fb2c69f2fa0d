<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 12:35:13
 * @FilePath: /bid/src/views/bidding/project-info/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleAdd">新建</el-button>
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>

        <el-table-column prop="project_code" label="项目编号"></el-table-column>
        <el-table-column
          prop="project_name"
          show-overflow-tooltip
          label="项目名称"
        ></el-table-column>
        <el-table-column
          prop="project_legal_person"
          label="项目法人"
        ></el-table-column>
        <el-table-column
          prop="investment_amount"
          label="投资金额（元）"
        ></el-table-column>
        <el-table-column
          prop="project_initiation_date"
          label="立项时间"
        ></el-table-column>

        <el-table-column label="操作" fixed="right" align="center" width="280">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >

            <!-- <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            > -->

            <el-button
              size="mini"
              type="primary"
              @click="handleShowBid(scope.$index, scope.row)"
              >查看招标项目</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <project-form
      v-if="addShow"
      :visible="addShow"
      :addressOptions="addressOptions"
      :industryOptions="industryOptions"
      :editForm="editForm"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></project-form>
  </div>
</template>

<script>
import {
  getProjectList,
  addProject,
  deleteProject,
  updateProject,
  getIndustries,
  getGeoAreas,
  getProjectById,
} from "@/api/bid-manage/project-info";
import SearchForm from "@/components/SearchForm/index.vue";
import ProjectForm from "./modules/project-form.vue";

export default {
  components: {
    SearchForm,
    ProjectForm,
  },
  data() {
    return {
      addShow: false,
      editForm: {},
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
        // {
        //   label: "项目法人：",
        //   prop: "keyword",
        //   component: "el-input",
        //   props: {
        //     placeholder: "请输入项目法人：",
        //   },
        // },
        // {
        //   label: "立项时间：",
        //   prop: "dateRange3",
        //   component: "el-date-picker",
        //   props: {
        //     type: "daterange",
        //     rangeSeparator: "至",
        //     startPlaceholder: "开始日期",
        //     endPlaceholder: "结束日期",
        //   },
        // },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
      industryOptions: [],
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      // getIndustries().then((res) => {
      //   this.industryOptions = res.data;
      // });
      // getGeoAreas().then((res) => {
      //   this.addressOptions = res.data;
      // });
      this.addShow = true;
    },
    handleUpdate(data) {
      getProjectById(data.id).then((res) => {
        this.editForm = res.data;
        console.log(res);
      });
      this.addShow = true;
    },
    getList() {
      getProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        updateProject(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addProject(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      this.$confirm("确认要提交吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          let formData = { ...data };
          formData.status = 1;
          if (data.id) {
            updateProject(data.id, formData).then((res) => {
              this.message(res.status_code, res.message);
              console.log(res);
            });
          } else {
            addProject(formData).then((res) => {
              this.message(res.status_code, res.message);
              console.log(res);
            });
          }
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteProject(row.id).then((res) => {
            console.log(res);

            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleShowBid(index, data) {
      this.$router.push({ path: "/bidding/bid-project" });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.getList();
        this.addShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
    getIndustries().then((res) => {
      this.industryOptions = res.data;
    });
    getGeoAreas().then((res) => {
      this.addressOptions = res.data;
    });
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
