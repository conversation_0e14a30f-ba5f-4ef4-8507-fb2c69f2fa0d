<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: 王子超
 * @LastEditTime: 2024-12-03 22:40:32
 * @FilePath: /bidding-web/src/views/expert-project/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="bidding_project.bidding_project_name"
          label="项目名称"
        >
        </el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project.bidding_project_code"
          label="项目编号"
        ></el-table-column>

        <el-table-column
          prop="segment_name"
          show-overflow-tooltip
          width="180"
          label="标段名称"
        >
          <template slot-scope="scope">
            {{
              scope.row.bid_segments.length > 0
                ? scope.row.bid_segments[0].segment_name
                : ""
            }}
          </template>
        </el-table-column>

        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="opening_date"
          label="开标时间"
        ></el-table-column>

        <el-table-column
          show-overflow-tooltip
          prop="evaluation_date"
          label="评标时间"
        ></el-table-column>

        <el-table-column
          show-overflow-tooltip
          prop="tender_agency"
          label="招标代表机构"
        ></el-table-column>

        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="investment_amount"
          label="标段投资额"
        ></el-table-column>

        <el-table-column width="100" prop="status" label="状态">
          <template slot-scope="scope">
            <span>{{
              setStatusText(
                scope.row.bid_segments.length > 0
                  ? scope.row.bid_segments[0].flow_status
                  : ""
              )
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="220">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="handleViewHall(scope.row)"
              >进入评标大厅</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <bid-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :addressOptions="addressOptions"
      :industryOptions="industryOptions"
      :fileList="fileList"
      @close="addShow = false"
    ></bid-form>
    <view-form
      v-if="viewShow"
      :visible="viewShow"
      :editForm="viewForm"
      :addressOptions="addressOptions"
      :fileList="viewFileList"
      :industryOptions="industryOptions"
      @close="viewShow = false"
    ></view-form>
  </div>
</template>

<script>
import { getProjectList } from "@/api/expert/index";
import { getRegistrationsDetail } from "@/api/bid-manage/invite_bid";
import { getGeoAreas, getIndustries } from "@/api/bid-manage/project-info";
import SearchForm from "@/components/SearchForm/index.vue";
import BidForm from "./modules/bid-form.vue";
import ViewForm from "./modules/view-form.vue";
export default {
  components: {
    SearchForm,
    BidForm,
    ViewForm,
  },
  data() {
    return {
      viewShow: false,
      addShow: false,
      total: 0,
      viewForm: {},
      editForm: {},
      fileList: [],
      viewFileList: [],
      applicaList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
        {
          label: "状态",
          prop: "flow_status",
          component: "el-select",
          props: {
            placeholder: "请选择类型",
          },
          options: [
            { label: "全部", value: null },
            { label: "发标", value: 1 },
            { label: "投标", value: 2 },
            { label: "评标", value: 3 },
            { label: "定标", value: 4 },
          ],
        },
      ],
      tableData: [], // 过滤后的数据
      industryOptions: [],
      addressOptions: [],
    };
  },
  methods: {
    handleViewHall() {
      this.$router.push({ path: "/hall/expert" });
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleUpdate(data) {
      this.viewShow = true;
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      getRegistrationsDetail(data.bid_segments[0].id).then((res) => {
        console.log(res.data);
        this.viewForm = { ...res.data[0].bidding_project };
        let documentForm = { ...res.data[0].bid_document[0] };
        Object.assign(this.viewForm, documentForm);
        this.viewForm.fileList = [];
        if (documentForm.attachments != null) {
          documentForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.viewForm.fileList.push(obj);
          });
        }
        console.log(this.viewForm);
      });
    },
    getList() {
      getProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteBidHalfAnnouncements(row.id).then((res) => {
            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "未开始";
          break;
        case 1:
          text = "发标";
          break;
        case 2:
          text = "投标";
          break;
        case 3:
          text = "开标";
          break;
        case 4:
          text = "评标";
          break;
        case 5:
          text = "定标";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    setTypeText(status) {
      let text = "";
      switch (status) {
        case 1:
          text = "招标公告";
          break;
        case 2:
          text = "变更公告";
          break;
        case 3:
          text = "招标候选人公示";
          break;
        case 4:
          text = "结果公告";
          break;
        case 5:
          text = "废标公告";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
    getIndustries().then((res) => {
      this.industryOptions = res.data;
    });
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
