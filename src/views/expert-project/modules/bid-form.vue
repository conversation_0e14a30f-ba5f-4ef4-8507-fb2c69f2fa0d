<template>
  <div>
    <el-dialog
      title="项目信息"
      :visible.sync="visible"
      width="90%"
      :before-close="handleClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="230px"
        status-icon
        size="mini"
      >
        <el-collapse v-model="collapse">
          <el-collapse-item title="基本信息" name="1">
            <el-row>
              <el-col :span="24">
                <el-form-item label="所属项目:" prop="project_name">
                  <div style="display: flex">
                    <el-input v-model="form.project_name" disabled></el-input>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="招标项目名称:" prop="bidding_project_name">
                  <el-input
                    v-model="form.bidding_project_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目类型:" prop="bidding_project_type">
                  <el-select
                    v-model="form.bidding_project_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in projectTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                    <!-- 其他选项可以在这里添加 -->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标项目行业分类:"
                  prop="bidding_project_industry_classification"
                >
                  <el-select
                    v-model="form.bidding_project_industry_classification"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in industryProjectOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标方式:" prop="bidding_method">
                  <el-select v-model="form.bidding_method" placeholder="请选择">
                    <el-option key="0" label="公开招标" value="0"> </el-option>
                    <el-option key="1" label="邀请招标" value="1"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资格审查:" prop="qualification_review">
                  <el-select
                    v-model="form.qualification_review"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="资格后审" value="0"> </el-option>
                    <!-- <el-option key="1" label="资格预审" value="1"> </el-option> -->
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联合体招标:" prop="joint_bidding">
                  <el-radio-group v-model="form.joint_bidding">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item></el-col
              >
              <el-col :span="12">
                <el-form-item
                  label="监督部门类型："
                  prop="supervisory_department_type"
                >
                  <el-select
                    v-model="form.supervisory_department_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in departmentTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="监督部门:" prop="supervisory_department">
                  <el-input
                    v-model="form.supervisory_department"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="审核部门:" prop="approval_department">
                  <el-input
                    v-model="form.approval_department"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="业主单位:" prop="owner_unit">
                  <el-input v-model="form.owner_unit"></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标项目地点:"
                  prop="bidding_project_location"
                >
                  <el-cascader
                    v-model="form.bidding_project_location"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标项目建立时间:"
                  prop="bidding_project_establishment_date"
                >
                  <el-date-picker
                    v-model="form.bidding_project_establishment_date"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标项目实施地点:"
                  prop="bidding_project_implementation_location"
                >
                  <el-cascader
                    v-model="form.bidding_project_implementation_location"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item
                label="项目资格概况:"
                prop="project_qualification_overview"
              >
                <el-input
                  type="textarea"
                  v-model="form.project_qualification_overview"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item
                label="招标内容与范围及招标方案说明:"
                prop="bidding_scope_and_plan_description"
              >
                <el-input
                  type="textarea"
                  v-model="form.bidding_scope_and_plan_description"
                ></el-input> </el-form-item
            ></el-row>
          </el-collapse-item>

          <el-collapse-item title="文件信息" name="2">
            <el-row>
              <el-col>
                <el-form-item label="标题:" prop="title">
                  <el-input
                    v-model="form.title"
                    :disabled="['detail', 'superDetail'].includes(type)"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取开始时间:"
                  prop="document_obtain_start"
                >
                  <el-date-picker
                    v-model="form.document_obtain_start"
                    type="datetime"
                    :disabled="['detail', 'superDetail'].includes(type)"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取截止时间:"
                  prop="document_obtain_deadline"
                >
                  <el-date-picker
                    v-model="form.document_obtain_deadline"
                    :disabled="['detail', 'superDetail'].includes(type)"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="投标文件递交截止时间:"
                  prop="document_submission_deadline"
                >
                  <el-date-picker
                    v-model="form.document_submission_deadline"
                    :disabled="['detail', 'superDetail'].includes(type)"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="投标有效期(天):"
                  prop="bid_validity_period"
                >
                  <el-input
                    v-model="form.bid_validity_period"
                    :disabled="['detail', 'superDetail'].includes(type)"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="是否需要清单评审:"
                  prop="requires_list_review"
                >
                  <el-radio-group
                    v-model="form.requires_list_review"
                    :disabled="['detail', 'superDetail'].includes(type)"
                  >
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
              <el-col :span="12">
                <el-form-item label="投标报价价款形式:" prop="bid_price_form">
                  <el-radio-group
                    v-model="form.bid_price_form"
                    :disabled="['detail', 'superDetail'].includes(type)"
                  >
                    <el-radio :label="1">金额</el-radio>
                    <el-radio :label="2">费率</el-radio>
                  </el-radio-group></el-form-item
                ></el-col
              >
            </el-row>
            <el-row>
              <el-form-item label="投标文件提交方法:" prop="submission_method">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type)"
                  v-model="form.submission_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标地点:" prop="bid_opening_location">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type)"
                  v-model="form.bid_opening_location"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标方式:" prop="bid_opening_method">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type)"
                  v-model="form.bid_opening_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="评标方法:" prop="evaluation_method">
                <el-input
                  type="textarea"
                  :disabled="['detail', 'superDetail'].includes(type)"
                  v-model="form.evaluation_method"
                ></el-input> </el-form-item
            ></el-row>
          </el-collapse-item>
          <el-collapse-item title="附件信息" name="3">
            <el-upload
              ref="upload1"
              action="#"
              :file-list="fileList"
              :on-preview="handlePreview"
              :before-remove="handleRemove"
            >
            </el-upload>
          </el-collapse-item>
        </el-collapse>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getProjectList } from "@/api/bid-manage/project-info";
import {
  getTitles,
  getDivisions,
  getBidderByName,
} from "@/api/bid-manage/bid-project";
import {
  projectTypeOptions,
  industryProjectOptions,
  departmentTypeOptions,
} from "@/utils/data";
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
export default {
  name: "BidForm",
  components: {
    SearchForm,
  },
  props: {
    editForm: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    addressOptions: {
      type: Array,
    },
    industryOptions: {
      type: Array,
      default: () => [],
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
        }
      },
      deep: true,
    },
    innerVisible: {
      handler(val) {
        if (val) {
          this.handleSearch();
        }
      },
    },
  },
  created() {
    getTitles().then((res) => {
      this.titlesOptions = res.data.data;
    });
    getDivisions().then((res) => {
      this.divisionsOptions = res.data.data;
    });
  },
  data() {
    return {
      fileList1: [],
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],

      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        agency_contract_scan: [],
        approval_department: null,
        bid_segments: [],
        bidding_agent_scope: null,
        bidding_method: null,
        bidding_project_code: null,
        bidding_project_establishment_date: null,
        bidding_project_implementation_location: [],
        bidding_project_industry_classification: null,
        bidding_project_location: [],
        bidding_project_name: null,
        bidding_project_type: null,
        investment_amount: null,
        joint_bidding: null,
        owner_unit: null,
        project_code: null,
        project_industry_classification: [],
        project_initiation_date: null,
        project_legal_person: null,
        project_name: null,
        project_qualification_overview: null,
        project_team_assignments: [],
        qualification_review: "0",
        status: null,
        supervisory_department: null,
        supervisory_department_type: null,
        organizational_form: "委托招标",
        bidding_agent: "招标公司",
        bidder: null,
        bidding_agent_scope: null,
        bidding_agent_authority: null,
        task_plan: null,
      },
      titlesOptions: [],
      divisionsOptions: [],
      innerVisible: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      logData: [],
      totalLog: 0,
      queryFormLog: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      projectTypeOptions: projectTypeOptions,
      industryProjectOptions: industryProjectOptions,
      departmentTypeOptions: departmentTypeOptions,
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      addressProps: {
        label: "name",
        value: "id",
      },
      industryProps: {
        label: "name",
        value: "id",
      },
      selectedRows1: [],
      selectedRows2: [],
      loading: false,
      rules: {
        project_name: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: ["blur", "change"],
          },
        ],
        bidding_project_name: [
          { required: true, message: "请输入招标项目名称", trigger: "blur" },
        ],
        bidding_project_industry_classification: [
          {
            required: true,
            message: "请选择招标项目行业分类",
            trigger: "change",
          },
        ],
        // supervisory_department_type: [
        //   {
        //     required: true,
        //     message: "请选择监督部门类型",
        //     trigger: "change",
        //   },
        // ],

        qualification_review: [
          {
            required: true,
            message: "请选择资格审核",
            trigger: "change",
          },
        ],
        // supervisory_department: [
        //   { required: true, message: "请输入监督部门", trigger: "blur" },
        // ],
        // approval_department: [
        //   { required: true, message: "请输入审核部门", trigger: "blur" },
        // ],
        owner_unit: [
          { required: true, message: "请输入业主单位", trigger: "blur" },
        ],
        bidding_project_location: [
          { required: true, message: "请选择招标项目地点", trigger: "change" },
        ],
        // bidding_project_establishment_date: [
        //   {
        //     required: true,
        //     message: "请选择招标项目建立时间",
        //     trigger: "change",
        //   },
        // ],
        // bidding_project_implementation_location: [
        //   {
        //     required: true,
        //     message: "请选择招标项目实施地点",
        //     trigger: "change",
        //   },
        // ],
        project_qualification_overview: [
          { required: true, message: "请输入项目资格概况", trigger: "blur" },
        ],
        bidding_agent_scope: [
          {
            required: true,
            message: "请输入招标内容与范围及招标方案说明",
            trigger: "blur",
          },
        ],
      },
      selectProject: [],
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSelectionChange1(data) {
      this.selectedRows1 = data;
    },
    handleSelectionChange2(data) {
      this.selectedRows2 = data;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.handleGetProjectList();
    },
    handleGetProjectList() {
      getProjectList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.handleGetProjectList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.handleGetProjectList();
    },
    handleSelectProject(data) {
      this.selectProject = data;
      console.log(data);
    },
    handleSaveProject() {
      if (this.selectProject.length > 1) {
        this.$message.warning("只能选择一个项目");
        return;
      }
      this.form.project_name = this.selectProject[0].project_name;
      this.form.project_id = this.selectProject[0].id;
      this.innerVisible = false;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    addPerson() {
      this.form.project_team_assignments.push({
        name: null,
        phone: null,
        gender: null,
        position: null,
        title_id: null,
        title_level: null,
        division_id: null,
        performance_requirements: null,
        index: this.generateUniqueId(),
      });
    },
    addBidSegment() {
      this.form.bid_segments.push({
        segment_number: null,
        segment_name: null,
        segment_code: null,
        is_blind: null,
        segment_content: null,
        estimated_contract_price: null,
        start_date: "",
        duration_days: null,
        bid_qualification: null,
        index: this.generateUniqueId(),
      });
    },

    handleDeleteTeam(index, row) {
      this.form.project_team_assignments.splice(index, 1);
    },

    handleBatchDelete1() {
      this.selectedRows1.forEach((item) => {
        let index = this.form.project_team_assignments.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.project_team_assignments.splice(index, 1);
        }
      });
    },
    handleBatchDelete2() {
      this.selectedRows2.forEach((item) => {
        let index = this.form.bid_segments.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.bid_segments.splice(index, 1);
        }
      });
    },
    generateUniqueId() {
      return Date.now().toString(36);
    },
    handleDeleteBid(index, row) {
      this.form.bid_segments.splice(index, 1);
    },

    handleSave() {
      console.log("save", this.form);
      this.$emit("save", this.form);
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$emit("submit", this.form);
    },
    handleFileChange1(file, fileList) {
      console.log(this.form.agency_contract_scan);
      console.log(file, fileList);
    },
    handleSuccess1(res, file, fileList) {
      this.form.agency_contract_scan.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.agency_contract_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.agency_contract_scan.splice(index, 1);
      }
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleShowProject() {
      this.innerVisible = true;
    },
    handleCheckBidder() {
      if (this.form.bidder == null || this.form.bidder == "") {
        this.$message.warning("请输入招标人");
        return;
      }
      getBidderByName(this.form.bidder).then((res) => {
        this.message(res.status_code, "查验成功");
      });
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
