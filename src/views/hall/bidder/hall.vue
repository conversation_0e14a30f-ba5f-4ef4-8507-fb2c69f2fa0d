<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 23:21:09
 * @FilePath: /bid/src/views/hall/bidder/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-row>
      <el-card> <h2 style="text-align: center">开标大厅</h2></el-card>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="18">
        <el-card class="table" :body-style="{ height: height }">
          <el-table :data="tableData" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              prop="bidding_project.bidding_project_code"
              label="时间"
            ></el-table-column>
            <el-table-column
              width="180"
              prop="bidding_project.bidding_project_name"
              show-overflow-tooltip
              label="角色"
            >
            </el-table-column>
            <el-table-column
              prop="segment_name"
              show-overflow-tooltip
              label="公司"
            >
            </el-table-column>

            <el-table-column width="100" prop="status" label="状态">
              <template slot-scope="scope">
                <span>{{ setRegText(scope.row.status) }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div style="text-align: center; padding: 20px 0px">
            <el-pagination
              background
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
              :page-size="queryForm.per_page"
              :current-page="queryForm.page"
              @size-change="handleChangeSize"
              @current-change="handleChangePage"
            >
            </el-pagination>
          </div> </el-card
      ></el-col>
      <el-col :span="6"
        ><el-card :body-style="{ height: height }" style="margin-top: 10px">
          <el-card style="margin-top: 10px; text-align: center">
            <p style="font-size: 28px; color: orange">否</p>
            <h3>签到状态</h3>
          </el-card>
          <el-card style="margin-top: 10px; text-align: center">
            <p style="font-size: 28px; color: orange">否</p>
            <h3>解密状态</h3>
          </el-card>
        </el-card></el-col
      >
    </el-row>
  </div>
</template>

<script>
import { getInviteHallList } from "@/api/hall/index";
import SearchForm from "@/components/SearchForm/index.vue";
export default {
  components: {
    SearchForm,
  },
  props: {
    bidData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      height: 0,
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getInviteHallList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    setRegText(val) {
      if (val == 2) {
        return "未开始";
      } else if (val == 3 || val == 4) {
        return "进行中";
      } else {
        return "已结束";
      }
    },
    getHeight() {
      this.height = window.innerHeight - 220 + "px";
    },
  },

  created() {
    this.getList();
    window.addEventListener("resize", this.getHeight);
    this.getHeight();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
