<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: huiji
 * @LastEditTime: 2025-07-05 23:13:22
 * @FilePath: /bidding-web/src/views/hall/bidder/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <div v-if="mode == 0">
      <el-card>
        <search-form
          :fields="fields"
          :itemsPerRow="2"
          @search="handleSearch"
        ></search-form>
      </el-card>
      <el-card class="table">
        <el-table :data="tableData" border style="margin-top: 10px">
          <!-- 表格列定义 -->
          <el-table-column
            type="index"
            label="序号"
            width="50"
            align="center"
          ></el-table-column>
          <el-table-column
            width="180"
            show-overflow-tooltip
            prop="bidding_project.bidding_project_code"
            label="招标/采购项目编号"
          ></el-table-column>
          <el-table-column
            prop="bidding_project.bidding_project_name"
            show-overflow-tooltip
            label="招标/采购项目名称"
          >
          </el-table-column>

          <el-table-column
            prop="bid_segment.segment_name"
            show-overflow-tooltip
            label="标段名称"
          >
          </el-table-column>
          <el-table-column
            prop="bid_segment.segment_number"
            width="180"
            label="标段编号"
          >
          </el-table-column>
          <el-table-column
            width="180"
            prop="bid_segment.bid_document[0].document_submission_deadline"
            label="开标时间"
          ></el-table-column>
          <el-table-column width="100" prop="status" label="状态">
            <template slot-scope="scope">
              <span>{{
                setRegText(
                  scope.row.bid_segment ? scope.row.bid_segment.flow_status : ""
                )
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              <el-link
                size="mini"
                type="primary"
                @click="handleIn(scope.row, $event)"
                >进入</el-link
              >
            </template>
          </el-table-column>
        </el-table>
        <div style="text-align: center; padding: 20px 0px">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            :page-size="queryForm.per_page"
            :current-page="queryForm.page"
            @size-change="handleChangeSize"
            @current-change="handleChangePage"
          >
          </el-pagination>
        </div>
      </el-card>
    </div>

    <bid-hall :bidData="bidData" v-else></bid-hall>
  </div>
</template>

<script>
import { getBidderHallList, checkBidOpening } from "@/api/hall/index";
import SearchForm from "@/components/SearchForm/index.vue";
import BidHall from "../bidder/hall.vue";
export default {
  components: {
    SearchForm,
    BidHall,
  },
  data() {
    return {
      total: 0,
      mode: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      bidData: {},
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getBidderHallList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    setRegText(val) {
      if (val == 2) {
        return "未开始";
      } else if (val == 3 || val == 4) {
        return "进行中";
      } else {
        return "已结束";
      }
    },
    handleView() {
      this.mode = 1;
      this.$router.push("/hall");
    },
    handleIn(data, event) {
      if (event) event.preventDefault();
      checkBidOpening(data.bid_segment_id)
        .then((res) => {
          const result = res.data;
          if (result && result.can_bid_opening) {
            this.bidData = data;
            window.open(
              `#/open-hall?model=2&projectId=${data.bidding_project.id}&segmentId=${data.bid_segment_id}`,
              "_blank"
            );
          } else {
            this.$message.warning(
              result && result.status_message
                ? result.status_message
                : "未到开标时间，无法进入开标大厅"
            );
          }
        })
        .catch((err) => {
          this.$message.error(
            err.response?.data?.message || "校验开标状态失败"
          );
        });
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
