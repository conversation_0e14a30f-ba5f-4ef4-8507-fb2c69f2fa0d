<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 23:21:09
 * @FilePath: /bid/src/views/hall/bidder/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project.bidding_project_code"
          label="招标/采购项目编号"
        ></el-table-column>
        <el-table-column
          prop="bidding_project.bidding_project_name"
          show-overflow-tooltip
          label="招标/采购项目名称"
        >
        </el-table-column>

        <el-table-column
          prop="segment_name"
          show-overflow-tooltip
          label="标段名称"
        >
        </el-table-column>
        <el-table-column prop="segment_number" width="180" label="标段编号">
        </el-table-column>
        <el-table-column
          width="180"
          prop="bid_document[0].document_submission_deadline"
          label="开标时间"
        ></el-table-column>
        <el-table-column width="100" prop="status" label="状态">
          <template slot-scope="scope">
            <span>{{ setRegText(scope.row.flow_status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="150">
          <template slot-scope="scope">
            <el-link
              size="mini"
              type="primary"
              :href="`#/open-hall?model=3&projectId=${scope.row.bidding_project.id}&segmentId=${scope.row.id}`"
              target="_blank"
              >进入</el-link
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getExpertHallList } from "@/api/hall/index";
import SearchForm from "@/components/SearchForm/index.vue";
export default {
  components: {
    SearchForm,
  },
  data() {
    return {
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getExpertHallList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    setRegText(val) {
      if (val == 2) {
        return "未开始";
      } else if (val == 3 || val == 4) {
        return "进行中";
      } else {
        return "已结束";
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
