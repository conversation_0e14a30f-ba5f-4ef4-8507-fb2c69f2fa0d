<!--
 * @Author: wzc
 * @Date: 2024-10-25 23:02:56
 * @LastEditors: 王子超
 * @LastEditTime: 2024-12-02 23:02:30
 * @FilePath: /bidding-web/src/views/expert-info/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-divider content-position="left">个人信息</el-divider>
    <el-row :gutter="50" style="margin-left: 10%">
      <el-col :span="12">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          status-icon
          size="mini"
        >
          <el-form-item label="姓名:" prop="name">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="性别:" prop="gender">
            <el-select v-model="form.gender" placeholder="请选择">
              <el-option key="0" label="男" :value="0"> </el-option>
              <el-option key="1" label="女" :value="1"> </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="身份证:" prop="id_card">
            <el-input v-model="form.id_card"></el-input>
          </el-form-item>
          <el-form-item label="手机号/用户名:" prop="phone">
            <el-input disabled v-model="form.phone"></el-input>
          </el-form-item>
          <el-form-item label="固定联系电话:" prop="fixed_telephone">
            <el-input v-model="form.fixed_telephone"></el-input>
          </el-form-item>
          <el-form-item label="传真:" prop="fax">
            <el-input v-model="form.fax"></el-input>
          </el-form-item>
          <!-- <el-form-item label="部门:" prop="department">
            <el-input disabled v-model="form.department"></el-input>
          </el-form-item>
          <el-form-item label="职务:" prop="position">
            <el-input disabled v-model="form.position"></el-input>
          </el-form-item> -->
          <el-form-item label="平台身份:" prop="role"> 专家 </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSave">保存</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="4">
        <div style="display: inline-grid">
          <pan-thumb :image="image" />
          <el-button
            type="primary"
            icon="el-icon-upload"
            style="margin-top: 10px"
            @click="imgShow = true"
            >更换
          </el-button>
        </div>
        <image-cropper
          v-if="imgShow"
          :width="300"
          :height="300"
          field="file"
          url="https://bidding.senmoio.cn/api/file"
          lang-type="zh"
          @close="close"
          @crop-upload-success="cropSuccess"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import PanThumb from "@/components/PanThumb";
import ImageCropper from "@/components/ImageCropper";
import { getInfo, updateUser } from "@/api/user";
export default {
  name: "Tab",
  components: {
    ImageCropper,
    PanThumb,
  },
  data() {
    return {
      form: {},
      imgShow: false,
      image: "",
    };
  },
  watch: {},
  created() {
    getInfo().then((res) => {
      this.form = { ...res.data };
      this.image = "http://bidding.senmoio.cn/" + this.form.user_img.path;
      console.log(res.data);
    });
  },
  methods: {
    showCreatedTimes() {
      this.createdTimes = this.createdTimes + 1;
    },
    cropSuccess(resData) {
      console.log(resData);
      this.imgShow = false;
      this.image = "http://bidding.senmoio.cn/" + resData.path;
      this.form.user_img = resData;
      console.log(this.image);
    },
    close() {
      this.imgShow = false;
    },
    handleSave() {
      updateUser(this.form).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
.main {
  padding: 10px;
}
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
