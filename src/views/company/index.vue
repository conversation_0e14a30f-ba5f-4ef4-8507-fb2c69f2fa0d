<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="company_name"
          label="企业名称"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="company_short_name"
          label="公司简称"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="company_phone"
          label="公司电话"
        ></el-table-column>
        <el-table-column
          prop="business_term_start"
          label="企业注册日期"
        ></el-table-column>
        <el-table-column
          prop="registered_capital"
          label="注册资金(元)"
        ></el-table-column>
        <el-table-column
          prop="legal_representative"
          label="法人代表"
        ></el-table-column>
        <el-table-column
          prop="updated_at"
          label="提交日期"
          show-overflow-tooltip
          width="200"
        ></el-table-column>
        <el-table-column prop="type" label="状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.type) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button size="mini" type="primary" @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="primary"
              @click="handleViewUser(scope.row)"
              >公司成员</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>

    <audit-form
      v-if="visible"
      :companyId="companyId"
      :visible="visible"
      @close="handleClose"
    ></audit-form>

    <el-dialog title="公司成员" :visible.sync="userVisible" width="70%">
      <el-table :data="userTableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column label="性别" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.gender == 0 ? "男" : "女" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="账号"></el-table-column>
        <el-table-column prop="role" label="角色">
          <template slot-scope="scope">
            <span>{{ setRoleName(scope.row.roles) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态">
          <template slot-scope="scope">
            <span>{{ scope.row.state == 1 ? "启用" : "停用" }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCompanyList,
  getCompanyById,
  auditCompany,
  getUsersByCompanyId,
} from "@/api/company/index";
import SearchForm from "@/components/SearchForm/index.vue";
import AuditForm from "./modules/audit-form.vue";
export default {
  components: {
    SearchForm,
    AuditForm,
  },
  data() {
    return {
      visible: false,
      userVisible: false,
      companyId: null,
      userTableData: [],
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "企业名称：",
          prop: "name",
          component: "el-input",
          props: {
            placeholder: "请输入企业名称",
          },
        },
        {
          label: "审核状态：",
          prop: "type",
          component: "el-select",
          props: {
            placeholder: "请选择审核状态",
          },
          options: [
            { label: "全部", value: null },
            { label: "待审核", value: 4 },
            { label: "审核通过", value: 5 },
            { label: "审核退回", value: 6 },
          ],
        },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
      industryOptions: [],
    };
  },
  methods: {
    setRoleName(data) {
      let text = "";
      data.forEach((item) => {
        text += item.description + ",";
      });
      return text.substring(0, text.length - 1);
    },
    handleViewUser(data) {
      this.userVisible = true;
      getUsersByCompanyId(data.id).then((res) => {
        this.userTableData = res.data.data;
      });
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getCompanyList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    setStatusText(val) {
      let text = "";
      switch (val) {
        case 4:
          text = "待审核";
          break;
        case 5:
          text = "审核通过";
          break;
        case 6:
          text = "审核退回";
          break;
        default:
          break;
      }
      return text;
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleView(data) {
      this.companyId = data.id;
      this.visible = true;
    },
    handleClose() {
      this.visible = false;
      this.getList();
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
    // getIndustries().then((res) => {
    //   this.industryOptions = res.data;
    // });
    // getGeoAreas().then((res) => {
    //   this.addressOptions = res.data;
    // });
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
