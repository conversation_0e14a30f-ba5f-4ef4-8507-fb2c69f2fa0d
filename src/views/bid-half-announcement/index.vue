<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 20:06:03
 * @FilePath: /bid/src/views/bid-half-announcement/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>

        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="company_info.company_name"
          label="公司"
        ></el-table-column>
        <el-table-column prop="announcement_cate" width="180" label="发布类别">
          <template> 半流程招投标公告发布</template>
        </el-table-column>
        <el-table-column prop="announcement_cate" width="180" label="公告类型">
          <template slot-scope="scope">
            {{ setTypeText(scope.row.announcement_cate) }}
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="title"
          label="公告名称"
        ></el-table-column>

        <el-table-column
          width="180"
          prop="publish_date"
          label="发布日期"
        ></el-table-column>

        <el-table-column
          prop="publisher"
          show-overflow-tooltip
          width="120"
          label="发布人"
        ></el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="keywords"
          label="关键字"
        ></el-table-column>
        <el-table-column width="100" prop="status" label="公告状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>

    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :fileList="fileList"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import {
  getBidHalfAnnouncements,
  getBidHalfAnnouncementById,
  auditBidAnnouncement,
} from "@/api/bid-manage/bid-announcement-audit";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";

export default {
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "公告名称：",
          prop: "title",
          component: "el-input",
          props: {
            placeholder: "请输入公告名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleUpdate(data) {
      this.addShow = true;
      getBidHalfAnnouncementById(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        if (this.editForm.attachments != null) {
          this.editForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },
    getList() {
      getBidHalfAnnouncements(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        updateBidHalfAnnouncements(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addBidHalfAnnouncements(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      auditBidAnnouncement(data).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteBidHalfAnnouncements(row.id).then((res) => {
            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    setTypeText(status) {
      let text = "";
      switch (status) {
        case 1:
          text = "招标公告";
          break;
        case 2:
          text = "变更公告";
          break;
        case 3:
          text = "招标候选人公示";
          break;
        case 4:
          text = "结果公告";
          break;
        case 5:
          text = "废标公告";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
