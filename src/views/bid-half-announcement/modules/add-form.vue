<template>
  <div>
    <el-dialog
      title="招标公告表单"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      disabled
                      v-model="form.bidding_project_name"
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="标段" name="2">
          <div>
            <el-table
              :data="form.bid_segments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" prop="segment_number">
              </el-table-column>
              <el-table-column label="标段名称" prop="segment_name">
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="基本信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="公告名称:" prop="title">
                  <el-input
                    disabled
                    v-model="form.title"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="公司:" prop="company_info_id">
                  <el-select
                    disabled
                    v-model="form.company_info_id"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in companyList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    /> </el-select></el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="发布类别:" prop="publish_type">
                  <el-select
                    disabled
                    v-model="form.publish_type"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="半流程招投标公告发布" value="0">
                    </el-option> </el-select></el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="公告类别:" prop="announcement_cate">
                  <el-select
                    disabled
                    v-model="form.announcement_cate"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="招标公告" :value="1"> </el-option>
                    <el-option key="1" label="变更公告" :value="2"> </el-option>
                    <el-option key="2" label="招标候选人公示" :value="3">
                    </el-option>
                    <el-option key="3" label="结果公告" :value="4"></el-option>
                    <el-option key="4" label="废标公告" :value="5">
                      <el-option key="5" label="澄清公告" :value="9">
                      </el-option> </el-option></el-select></el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="所属地区:" prop="geo_area_id">
                  <el-select
                    disabled
                    v-model="form.geo_area_id"
                    placeholder="请选择"
                  >
                    <el-option key="1" label="河北" :value="36">
                    </el-option> </el-select></el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="关键字:" prop="keywords">
                  <el-input
                    disabled
                    v-model="form.keywords"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="发布人:" prop="publisher">
                  <el-input
                    disabled
                    v-model="form.publisher"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="发布时间:" prop="publish_date">
                  <el-date-picker
                    disabled
                    v-model="form.publish_date"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="修改人:" prop="modifier">
                  <el-input
                    disabled
                    v-model="form.modifier"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="修改时间:" prop="modify_date">
                  <el-date-picker
                    v-model="form.modify_date"
                    disabled
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="来源:" prop="source">
                  <el-input
                    disabled
                    v-model="form.source"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="作者:" prop="author">
                  <el-input
                    disabled
                    v-model="form.author"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="公告详情" name="2">
          <Tinymce ref="editor" v-model="form.content" :height="400" />
        </el-collapse-item>
        <el-collapse-item title="相关附件" name="3">
          <el-upload
            ref="upload1"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>

        <el-collapse-item title="审核记录" name="4">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <div style="margin-top: 10px">
        <el-form
          size="mini"
          ref="auditForm"
          :model="auditForm"
          :rules="rules"
          label-width="180px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="审核备注" prop="approval_view">
                <el-input
                  :disabled="isDisabled"
                  type="textarea"
                  v-model="auditForm.approval_view"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核状态" prop="status">
                <el-radio-group
                  :disabled="isDisabled"
                  v-model="auditForm.status"
                >
                  <el-radio :label="2">审核通过</el-radio>
                  <el-radio :label="3">审核退回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :disabled="isDisabled" @click="handleSubmit"
          >提交审核</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import Tinymce from "@/components/Tinymce";
export default {
  name: "ProjectForm",
  components: {
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          console.log(val);

          this.form.bidding_project_name =
            val.bidding_project.bidding_project_name;
          this.form.bidding_project_code =
            val.bidding_project.bidding_project_code;
          this.form.publish_type = "0";
          if (this.form.attachments == null) {
            this.form.attachments = [];
          }
          if (this.form.status !== 1) {
            this.isDisabled = true;
          }
        }
      },
      deep: true,
    },
  },
  created() {
    let user = this.$store.getters.user;
    let obj = {
      label: user.company_info.company_name,
      value: user.company_info.id,
    };
    this.companyList.push(obj);
    console.log(user);
  },
  data() {
    return {
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      auditForm: {},
      isDisabled: false,
      collapse: ["1", "2", "3", "4"],
      form: {
        publish_type: "0",
        status: 0,
        title: "",
        publisher: this.$store.getters.name,
        company_info_id: null,
        announcement_cate: null,
        geo_area_id: 36,
        keywords: "",
        publish_date: "",
        modify_date: "",
        source: "",
        author: "",
        content: "",
        attachments: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      companyList: [],
      rules: {
        status: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              this.auditForm.notice_id = this.form.id;
              this.auditForm.bidding_project_id = this.form.bidding_project_id;
              this.auditForm.title = this.form.title;
              this.auditForm.company_id = this.form.company_info_id;
              this.auditForm.notice_status = this.form.announcement_cate;
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
