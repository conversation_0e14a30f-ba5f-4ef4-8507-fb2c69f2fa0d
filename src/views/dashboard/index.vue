<!--
 * @Author: 王子超
 * @Date: 2024-12-02 22:00:05
 * @LastEditors: huiji
 * @LastEditTime: 2025-06-26 00:00:27
 * @FilePath: /bidding-web/src/views/dashboard/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <!-- 待办事项 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <el-card class="todo-card">
          <div slot="header" class="card-header">
            <span class="card-title">待办事项</span>
          </div>
          <div class="todo-content">
            <el-tabs
              v-model="todoTab"
              @tab-click="handleTodoTabChange"
              size="small"
            >
              <el-tab-pane label="全部" name="all"></el-tab-pane>
              <el-tab-pane label="未读" name="unread"></el-tab-pane>
              <el-tab-pane label="已读" name="read"></el-tab-pane>
            </el-tabs>
            <div
              v-for="(todo, idx) in filteredTodoList"
              :key="idx"
              class="todo-item"
              @click="handleTodoClick(todo)"
            >
              <i
                :class="[
                  'todo-icon',
                  todo.is_read === true
                    ? 'el-icon-message read-icon'
                    : 'el-icon-chat-dot-square unread-icon',
                ]"
              ></i>
              <span class="todo-text">{{
                `【${todo.notification.title}】` + todo.notification.content
              }}</span>
            </div>
          </div>
          <div class="todo-pagination">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="todoPagination.total"
              :page-size="todoPagination.pageSize"
              :current-page="todoPagination.currentPage"
              @current-change="handleTodoPageChange"
            >
            </el-pagination>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12" class="right-panel">
        <!-- 日历 -->
        <el-card class="calendar-card">
          <div class="calendar-header">
            <el-select
              v-model="selectedYear"
              size="small"
              style="width: 100px; margin-right: 10px"
            >
              <el-option
                v-for="year in years"
                :key="year"
                :label="year + '年'"
                :value="year"
              >
              </el-option>
            </el-select>
            <el-select v-model="selectedMonth" size="small" style="width: 80px">
              <el-option
                v-for="month in months"
                :key="month"
                :label="month + '月'"
                :value="month"
              >
              </el-option>
            </el-select>
          </div>
          <el-calendar v-model="calendarValue" :range="calendarRange">
            <template slot="dateCell" slot-scope="{ data }">
              <div class="calendar-day">
                {{ data.day.split("-")[2] }}
              </div>
            </template>
          </el-calendar>
        </el-card>

        <!-- 公司资讯 -->
        <el-card class="news-card">
          <div slot="header" class="card-header">
            <span class="card-title">公司资讯&政策法规</span>
          </div>
          <div class="news-content">
            <div
              v-for="(news, index) in newsList"
              :key="index"
              class="news-item"
              @click="handleNoticeInfo(news)"
            >
              <div class="news-title">{{ news.title }}</div>
              <!-- <div class="news-date">{{ news.date }}</div> -->
            </div>
          </div>
          <div class="news-pagination">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="newsPagination.total"
              :page-size="5"
              :current-page="newsPagination.currentPage"
              @current-change="handleNewsPageChange"
            >
            </el-pagination>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      title="待办事项详情"
      :visible.sync="todoDetailDialog"
      width="400px"
      @close="handleTodoDetailClose"
    >
      <div v-if="currentTodo">
        <p><strong>内容：</strong>{{ currentTodo.notification.content }}</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="todoDetailDialog = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import adminDashboard from "./admin";
import editorDashboard from "./editor";
import { fetchNotifications } from "@/api/notification";
import { markNotificationRead } from "@/api/notification";
import { getNoticesTwo } from "@/api/home/<USER>";
export default {
  name: "Dashboard",
  components: { adminDashboard, editorDashboard },
  data() {
    return {
      // 日历相关
      calendarValue: new Date(),
      selectedYear: new Date().getFullYear(),
      selectedMonth: new Date().getMonth() + 1,
      years: [],
      months: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],

      // 待办事项分页
      todoPagination: {
        currentPage: 1,
        pageSize: 15,
        total: 0,
      },

      // 待办事项页签
      todoTab: "all",

      // 待办事项数据
      todoList: [],

      // 公司资讯
      newsList: [
        {
          title: "易招天成河北交易平台关于平台收费的通知",
          date: "2024-01-15",
        },
        {
          title: "易招天成河北交易平台关于平台收费的通知",
          date: "2024-01-14",
        },
        {
          title: "系统维护通知",
          date: "2024-01-13",
        },
        {
          title: "新功能上线公告",
          date: "2024-01-12",
        },
        {
          title: "新功能上线公告",
          date: "2024-01-12",
        },
      ],

      // 公司资讯分页
      newsPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      currentRole: "adminDashboard",
      indexUrl: require("@/assets/images/index.jpg"),
      todoDetailDialog: false,
      currentTodo: null,
    };
  },
  computed: {
    ...mapGetters(["roles"]),
    calendarRange() {
      const year = this.selectedYear;
      const month = this.selectedMonth;
      const start = new Date(year, month - 1, 1);
      const end = new Date(year, month, 0);
      return [start, end];
    },
    filteredTodoList() {
      return this.todoList;
    },
  },
  watch: {
    selectedYear(newVal) {
      this.updateCalendar();
    },
    selectedMonth(newVal) {
      this.updateCalendar();
    },
  },
  created() {
    if (!this.roles.includes("admin")) {
      this.currentRole = "editorDashboard";
    }
    this.initYears();
    this.getTodoList();
    this.getNoticesTwo();
  },
  methods: {
    initYears() {
      const currentYear = new Date().getFullYear();
      for (let i = currentYear - 5; i <= currentYear + 5; i++) {
        this.years.push(i);
      }
    },

    updateCalendar() {
      this.calendarValue = new Date(
        this.selectedYear,
        this.selectedMonth - 1,
        1
      );
    },

    async handleTodoClick(todo) {
      this.currentTodo = todo;
      this.todoDetailDialog = true;
      if (todo.status !== "read") {
        try {
          await markNotificationRead(todo.id);
        } catch (e) {}
      }
    },

    handleTodoPageChange(page) {
      this.todoPagination.currentPage = page;
      this.getTodoList();
    },

    handleNewsClick(news) {
      this.$message({
        message: `查看资讯: ${news.title}`,
        type: "info",
      });
    },

    handleNewsPageChange(page) {
      this.newsPagination.currentPage = page;
      this.getNoticesTwo();
    },

    handleTodoTabChange() {
      this.todoPagination.currentPage = 1;
      this.getTodoList();
    },

    // 获取待办事项列表
    async getTodoList() {
      let read_status = null;
      if (this.todoTab === "unread") read_status = 0;
      if (this.todoTab === "read") read_status = 1;
      const params = {
        page: this.todoPagination.currentPage,
        per_page: this.todoPagination.pageSize,
        read_status,
      };
      try {
        const res = await fetchNotifications(params);
        // 假设返回格式为 { data: { list: [], total: 100 } }
        this.todoList = res.data.data || [];
        this.todoPagination.total = res.data.total || 0;
      } catch (e) {
        this.todoList = [];
        this.todoPagination.total = 0;
      }
    },
    handleTodoDetailClose() {
      this.todoDetailDialog = false;
      this.getTodoList();
    },
    getNoticesTwo() {
      let params1 = {
        page: this.newsPagination.currentPage,
        per_page: 5,
      };
      getNoticesTwo(params1).then((res) => {
        this.newsList = res.data.data;
        this.newsPagination.total = res.data.total;
        console.log("111", this.gsList);
      });
    },
    handleNoticeInfo(item) {
      console.log(item);
      const targetRoute = this.$router.resolve({
        path: "notice-index",
        query: { id: item.id, type: item.announcement_cate },
      });
      window.open(targetRoute.href, "_blank");

      // this.$router.push({
      //   path: "notice-index",
      //   query: { id: item.id, type: item.announcement_cate },
      // });
      console.log(item);
    },
  },
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  width: 100%;
  height: calc(100vh - 50px);
  overflow: hidden;
  padding: 20px;
  background-color: #f5f7fa;

  .el-row {
    height: 100%;
  }
}

.full-screen-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 待办事项卡片 */
.todo-card {
  height: 91vh;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  display: flex;
  flex-direction: column;

  ::v-deep .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
}

.todo-content {
  flex: 1;
  padding: 20px 0;
  overflow: auto;
  position: relative;
  height: 50vh;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin-bottom: 10px;
}

.todo-item:hover {
  background-color: #f0f8ff;
  padding-left: 10px;
}

.todo-icon {
  font-size: 20px;
  margin-right: 15px;
  width: 24px;
  text-align: center;
}

.read-icon {
  color: #67c23a;
}

.unread-icon {
  color: #e6a23c;
}

.todo-text {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.todo-pagination {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

/* 日历卡片 */
.calendar-card {
  margin-bottom: 5px;
  height: 50vh;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  text-align: center;
  margin-bottom: 8px;
  padding: 4px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

// .el-calendar-table .el-calendar-day {
//   padding: 2px !important;
//   height: 22px !important;
//   min-height: 22px !important;
//   height: 0px !important;
// }

.calendar-day {
  text-align: center;
  padding: 0 !important;
  border-radius: 4px;
  transition: all 0.2s;
  font-size: 11px !important;
  width: 22px;
  height: 22px;
  line-height: 22px;
  margin: 0 auto;
}

.calendar-day:hover {
  background-color: #409eff;
  color: white;
}

/* 公司资讯卡片 */
.news-card {
  height: 50vh;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.news-content {
  min-height: 200px;
}

.news-item {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
  margin-bottom: 6px;
}

.news-item:hover {
  background-color: #f0f8ff;
  border-left: 4px solid #409eff;
  padding-left: 20px;
}

.news-item:last-child {
  border-bottom: none;
}

.news-title {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 8px;
  line-height: 1.4;
}

.news-date {
  font-size: 12px;
  color: #909399;
}

.news-pagination {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
}

.right-panel {
  display: flex;
  flex-direction: column;
  height: 91vh;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 10px;
  }

  .todo-card,
  .calendar-card,
  .news-card {
    margin-bottom: 15px;
  }

  .calendar-header {
    display: flex;
    justify-content: center;
    gap: 10px;
  }
}

/* Element UI 样式覆盖 */
.el-card {
  border-radius: 12px;
  overflow: hidden;
}

.el-card__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.el-card__body {
  padding: 20px;
}

.el-pagination {
  margin-top: 10px;
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
  background-color: #409eff;
}

.el-calendar__header {
  display: none;
}

// .el-calendar-table >>> .el-calendar-day {
//   padding: 8px;
//   height: 0px !important;
// }

.el-calendar-table td.is-today .calendar-day {
  background-color: #409eff;
  color: white;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  line-height: 20px;
  margin: 0 auto;
}
</style>
<style>
.el-calendar-day {
  padding: 5px;
  height: 40px !important;
  justify-content: center;
  align-items: center;
  display: flex;
}
.el-calendar-table thead th {
  padding: 5px !important;
}
</style>
