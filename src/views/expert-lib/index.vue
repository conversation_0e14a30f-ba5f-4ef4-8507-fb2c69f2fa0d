<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>

        <el-table-column prop="name" label="姓名"></el-table-column>
        <el-table-column prop="gender" label="性别">
          <template slot-scope="scope">
            <span>{{ scope.row.gender == 0 ? "男" : "女" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="birthday"
          label="出生日期"
          width="100"
        ></el-table-column>
        <el-table-column prop="nation" label="民族"></el-table-column>
        <el-table-column
          prop="political_status"
          label="政治面貌"
        ></el-table-column>
        <el-table-column
          prop="districts"
          label="所属地区"
          width="260"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="expert_type_view"
          label="专家类别"
          width="260"
          show-overflow-tooltip
        ></el-table-column>
        <!-- <el-table-column prop="email" label="信息来源"></el-table-column> -->
        <el-table-column
          prop="unit"
          label="工作单位"
          width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="retirement_status" label="退休状态">
          <template slot-scope="scope">
            <span>{{ setRetirementText(scope.row.retirement_status) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <add-form
      v-if="addShow"
      :visible="addShow"
      :addressOptions="addressOptions"
      :expertOptions="expertOptions"
      :formData="formData"
      :fileList1="fileList1"
      :fileList2="fileList2"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import { getExpertTypes } from "@/api/expert/expert";
import { getExpertList, auditExpert, getExpertById } from "@/api/expert/index";
import { getGeoAreas } from "@/api/bid-manage/project-info";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";

export default {
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fileList1: [],
      fileList2: [],
      expertOptions: [],
      formData: {},
      fields: [
        {
          label: "专家名称：",
          prop: "name",
          component: "el-input",
          props: {
            placeholder: "请输入专家名称",
          },
        },
        {
          label: "所属地区：",
          prop: "district",
          component: "el-input",
          props: {
            placeholder: "请输入所属地区",
          },
        },
        {
          label: "专家类别：",
          prop: "expert_type",
          component: "el-input",
          props: {
            placeholder: "请输入专家类别",
          },
        },
        {
          label: "工作单位：",
          prop: "unit",
          component: "el-input",
          props: {
            placeholder: "请输入工作单位",
          },
        },
        {
          label: "退休状态",
          prop: "retirement_status",
          component: "el-select",
          props: {
            placeholder: "请选择退休状态",
          },
          options: [
            { label: "全部", value: null },
            { label: "正常", value: "0" },
            { label: "返聘", value: "1" },
            { label: "退休", value: "2" },
          ],
        },
        {
          label: "状态：",
          prop: "status",
          component: "el-select",
          props: {
            placeholder: "请选择状态",
          },
          options: [
            { label: "全部", value: null },
            { label: "待审核", value: 1 },
            { label: "审核通过", value: 2 },
            { label: "审核退回", value: 3 },
          ],
        },
      ],
      tableData: [], // 过滤后的数据
      addressOptions: [],
    };
  },
  methods: {
    setRetirementText(val) {
      let text = "";
      switch (val) {
        case "0":
          text = "正常";
          break;
        case "1":
          text = "返聘";
          break;
        case "2":
          text = "退休";
          break;
        default:
          break;
      }
      return text;
    },
    setStatusText(val) {
      let text = "";
      switch (val) {
        case 0:
          text = "未提交";
          break;
        case 1:
          text = "待审核 ";
          break;
        case 2:
          text = " 审核通过";
          break;
        case 3:
          text = " 审核退回";
          break;
        default:
          break;
      }
      return text;
    },
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleUpdate(data) {
      this.addShow = true;
      getGeoAreas().then((res) => {
        this.addressOptions = res.data;
      });
      getExpertTypes().then((res) => {
        this.expertOptions = res.data;
      });
      getExpertById(data.id).then((res) => {
        this.formData = res.data;
        this.fileList1 = [];
        this.formData.professional_certificate.forEach((item) => {
          let obj = {
            name: item.name,
            url: item.path,
          };
          this.fileList1.push(obj);
        });
        this.fileList2 = [];
        this.formData.certificate_occupancy.forEach((item) => {
          let obj = {
            name: item.name,
            url: item.path,
          };
          this.fileList2.push(obj);
        });
      });
    },

    getList() {
      getExpertList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 0;
      if (data.id) {
        updateExpert(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addExpert(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      auditExpert(data).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },

    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.getList();
        this.addShow = false;
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
