<template>
  <div>
    <el-dialog
      title="平台专家库管理"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="发布信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="专家姓名:" prop="name">
                  <el-input
                    disabled
                    v-model="form.name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="身份证号:" prop="number_id">
                  <el-input
                    disabled
                    v-model="form.number_id"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="性别:" prop="gender">
                  <el-select
                    disabled
                    v-model="form.gender"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="男" :value="0"> </el-option>
                    <el-option key="1" label="女" :value="1"> </el-option>
                  </el-select> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="出生日期:" prop="birthday">
                  <el-date-picker
                    disabled
                    v-model="form.birthday"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="民族:" prop="nation">
                  <el-input
                    disabled
                    v-model="form.nation"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="政治面貌:" prop="political_status">
                  <el-input
                    disabled
                    v-model="form.political_status"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="详情信息" name="2">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="专家类别:" prop="expert_type">
                  <el-cascader
                    disabled
                    v-model="form.expert_type"
                    :props="expertProps"
                    :options="expertOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="所属地区:" prop="district">
                  <el-cascader
                    disabled
                    v-model="form.district"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="专家专业:" prop="expert_major">
                  <el-input
                    disabled
                    v-model="form.expert_major"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="专家职称:" prop="expert_title">
                  <el-select
                    disabled
                    v-model="form.expert_title"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="正高级" value="0"> </el-option>
                    <el-option key="1" label="副高级" value="1"> </el-option>
                    <el-option key="2" label="中级" value="2"> </el-option>
                    <el-option key="3" label="初级" value="3"> </el-option>
                  </el-select> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="从业信息" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label=" 从业单位:" prop="unit">
                  <el-input
                    disabled
                    v-model="form.unit"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="组织机构代码:" prop="organization_code">
                  <el-input
                    disabled
                    v-model="form.organization_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label=" 退休状态:" prop="retirement_status">
                  <el-select
                    disabled
                    v-model="form.retirement_status"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="正常" value="0"> </el-option>
                    <el-option key="1" label="返聘" value="1"> </el-option>
                    <el-option key="2" label="退休" value="2"> </el-option>
                  </el-select> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="参加工作时间:" prop="work_time">
                  <el-date-picker
                    disabled
                    v-model="form.work_time"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="联系方式" name="4">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label=" 手机号码:" prop="phone">
                  <el-input
                    disabled
                    v-model="form.phone"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="电子邮箱:" prop="email">
                  <el-input
                    disabled
                    v-model="form.email"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="通讯地址:" prop="mail_address">
                  <el-input
                    disabled
                    v-model="form.mail_address"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="附件信息" name="5">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="职业证书:" prop="professional_certificate">
                  <el-upload
                    ref="upload1"
                    :file-list="fileList1"
                    :on-preview="handlePreview"
                    :before-remove="handleRemove"
                  >
                  </el-upload></el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="入驻证明:" prop="certificate_occupancy">
                  <el-upload
                    ref="upload2"
                    :file-list="fileList2"
                    :on-preview="handlePreview"
                    :before-remove="handleRemove"
                  >
                  </el-upload> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="审核记录" name="6">
          <el-table :data="logData" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>

        <!-- 表单操作 -->

        <div style="margin-top: 10px">
          <el-form
            size="mini"
            ref="auditForm"
            :model="auditForm"
            :rules="rules"
            label-width="180px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="审核备注" prop="approval_view">
                  <el-input
                    :disabled="isDisabled"
                    type="textarea"
                    v-model="auditForm.approval_view"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审核状态" prop="status">
                  <el-radio-group
                    :disabled="isDisabled"
                    v-model="auditForm.status"
                  >
                    <el-radio :label="2">审核通过</el-radio>
                    <el-radio :label="3">审核退回</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :disabled="isDisabled" @click="handleSubmit"
          >提交审核</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "AddForm",
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    formData: {
      type: Object,
      default: () => {},
    },
    addressOptions: {
      type: Array,
      default: () => [],
    }, // 这里应该是地址的级联选项，需要根据实际情况填充
    expertOptions: {
      type: Array,
      default: () => [],
    },
    fileList1: {
      type: Array,
      default: () => [],
    },
    fileList2: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    formData: {
      handler(val) {
        console.log(val);
        if (val) {
          this.form = { ...this.formData };
          this.auditForm.company_id = val.company_id;
          this.auditForm.expert_id = val.id;
          this.logData = val.approvalLog;
          if (this.form.status !== 1) {
            this.isDisabled = true;
          }
        }
      },
      deep: true,
    },
  },
  data() {
    return {
      auditForm: {},
      logData: [],
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5", "6"],
      header: {
        Authorization: "Bearer " + getToken(),
      },
      form: {
        status: 0,
        professional_certificate: [],
        certificate_occupancy: [],
      },
      addressProps: {
        label: "name",
        value: "id",
      },
      expertProps: {
        label: "name",
        value: "id",
        multiple: true,
      },
      loading: false,
      rules: {
        status: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    setStatusText(val) {
      let text = "";
      switch (val) {
        case 0:
          text = "未提交";
          break;
        case 1:
          text = "待审核 ";
          break;
        case 2:
          text = " 审核通过";
          break;
        case 3:
          text = " 审核退回";
          break;
        default:
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
