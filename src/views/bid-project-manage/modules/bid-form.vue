<template>
  <div>
    <el-dialog
      title="招标项目表单"
      :visible.sync="visible"
      width="90%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="基本信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="24">
                <el-form-item label="所属项目:" prop="project_name">
                  <div style="display: flex">
                    <el-input v-model="form.project_name" disabled></el-input>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目分类:" prop="bidding_project_cate">
                  <el-select
                    disabled
                    v-model="form.bidding_project_cate"
                    placeholder="请选择"
                  >
                    <el-option key="1" label="工程" :value="1"> </el-option>
                    <el-option key="2" label="采购" :value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 2"
                  :label="labelText + '方式'"
                  prop="bidding_project_purchase_method"
                >
                  <el-select
                    disabled
                    v-model="form.bidding_project_purchase_method"
                    placeholder="请选择"
                  >
                    <el-option key="1" label="竞争性谈判" :value="1">
                    </el-option>
                    <el-option key="2" label="竞争性磋商" :value="2">
                    </el-option>
                    <el-option key="3" label="询比采购" :value="3"> </el-option>
                    <el-option key="4" label="单一来源" :value="4"> </el-option>
                    <el-option key="5" label="公开招标" :value="5"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  :label="labelText + '项目编号:'"
                  prop="bidding_project_code"
                >
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  :label="labelText + '项目名称:'"
                  prop="bidding_project_name"
                >
                  <el-input
                    disabled
                    v-model="form.bidding_project_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 1"
                  :label="labelText + '项目类型:'"
                  prop="bidding_project_type"
                >
                  <el-select
                    disabled
                    v-model="form.bidding_project_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in projectTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                    <!-- 其他选项可以在这里添加 -->
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-else
                  :label="labelText + '项目类型:'"
                  prop="bidding_project_type"
                >
                  <el-select
                    disabled
                    v-model="form.bidding_project_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in buyProjectTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                    <!-- 其他选项可以在这里添加 -->
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item
                  :label="labelText + '项目行业分类:'"
                  prop="bidding_project_industry_classification"
                >
                  <el-select
                    v-model="form.bidding_project_industry_classification"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in industryProjectOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item
                  label="项目行业分类:"
                  prop="bidding_project_industry_classification"
                >
                  <el-cascader
                    disabled
                    v-model="form.bidding_project_industry_classification"
                    :props="industryProps"
                    :options="industryOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 1"
                  :label="labelText + '形式:'"
                  prop="bidding_method"
                >
                  <el-select
                    disabled
                    v-model="form.bidding_method"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="公开招标" value="0"> </el-option>
                    <el-option key="1" label="邀请招标" value="1"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资格审查:" prop="qualification_review">
                  <el-select
                    disabled
                    v-model="form.qualification_review"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="资格后审" :value="1"> </el-option>
                    <el-option key="1" label="资格预审" :value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联合体招标:" prop="joint_bidding">
                  <el-radio-group disabled v-model="form.joint_bidding">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                  </el-radio-group>
                </el-form-item></el-col
              >
              <el-col :span="12">
                <el-form-item
                  v-if="form.bidding_project_cate == 1"
                  label="监督部门类型："
                  prop="supervisory_department_type"
                >
                  <el-select
                    disabled
                    v-model="form.supervisory_department_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in departmentTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  v-else
                  label="监督部门类型："
                  prop="supervisory_department_type"
                >
                  <el-select
                    disabled
                    v-model="form.supervisory_department_type"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in buyDepartmentTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="监督部门:" prop="supervisory_department">
                  <el-input
                    disabled
                    v-model="form.supervisory_department"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="审核部门:" prop="approval_department">
                  <el-input
                    disabled
                    v-model="form.approval_department"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="业主单位:" prop="owner_unit">
                  <el-input
                    disabled
                    v-model="form.owner_unit"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标项目地点:"
                  prop="bidding_project_location"
                >
                  <el-cascader
                    disabled
                    v-model="form.bidding_project_location"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标项目建立时间:"
                  prop="bidding_project_establishment_date"
                >
                  <el-date-picker
                    disabled
                    v-model="form.bidding_project_establishment_date"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标项目实施地点:"
                  prop="bidding_project_implementation_location"
                >
                  <el-cascader
                    disabled
                    v-model="form.bidding_project_implementation_location"
                    :props="addressProps"
                    :options="addressOptions"
                    placeholder="请选择"
                  ></el-cascader> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item
                label="项目资格概况:"
                prop="project_qualification_overview"
              >
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.project_qualification_overview"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item
                label="招标内容与范围及招标方案说明:"
                prop="bidding_scope_and_plan_description"
              >
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.bidding_scope_and_plan_description"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="组织形式" name="2">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标组织形式:" prop="organizational_form">
                  <el-input
                    disabled
                    v-model="form.organizational_form"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="招标代理:" prop="bidding_agent">
                  <el-input
                    disabled
                    v-model="form.bidding_agent"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标人:" prop="bidder">
                  <div style="display: flex">
                    <el-input disabled v-model="form.bidder"></el-input>
                  </div> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-form-item
                label="招标代理内容与范围:"
                prop="bidding_agent_scope"
              >
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.bidding_agent_scope"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item
                label="招标代理权限:"
                prop="bidding_agent_authority"
              >
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.bidding_agent_authority"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="成员与计划" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-form-item label="工作任务及计划:" prop="task_plan">
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.task_plan"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="团队成员及分工" name="4">
          <div>
            <el-table
              :data="form.project_team_assignments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="姓名" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.name"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="联系电话">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.phone"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="性别" width="180">
                <template slot-scope="scope">
                  <el-select
                    disabled
                    v-model="scope.row.gender"
                    placeholder="请选择"
                  >
                    <el-option label="男" :value="0"></el-option>
                    <el-option label="女" :value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="职务">
                <template slot-scope="scope">
                  <el-select
                    disabled
                    v-model="scope.row.position"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="组长" value="0"></el-option>
                    <el-option label="组员" value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="职称">
                <template slot-scope="scope">
                  <el-select
                    disabled
                    v-model="scope.row.title_id"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in titlesOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="职称等级" width="180">
                <template slot-scope="scope">
                  <el-select
                    disabled
                    v-model="scope.row.title_level"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="初级" value="0"></el-option>
                    <el-option label="中级" value="1"></el-option>
                    <el-option label="高级" value="2"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <!-- <el-table-column label="分工明细" width="180">
                <template slot-scope="scope">
                  <el-select
                    disabled
                    v-model="scope.row.division_id"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option
                      v-for="item in divisionsOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="业绩要求">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.performance_requirements"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="委托代理协议扫描件" name="5">
          <el-upload
            ref="upload1"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="标段" name="6">
          <div>
            <el-table
              :data="form.bid_segments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_number"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="标段名称" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_name"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="标段分类代码" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_code"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column> -->
              <el-table-column label="是否双盲" width="180">
                <template slot-scope="scope">
                  <el-select
                    disabled
                    v-model="scope.row.is_blind"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="否" :value="0"></el-option>
                    <el-option label="是" :value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="标段内容">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_content"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="合同估价（元）">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.estimated_contract_price"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="预开工日期" width="200">
                <template slot-scope="scope">
                  <el-date-picker
                    disabled
                    v-model="scope.row.start_date"
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column label="工期（天）">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.duration_days"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="投标资格">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.bid_qualification"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="审核记录" name="7" v-if="false">
          <el-table :data="logData" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <!-- <div style="margin-top: 10px">
        <el-form
          size="mini"
          ref="auditForm"
          :model="auditForm"
          :rules="rules"
          label-width="180px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="审核备注" prop="approval_view">
                <el-input
                  :disabled="isDisabled"
                  type="textarea"
                  v-model="auditForm.approval_view"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核状态" prop="status">
                <el-radio-group
                  :disabled="isDisabled"
                  v-model="auditForm.status"
                >
                  <el-radio :label="2">审核通过</el-radio>
                  <el-radio :label="3">审核退回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
        <!-- <el-button @click="handleClose">取 消</el-button> -->
        <!-- <el-button type="primary" :disabled="isDisabled" @click="handleSubmit"
          >提交审核</el-button
        > -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTitles,
  getDivisions,
  getBidderByName,
} from "@/api/bid-manage/bid-project";
import {
  projectTypeOptions,
  industryProjectOptions,
  departmentTypeOptions,
  buyDepartmentTypeOptions,
  buyProjectTypeOptions,
} from "@/utils/data";
import SearchForm from "@/components/SearchForm/index.vue";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
  },
  props: {
    editForm: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    addressOptions: {
      type: Array,
    },
    industryOptions: {
      type: Array,
      default: () => [],
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          this.logData = val.approvalLog;
          if (this.form.status !== 1) {
            this.isDisabled = true;
          }
          console.log(val);
        }
      },
      deep: true,
    },
    "form.bidding_project_cate": {
      handler(val) {
        if (val == 1) {
          this.labelText = "招标";
        } else {
          this.labelText = "采购";
        }
      },
    },
  },
  created() {
    getTitles().then((res) => {
      this.titlesOptions = res.data.data;
    });
    getDivisions().then((res) => {
      this.divisionsOptions = res.data.data;
    });
  },
  data() {
    return {
      labelText: "招标",
      isDisabled: false,
      auditForm: {},
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        agency_contract_scan: [],
        approval_department: null,
        bid_segments: [],
        bidding_agent_scope: null,
        bidding_method: null,
        bidding_project_code: null,
        bidding_project_establishment_date: null,
        bidding_project_implementation_location: [],
        bidding_project_industry_classification: null,
        bidding_project_location: [],
        bidding_project_name: null,
        bidding_project_type: null,
        investment_amount: null,
        joint_bidding: null,
        owner_unit: null,
        project_code: null,
        project_industry_classification: [],
        project_initiation_date: null,
        project_legal_person: null,
        project_name: null,
        project_qualification_overview: null,
        project_team_assignments: [],
        qualification_review: "0",
        status: null,
        supervisory_department: null,
        supervisory_department_type: null,
        organizational_form: "委托招标",
        bidding_agent: "招标公司",
        bidder: null,
        bidding_agent_scope: null,
        bidding_agent_authority: null,
        task_plan: null,
      },
      titlesOptions: [],
      divisionsOptions: [],
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      logData: [],
      totalLog: 0,
      queryFormLog: {
        page: 1,
        per_page: 10,
      },

      projectTypeOptions: projectTypeOptions,
      buyProjectTypeOptions: buyProjectTypeOptions,
      buyDepartmentTypeOptions: buyDepartmentTypeOptions,
      industryProjectOptions: industryProjectOptions,
      departmentTypeOptions: departmentTypeOptions,
      addressProps: {
        label: "name",
        value: "id",
      },
      loading: false,
      rules: {
        status: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.yztc2025.com/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              this.auditForm.project_id = this.form.project_id;
              this.auditForm.bidding_project_id = this.form.id;
              this.auditForm.bidding_project_name =
                this.form.bidding_project_name;
              this.auditForm.company_id = this.form.company_info_id;
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handleCheckBidder() {
      if (this.form.bidder == null || this.form.bidder == "") {
        this.$message.warning("请输入招标人");
        return;
      }
      getBidderByName(this.form.bidder).then((res) => {
        this.message(res.status_code, "查验成功");
      });
    },
    message(status, message) {
      if (status) {
        this.$message({
          message,
          type: "success",
        });
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
