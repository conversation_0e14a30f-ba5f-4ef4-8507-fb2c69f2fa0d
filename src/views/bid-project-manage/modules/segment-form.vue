<template>
  <div>
    <el-dialog
      title="招标项目标段表单"
      :visible.sync="visible"
      width="90%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      disabled
                      v-model="form.bidding_project_name"
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="委托代理协议扫描件" name="2">
          <el-upload
            ref="upload1"
            action=""
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="标段" name="3">
          <div>
            <div style="text-align: right"></div>
            <el-table
              :data="form.bid_segments"
              style="width: 100%; margin-top: 10px"
              @selection-change="handleSelectionChange"
              v-loading="loading"
              border
            >
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_number"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="标段名称" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_name"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <!-- <el-table-column label="标段分类代码" width="180">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_code"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column> -->
              <el-table-column label="是否双盲" width="180">
                <template slot-scope="scope">
                  <el-select
                    disabled
                    v-model="scope.row.is_blind"
                    placeholder="请选择"
                    size="small"
                  >
                    <el-option label="否" :value="0"></el-option>
                    <el-option label="是" :value="1"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="标段内容">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.segment_content"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="合同估价">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.estimated_contract_price"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="预开工日期" width="200">
                <template slot-scope="scope">
                  <el-date-picker
                    disabled
                    v-model="scope.row.start_date"
                    size="small"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                </template>
              </el-table-column>
              <el-table-column label="工期（天）">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.duration_days"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="投标资格">
                <template slot-scope="scope">
                  <el-input
                    disabled
                    v-model="scope.row.bid_qualification"
                    size="small"
                  ></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>

        <el-collapse-item title="审核记录" name="4" v-if="false">
          <el-table
            :data="form.agency_contract_scan_logs"
            border
            style="margin-top: 10px"
          >
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <!-- <div style="margin-top: 10px">
        <el-form
          size="mini"
          ref="auditForm"
          :model="auditForm"
          :rules="rules"
          label-width="180px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="审核备注" prop="approval_view">
                <el-input
                  :disabled="isDisabled"
                  type="textarea"
                  v-model="auditForm.approval_view"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核状态" prop="status">
                <el-radio-group
                  :disabled="isDisabled"
                  v-model="auditForm.status"
                >
                  <el-radio :label="2">审核通过</el-radio>
                  <el-radio :label="3">审核退回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div> -->

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>

        <!-- <el-button @click="handleClose">{{
          isDisabled ? "关 闭" : "取 消"
        }}</el-button>
        <el-button type="primary" v-if="!isDisabled" @click="handleSubmit"
          >提交审核</el-button
        > -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "AddForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    name: {
      type: String,
      default: () => "",
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          if (val.status != 1) {
            this.isDisabled = true;
          }
        }
      },
      deep: true,
    },
  },
  created() {
    console.log(this.name);
    if (!this.form.id) {
      this.form.bidding_project_name = this.name;
    }
  },
  data() {
    return {
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4"],
      form: {},
      auditForm: {},
      isDisabled: false,
      titlesOptions: [],
      divisionsOptions: [],
      innerVisible: false,
      total: 0,
      selectedRows: [],
      queryForm: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },

      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        status: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
      selectProject: [],
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    handleSelectionChange(data) {
      this.selectedRows = data;
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("提交成功");
        } else {
          console.log("验证失败");
          return false;
        }
      });
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    addBidSegment() {
      this.form.bid_segments.push({
        segment_number: null,
        segment_name: null,
        segment_code: null,
        is_blind: null,
        segment_content: null,
        estimated_contract_price: null,
        start_date: "",
        duration_days: null,
        bid_qualification: null,
        index: this.generateUniqueId(),
      });
    },
    handleBatchDelete() {
      this.selectedRows.forEach((item) => {
        let index = this.form.bid_segments.filter((data) => {
          return item.index == data.index;
        });
        if (index != -1) {
          this.form.bid_segments.splice(index, 1);
        }
      });
    },
    generateUniqueId() {
      return Date.now().toString(36);
    },
    handleDeleteBid(index, row) {
      this.form.bid_segments.splice(index, 1);
    },
    handleSave() {
      console.log("save", this.form);
      this.$emit("save", this.form);
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              this.auditForm.agency_contract_scan_id = this.form.id;
              this.auditForm.company_id = this.form.company_info_id;
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    handleSuccess(res, file, fileList) {
      this.form.agency_contract_scan.push(res.data);
      console.log(res, file, fileList);
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleRemove(file, fileList) {
      let index = this.form.agency_contract_scan.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.agency_contract_scan.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
