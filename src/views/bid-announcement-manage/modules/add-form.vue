<template>
  <div>
    <el-dialog
      title="招标公告表单"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="招标项目信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目:" prop="bidding_project_name">
                  <div style="display: flex">
                    <el-input
                      disabled
                      v-model="form.bidding_project_name"
                    ></el-input>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="bidding_project_code">
                  <el-input
                    disabled
                    v-model="form.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>

        <el-collapse-item title="标段" name="2">
          <div>
            <el-table
              :data="form.bid_segments"
              style="width: 100%; margin-top: 10px"
              v-loading="loading"
              border
            >
              <el-table-column label="序号" type="index" width="50">
              </el-table-column>
              <el-table-column label="标段编号" prop="segment_number">
              </el-table-column>
              <el-table-column label="标段名称" prop="segment_name">
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="时间设置" name="3">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标公告:" prop="title">
                  <el-input
                    disabled
                    v-model="form.title"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="联合体投标:" prop="joint_bidding">
                  <el-select
                    disabled
                    v-model="form.joint_bidding"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="接受" value="0"> </el-option>
                    <el-option key="1" label="不接受" value="1">
                    </el-option> </el-select></el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="公告发布时间:"
                  prop="announcement_start_time"
                >
                  <el-date-picker
                    disabled
                    v-model="form.announcement_start_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="公告截至时间:"
                  prop="announcement_end_time"
                >
                  <el-date-picker
                    disabled
                    v-model="form.announcement_end_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取时间:"
                  prop="file_obtain_start_time"
                >
                  <el-date-picker
                    disabled
                    v-model="form.file_obtain_start_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="招标文件获取截止时间:"
                  prop="file_obtain_end_time"
                >
                  <el-date-picker
                    disabled
                    v-model="form.file_obtain_end_time"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <!-- <el-col :span="12">
                <el-form-item label="保证金截止时间:" prop="bidder">
                  <el-date-picker disabled
                    v-model="form.announcement_start_time"
                    type="datetime"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col> -->
              <el-col :span="12">
                <el-form-item
                  label="投标文件递交截止时间:"
                  prop="submission_deadline"
                >
                  <el-date-picker
                    disabled
                    v-model="form.submission_deadline"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                  ></el-date-picker> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="详细内容" name="4">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item
                  label="公告发布类别:"
                  prop="announcement_category"
                >
                  <el-select
                    disabled
                    v-model="form.announcement_category"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="工程" value="0"> </el-option>
                    <el-option key="1" label="货物" value="1"> </el-option>
                    <el-option key="2" label="服务" value="2"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="资审方式:" prop="qualification_review">
                  <el-select
                    disabled
                    v-model="form.qualification_review"
                    placeholder="请选择"
                  >
                    <el-option key="0" label="资格后审" value="0"> </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item label="招标文件获取方法:" prop="file_obtain_method">
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.file_obtain_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="投标文件递交方法:" prop="submission_method">
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.submission_method"
                ></el-input> </el-form-item
            ></el-row>
            <el-row>
              <el-form-item label="开标方式:" prop="opening_method">
                <el-input
                  disabled
                  type="textarea"
                  v-model="form.opening_method"
                ></el-input> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="附件" name="5">
          <el-upload
            ref="upload1"
            :file-list="fileList"
            :on-preview="handlePreview"
            :before-remove="handleRemove"
          >
          </el-upload>
        </el-collapse-item>
        <el-collapse-item title="公告详情" name="6">
          <Tinymce ref="editor" v-model="form.details" :height="400" />
        </el-collapse-item>
        <!-- <el-collapse-item title="是否业主审核" name="7">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-form-item label="业主审核:" prop="task_plan">
                <el-select disabled
                  v-model="form.qualification_review"
                  placeholder="请选择"
                >
                  <el-option key="0" label="否" value="0"> </el-option>
                  <el-option key="1" label="是" value="1"> </el-option>
                </el-select> </el-form-item
            ></el-row>
          </el-form>
        </el-collapse-item> -->
        <el-collapse-item title="审核记录" name="7">
          <el-table :data="form.approvalLog" border style="margin-top: 10px">
            <!-- 表格列定义 -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="审核结果">
              <template slot-scope="scope">
                <span>{{ setStatusText(scope.row.status) }}</span>
              </template></el-table-column
            >
            <el-table-column
              prop="approval_view"
              label="审核备注"
            ></el-table-column>
            <el-table-column
              prop="approval_name"
              show-overflow-tooltip
              width="200"
              label="办理人姓名"
            ></el-table-column>

            <el-table-column
              prop="updated_at"
              label="办理时间"
            ></el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
      <div style="margin-top: 10px">
        <el-form
          size="mini"
          ref="auditForm"
          :model="auditForm"
          :rules="rules"
          label-width="180px"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="审核备注" prop="approval_view">
                <el-input
                  :disabled="isDisabled"
                  type="textarea"
                  v-model="auditForm.approval_view"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="审核状态" prop="status">
                <el-radio-group
                  :disabled="isDisabled"
                  v-model="auditForm.status"
                >
                  <el-radio :label="2">审核通过</el-radio>
                  <el-radio :label="3">审核退回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" :disabled="isDisabled" @click="handleSubmit"
          >提交审核</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import SearchForm from "@/components/SearchForm/index.vue";
import Tinymce from "@/components/Tinymce";
import { text } from "./text";
export default {
  name: "ProjectForm",
  components: {
    SearchForm,
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          this.form.bidding_project_name =
            val.bidding_project.bidding_project_name;
          this.form.bidding_project_code =
            val.bidding_project.bidding_project_code;
          this.form.qualification_review = "0";
          this.queryFormSegment.bidding_project_id =
            this.form.bidding_project_id;
          if (val.status !== 1) {
            this.isDisabled = true;
          }
        }
      },
      deep: true,
    },
  },
  created() {},
  data() {
    return {
      isDisabled: false,
      logData: [],
      auditForm: {},
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5", "6", "7"],
      form: {
        bidding_project_name: null,
        bidding_project_code: null,
        bidding_project_id: null,
        title: null,
        joint_bidding: null,
        announcement_start_time: null,
        announcement_end_time: null,
        file_obtain_start_time: null,
        file_obtain_end_time: null,
        submission_deadline: null,
        announcement_category: null,
        qualification_review: "0",
        file_obtain_method: null,
        submission_method: null,
        opening_method: null,
        bid_segments: [],
        details: text,
        attachment: [],
        status: 0,
      },
      innerVisible: false,
      selectSegmentShow: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      queryFormSegment: {
        page: 1,
        per_page: 10,
        bidding_project_id: null,
      },
      fields: [
        {
          label: "项目编号：",
          prop: "bidding_project_code",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      fieldsSegment: [
        {
          label: "标段编号：",
          prop: "segment_number",
          component: "el-input",
          props: {
            placeholder: "请输入项目编号",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入项目名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      rules: {
        status: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
      selectProject: [],
      tableDataSegment: [],
      totalSegment: 0,
    };
  },
  methods: {
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              console.log(this.form);
              this.auditForm.notice_id = this.form.id;
              this.auditForm.bidding_project_id = this.form.bidding_project_id;
              this.auditForm.title = this.form.title;
              this.auditForm.company_id =
                this.form.bidding_project.company_info_id;
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
