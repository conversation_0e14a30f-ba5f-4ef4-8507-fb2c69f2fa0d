<template>
  <div>
    <el-dialog
      title="报名公司审核"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="标段信息" name="1">
          <el-form
            ref="form"
            :model="form"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目编号:" prop="title">
                  <el-input
                    disabled
                    v-model="segmentForm.bidding_project_code"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="标段编号:" prop="title">
                  <el-input
                    disabled
                    v-model="form.segment_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="招标项目名称:" prop="bidding_project_name">
                  <el-input
                    disabled
                    v-model="segmentForm.bidding_project_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="标段名称:" prop="segment_name">
                  <el-input
                    disabled
                    v-model="segmentForm.segment_name"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="报名开始时间:" prop="title">
                  <el-date-picker
                    disabled
                    v-model="form.bid_document[0].document_obtain_start"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="报名结束时间:" prop="title">
                  <el-date-picker
                    disabled
                    v-model="form.bid_document[0].document_submission_deadline"
                    type="datetime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    placeholder="选择时间"
                    :picker-options="pickerOptions"
                  ></el-date-picker></el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="标段内容:" prop="title">
                  <el-input
                    disabled
                    type="textarea"
                    v-model="form.segment_content"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="经办人信息" name="2">
          <el-form
            ref="form"
            :model="form"
            label-width="240px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="经办人:" prop="agent_name">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.agent_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item
                  label="统一社会信用代码或纳税人识别号:"
                  prop="taxpayer_id"
                >
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.taxpayer_id"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="邮箱:" prop="email">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.email"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="地址:" prop="address">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.address"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="手机:" prop="phone">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.phone"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-form
          ref="form"
          :model="form"
          label-width="230px"
          status-icon
          size="mini"
        >
          <el-collapse-item title="项目联系人信息" name="3">
            <el-row>
              <el-col :span="12">
                <el-form-item label="项目联系人:" prop="project_contact_name">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.project_contact_name"
                  ></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="身份证号:" prop="id_number">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.id_number"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="联系电话:" prop="project_contact_phone">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.project_contact_phone"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="相关证书和编号:" prop="certificates">
                  <el-input
                    :disabled="isDisabled"
                    v-model="segmentForm.registration.certificates"
                  ></el-input> </el-form-item
              ></el-col>
            </el-row>
          </el-collapse-item>
          <el-collapse-item title="附件" name="4" v-if="isDisabled">
            <el-upload
              ref="upload1"
              action="#"
              :file-list="fileList"
              :on-preview="handlePreview"
              :before-remove="handleRemove"
            >
            </el-upload>
          </el-collapse-item>

          <el-collapse-item title="报名材料" name="5" v-if="isDisabled">
            <el-upload
              ref="upload1"
              action="#"
              :file-list="applicaList"
              :on-preview="handlePreview"
              :before-remove="handleRemove"
            >
            </el-upload>
          </el-collapse-item>
        </el-form>

        <div style="margin-top: 10px">
          <el-form
            size="mini"
            ref="auditForm"
            :model="auditForm"
            :rules="rules"
            label-width="180px"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="审核备注" prop="comments">
                  <el-input
                    type="textarea"
                    :disabled="auditDisabled"
                    v-model="auditForm.comments"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审核状态" prop="status">
                  <el-radio-group
                    v-model="auditForm.status"
                    :disabled="auditDisabled"
                  >
                    <el-radio :label="1">审核通过</el-radio>
                    <el-radio :label="2">审核拒绝</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭 </el-button>
        <el-button type="primary" v-if="!auditDisabled" @click="handleSubmit"
          >提交审核</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
export default {
  name: "ProjectForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    segmentForm: {
      type: Object,
      default: () => {},
    },
    // fileList: {
    //   type: Array,
    //   default: () => [],
    // },
    applicaList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
        }
      },
      deep: true,
    },
    segmentForm: {
      handler(val) {
        if (val) {
          console.log(this.segmentForm.registration);
          if (this.segmentForm.registration.approvals == null) {
            this.auditDisabled = false;
          } else {
            this.auditForm = this.segmentForm.registration.approvals;
            this.auditDisabled = true;
          }
          this.segmentForm.registration.attachment.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      },
      deep: true,
    },
  },
  created() {},
  data() {
    return {
      auditForm: {},
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      fileList: [],
      isDisabled: true,
      auditDisabled: false,
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5"],
      form: {
        bidding_project_id: null,
        bid_segment_id: null,
        attachment: [],
        registration_material: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      companyList: [],
      rules: {
        status: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.url}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 0:
          text = "待发布";
          break;
        case 1:
          text = "待审核";
          break;
        case 2:
          text = "审核通过";
          break;
        case 3:
          text = "审核退回";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },

    handleSave() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.bidding_project_id = this.segmentForm.bidding_project.id;
          this.form.bid_segment_id = this.segmentForm.id;
          this.form.segment_content = this.segmentForm.segment_content;
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.$refs.auditForm.validate((valid) => {
            if (valid) {
              console.log(this.form);
              this.auditForm.registration_id = this.segmentForm.registration.id;
              this.$emit("submit", this.auditForm);
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachment.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachment.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachment.splice(index, 1);
      }
    },
    handleSuccess2(res, file, applicaList) {
      this.form.registration_material.push(res.data);
      console.log(res, file, applicaList);
    },
    handleRemove2(file, applicaList) {
      let index = this.form.registration_material.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.registration_material.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
