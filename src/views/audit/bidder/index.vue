<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-23 23:21:09
 * @FilePath: /bid/src/views/hall/bidder/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column
          width="180"
          show-overflow-tooltip
          prop="bidding_project.bidding_project_code"
          label="招标/采购项目编号"
        ></el-table-column>
        <el-table-column
          prop="bidding_project.bidding_project_name"
          show-overflow-tooltip
          label="招标/采购项目名称"
        >
        </el-table-column>

        <el-table-column
          prop="bid_segment.segment_name"
          show-overflow-tooltip
          label="标段名称"
        >
        </el-table-column>
        <el-table-column
          prop="bid_segment.segment_number"
          width="180"
          label="标段编号"
        >
        </el-table-column>

        <el-table-column
          prop="company_info.company_name"
          width="380"
          show-overflow-tooltip
          label="投标公司名称"
        >
        </el-table-column>

        <el-table-column width="100" prop="status" label="审核状态">
          <template slot-scope="scope">
            <span>{{ setRegText(scope.row.approvals) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="150">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >审核</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>
    <audit-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :segmentForm="segmentForm"
      :fileList="fileList"
      :applicaList="applicaList"
      @submit="handleSubmit"
      @close="addShow = false"
    ></audit-form>
  </div>
</template>

<script>
import { geInviteSegmentsDetail } from "@/api/bid-manage/invite_bid";
import {
  getAuditList,
  getAuditInfo,
  submitAudit,
} from "@/api/bid-manage/bid-project";
import { getGeoAreas } from "@/api/bid-manage/project-info";
import SearchForm from "@/components/SearchForm/index.vue";
import AuditForm from "./modules/audit-form.vue";
export default {
  components: {
    SearchForm,
    AuditForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      queryForm: {
        page: 1,
        per_page: 10,
      },
      bidData: {},
      fields: [
        {
          label: "招标/采购项目名称：",
          prop: "bidding_project_name",
          component: "el-input",
          props: {
            placeholder: "请输入招标项目名称",
          },
        },
        {
          label: "标段名称：",
          prop: "segment_name",
          component: "el-input",
          props: {
            placeholder: "请输入标段名称",
          },
        },
      ],
      tableData: [], // 过滤后的数据
      editForm: {},
      addressOptions: [],
      viewFileList: [],
      segmentForm: {},
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    getList() {
      getAuditList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    setRegText(data) {
      if (data === null) {
        return "待审核";
      }
      let val = data.status;
      if (val == 0 || val == null) {
        return "待审核";
      } else if (val == 1) {
        return "通过";
      } else if (val == 2) {
        return "拒绝";
      } else {
        return "";
      }
    },
    handleSubmit(data) {
      submitAudit(data).then((res) => {
        this.message(res.status_code, res.message);
      });
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },

    handleView() {
      this.mode = 1;
      this.$router.push("/hall");
    },
    handleIn(data) {
      this.bidData = data;
      console.log(this.bidData);
    },
    handleUpdate(data) {
      this.fileList = [];
      this.applicaList = [];
      this.addShow = true;
      geInviteSegmentsDetail(data.bid_segment.id).then((res) => {
        console.log(res);
        this.editForm = { ...res.data };
        console.log(this.editForm);

        console.log("this.fileList", this.fileList);
        this.editForm.registration.forEach((temp) => {
          if (temp.id === data.id) {
            temp.registration_material.forEach((item) => {
              let obj = {
                name: item.name,
                url: item.path,
              };
              this.applicaList.push(obj);
            });
          }
        });
      });
      getAuditInfo(data.id).then((res) => {
        this.segmentForm = res.data;
        console.log(this.editForm);
      });
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
