<template>
  <div>
    <el-dialog
      title="招标公告内部审核"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
    >
      <div class="content-wrapper">
        <div class="announcement">
          <h2 style="text-align: center">
            {{ bidData.bidding_project.bidding_project_name }}
          </h2>
          <div class="details">
            <!-- <p>发布时间：{{ bidData.publish_date }}</p>
            <p>查看次数：{{ bidData.num }}</p> -->
            <p>
              招标/采购项目编号：{{
                bidData.bidding_project.bidding_project_code
              }}
            </p>
            <!-- <p>所属行业：{{}}</p> -->
            <p>业主单位：{{ bidData.bidding_project.owner_unit }}</p>
            <p>
              招标项目地址：{{
                bidData.bidding_project.bidding_project_location
              }}
            </p>
            <p>
              招标组织形式：{{ bidData.bidding_project.organizational_form }}
            </p>
          </div>
          <el-steps :active="bidData.flow_status" finish-status="success">
            <el-step
              title="招标文件获取时间"
              :description="bidData.file_obtain_start_time"
            ></el-step>
            <el-step
              title="招标文件获取截止时间"
              :description="bidData.file_obtain_end_time"
            ></el-step>
            <el-step
              title="投标文件递交截止时间"
              :description="bidData.submission_deadline"
            ></el-step>
            <el-step
              title="开标时间"
              :description="bidData.submission_deadline"
            ></el-step>
          </el-steps>
        </div>
        <div id="content"></div>
        <div id="file">
          附件:<a
            style="margin-right: 10px; color: blue"
            v-for="item in bidData.attachment"
            :key="item.name"
            :href="'http://bidding.yztc2025.com/' + item.path"
            >{{ item.name }}</a
          >
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <div style="text-align: center">
          <el-button @click="handleClose">关 闭 </el-button>
          <el-button
            type="primary"
            v-show="bidData.status == 4"
            @click="handleSubmit(1)"
            >审核通过</el-button
          >
          <el-button
            type="danger"
            v-show="bidData.status == 4"
            @click="handleSubmit(0)"
            >审核退回</el-button
          >
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import { getNoticesDetail } from "@/api/home/<USER>";
export default {
  name: "ProjectForm",
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    bidData: {
      type: Object,
      default: () => {},
    },

    id: {
      type: Number,
      default: () => 0,
    },

    applicaList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
        }
      },
      deep: true,
    },
    visible: {
      handler(val) {
        console.log(val);

        if (val) {
          console.log(this.bidData);
        }
      },
      deep: true,
    },
    segmentForm: {
      handler(val) {
        if (val) {
          console.log(this.segmentForm.registration);
          if (this.segmentForm.registration.approvals == null) {
            this.auditDisabled = false;
          } else {
            this.auditForm = this.segmentForm.registration.approvals;
            this.auditDisabled = true;
          }
          this.segmentForm.registration.attachment.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      var element = document.createElement("div");
      element.innerHTML = this.bidData.details;
      console.log(this.bidData);
      document.getElementById("content").appendChild(element);
    });
  },
  data() {
    return {
      auditForm: {},
      pickerOptions: {
        disabledDate(time) {
          // 返回true表示禁用该日期，禁用当前时间之前的所有日期
          return time.getTime() < Date.now();
        },
      },
      fileList: [],
      isDisabled: true,
      auditDisabled: false,
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3", "4", "5"],
      form: {
        bidding_project_id: null,
        bid_segment_id: null,
        attachment: [],
        registration_material: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      companyList: [],
      rules: {
        status: [
          { required: true, message: "请选择审核结果", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },

    handleSubmit(result) {
      this.$confirm("确认要提交审核结果吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.auditForm.status = result;
          this.auditForm.notice_id = this.bidData.id;
          this.auditForm.notice_status = 6;
          this.$emit("submit", this.auditForm);
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
#content {
  margin-top: 20px;
  margin-bottom: 20px;
  padding-left: 10%;
  padding-right: 10%;
}
.announcement {
  width: 80%;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
}

.details p {
  margin: 25px 0;
}

.el-steps {
  margin-top: 20px;
}
#file {
  background-color: #c6c6c6;
  height: 40px;
  line-height: 40px;
  padding: 0px 20px;
  padding-left: 10%;
  padding-right: 10%;
}
</style>
