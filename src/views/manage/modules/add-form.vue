<template>
  <div>
    <el-dialog
      title="公告表单"
      :visible.sync="visible"
      width="80%"
      :before-close="handleClose"
      :modal="false"
    >
      <el-collapse v-model="collapse">
        <el-collapse-item title="基本信息" name="1">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="230px"
            status-icon
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="公告名称:" prop="title">
                  <el-input v-model="form.title"></el-input> </el-form-item
              ></el-col>
              <el-col :span="12">
                <el-form-item label="公告类别:" prop="announcement_cate">
                  <el-select
                    v-model="form.announcement_cate"
                    placeholder="请选择"
                  >
                    <el-option key="1" label="法律法规" :value="1"> </el-option>
                    <el-option key="2" label="地方政策" :value="2"> </el-option>
                    <el-option key="3" label="收费标准" :value="3"> </el-option>
                    <el-option key="4" label="企业文化" :value="4"></el-option>
                    <el-option key="5" label="其他公告" :value="5">
                    </el-option></el-select></el-form-item
              ></el-col>
            </el-row>
          </el-form>
        </el-collapse-item>
        <el-collapse-item title="公告详情" name="2">
          <Tinymce ref="editor" v-model="form.content" :height="400" />
        </el-collapse-item>
        <el-collapse-item title="相关附件" name="3">
          <el-upload
            ref="upload1"
            action="https://bidding.senmoio.cn/api/file"
            :headers="header"
            :accept="acceptTypes.join(',')"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :on-remove="handleRemove1"
            :on-success="handleSuccess1"
          >
            <el-button slot="trigger" size="small" type="primary"
              >选取文件</el-button
            >
            <div slot="tip" class="el-upload__tip">
              可以上传的文件后缀为doc、docx、zip、pdf、ezb、png、jpg、jpeg
            </div>
          </el-upload>
        </el-collapse-item>
      </el-collapse>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button type="primary" @click="handleSubmit">递交生效</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
import Tinymce from "@/components/Tinymce";
export default {
  name: "ProjectForm",
  components: {
    Tinymce,
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    editForm: {
      type: Object,
      default: () => {},
    },
    fileList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    editForm: {
      handler(val) {
        if (val) {
          this.form = { ...val };
          if (this.form.attachments == null) {
            this.form.attachments = [];
          }
        }
      },
      deep: true,
    },
  },
  created() {
    let user = this.$store.getters.user;
    let obj = {
      label: user.company_info.company_name,
      value: user.company_info.id,
    };
    this.companyList.push(obj);
    console.log(user);
  },
  data() {
    return {
      acceptTypes: [
        ".doc",
        ".docx",
        ".zip",
        ".pdf",
        ".ezb",
        ".png",
        ".jpg",
        ".jpeg",
      ],
      collapse: ["1", "2", "3"],
      form: {
        status: 0,
        title: "",
        announcement_cate: null,
        content: "",
        attachments: [],
      },
      header: {
        Authorization: "Bearer " + getToken(),
      },
      loading: false,
      companyList: [],
      rules: {
        title: [
          {
            required: true,
            message: "请输入公告名称",
            trigger: ["blur", "change"],
          },
        ],
        announcement_cate: [
          {
            required: true,
            message: "请选择公告类别",
            trigger: ["blur", "change"],
          },
        ],
      },
    };
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },

    handleSave() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("save", this.form);
        }
      });
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("submit", this.form);
        }
      });
    },
    beforeUpload(file) {
      const extension = file.name.split(".").pop().toLowerCase();
      const allowedExtensions = this.acceptTypes;
      console.log(extension);
      if (!allowedExtensions.includes("." + extension)) {
        this.$message.error("不支持的文件格式");
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 100;
      if (!isLt10M) {
        this.$message.error("上传文件大小不能超过 100MB!");
        return false;
      }
      return true;
    },
    handleSuccess1(res, file, fileList) {
      this.form.attachments.push(res.data);
      console.log(res, file, fileList);
    },
    handleRemove1(file, fileList) {
      let index = this.form.attachments.filter((item) => {
        return item.path == file.url;
      });
      if (index != -1) {
        this.form.attachments.splice(index, 1);
      }
    },
  },
};
</script>

<style scoped>
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
