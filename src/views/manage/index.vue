<!--
 * @Author: wzc
 * @Date: 2024-11-02 22:12:58
 * @LastEditors: wzc
 * @LastEditTime: 2024-12-01 20:05:21
 * @FilePath: /bid/src/views/bidding/bid-half-announcement/index.vue
 * @copyright: sunkaisens
 * @Description: 
-->
<template>
  <div class="main">
    <el-card>
      <search-form
        :fields="fields"
        :itemsPerRow="2"
        @search="handleSearch"
      ></search-form>
    </el-card>
    <el-card class="table">
      <el-button type="primary" @click="handleAdd">新建</el-button>
      <el-table :data="tableData" border style="margin-top: 10px">
        <!-- 表格列定义 -->
        <el-table-column
          type="index"
          label="序号"
          width="50"
          align="center"
        ></el-table-column>

        <el-table-column prop="announcement_cate" width="180" label="公告类型">
          <template slot-scope="scope">
            {{ setTypeText(scope.row.announcement_cate) }}
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="title"
          label="公告名称"
        ></el-table-column>

        <el-table-column
          width="180"
          prop="created_at"
          label="发布日期"
        ></el-table-column>

        <el-table-column
          prop="user_name"
          show-overflow-tooltip
          width="120"
          label="发布人"
        ></el-table-column>

        <el-table-column width="100" prop="status" label="公告状态">
          <template slot-scope="scope">
            <span>{{ setStatusText(scope.row.status) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="180">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >

            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: center; padding: 20px 0px">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryForm.per_page"
          :current-page="queryForm.page"
          @size-change="handleChangeSize"
          @current-change="handleChangePage"
        >
        </el-pagination>
      </div>
    </el-card>

    <add-form
      v-if="addShow"
      :visible="addShow"
      :editForm="editForm"
      :fileList="fileList"
      @save="handleSave"
      @submit="handleSubmit"
      @close="addShow = false"
    ></add-form>
  </div>
</template>

<script>
import {
  getNoticesList,
  addNotice,
  updateNotice,
  deleteNotice,
  getNoticeById,
} from "@/api/home/<USER>";
import SearchForm from "@/components/SearchForm/index.vue";
import AddForm from "./modules/add-form.vue";

export default {
  components: {
    SearchForm,
    AddForm,
  },
  data() {
    return {
      addShow: false,
      total: 0,
      editForm: {},
      fileList: [],
      queryForm: {
        page: 1,
        per_page: 10,
      },
      fields: [
        {
          label: "公告名称：",
          prop: "title",
          component: "el-input",
          props: {
            placeholder: "请输入公告名称",
          },
        },
        // {
        //   label: "公告类型",
        //   prop: "announcement_cate",
        //   component: "el-select",
        //   props: {
        //     placeholder: "请选择公告类型",
        //   },

        //   options: [
        //     { label: "全部", value: null },
        //     { label: "法律法规", value: 1 },
        //     { label: "地方政策", value: 2 },
        //     { label: "收费标准", value: 3 },
        //     { label: "企业文化", value: 4 },
        //     { label: "其他公告", value: 5 },
        //   ],
        // },
      ],
      tableData: [], // 过滤后的数据
    };
  },
  methods: {
    handleSearch(filters) {
      Object.assign(this.queryForm, filters);
      this.getList();
    },
    handleAdd() {
      this.addShow = true;
    },
    handleUpdate(data) {
      this.addShow = true;
      getNoticeById(data.id).then((res) => {
        this.editForm = res.data;
        this.fileList = [];
        if (this.editForm.attachments != null) {
          this.editForm.attachments.forEach((item) => {
            let obj = {
              name: item.name,
              url: item.path,
            };
            this.fileList.push(obj);
          });
        }
      });
    },
    getList() {
      getNoticesList(this.queryForm).then((res) => {
        this.tableData = res.data.data;
        this.total = res.data.total;
      });
    },
    handleSave(data) {
      let formData = { ...data };
      formData.status = 1;
      if (data.id) {
        updateNotice(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addNotice(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleSubmit(data) {
      let formData = { ...data };
      formData.status = 2;
      if (data.id) {
        updateNotice(data.id, formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      } else {
        addNotice(formData).then((res) => {
          this.message(res.status_code, res.message);
        });
      }
    },
    handleChangeSize(number) {
      this.queryForm.page = 1;
      this.queryForm.per_page = number;
      this.getList();
    },
    handleChangePage(page) {
      this.queryForm.page = page;
      this.getList();
    },
    handleDelete(index, row) {
      this.$confirm("确认删除这条记录吗?", "提示", {
        type: "warning",
      })
        .then(() => {
          deleteNotice(row.id).then((res) => {
            this.message(res.status_code, res.message);
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    setStatusText(value) {
      let text = "";
      switch (value) {
        case 1:
          text = "待发布";
          break;
        case 2:
          text = "已发布";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    setTypeText(status) {
      let text = "";
      switch (status) {
        case 1:
          text = "法律法规";
          break;
        case 2:
          text = "地方政策";
          break;
        case 3:
          text = "收费标准";
          break;
        case 4:
          text = "企业文化";
          break;
        case 5:
          text = "其他公告";
          break;
        default:
          text = "未知";
          break;
      }
      return text;
    },
    message(status, message) {
      if (status == 200) {
        this.$message({
          message,
          type: "success",
        });
        this.addShow = false;
        this.getList();
      } else {
        this.$message({
          message,
          type: "error",
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>
<style scoped>
.main {
  padding: 10px;
}
.table {
  margin-top: 10px;
}
</style>
