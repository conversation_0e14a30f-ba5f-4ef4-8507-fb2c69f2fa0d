<template>
  <div class="main">
    <el-collapse v-model="collapse">
      <el-collapse-item title="发布信息" name="1">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          status-icon
          size="mini"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="专家姓名:" prop="name">
                <el-input
                  disabled
                  v-model="form.name"
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="身份证号:" prop="number_id">
                <el-input
                  disabled
                  v-model="form.number_id"
                ></el-input> </el-form-item
            ></el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="性别:" prop="gender">
                <el-select disabled v-model="form.gender" placeholder="请选择">
                  <el-option key="0" label="男" :value="0"> </el-option>
                  <el-option key="1" label="女" :value="1"> </el-option>
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="出生日期:" prop="birthday">
                <el-date-picker
                  disabled
                  v-model="form.birthday"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                ></el-date-picker> </el-form-item
            ></el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="民族:" prop="nation">
                <el-input
                  disabled
                  v-model="form.nation"
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="政治面貌:" prop="political_status">
                <el-input
                  disabled
                  v-model="form.political_status"
                ></el-input> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
      </el-collapse-item>
      <el-collapse-item title="详情信息" name="2">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          status-icon
          size="mini"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="专家类别:" prop="expert_type">
                <el-cascader
                  disabled
                  v-model="form.expert_type"
                  :props="expertProps"
                  :options="expertOptions"
                  placeholder="请选择"
                ></el-cascader> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="所属地区:" prop="district">
                <el-cascader
                  disabled
                  v-model="form.district"
                  :props="addressProps"
                  :options="addressOptions"
                  placeholder="请选择"
                ></el-cascader> </el-form-item
            ></el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="专家专业:" prop="expert_major">
                <el-input
                  disabled
                  v-model="form.expert_major"
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="专家职称:" prop="expert_title">
                <el-select
                  disabled
                  v-model="form.expert_title"
                  placeholder="请选择"
                >
                  <el-option key="0" label="正高级" value="0"> </el-option>
                  <el-option key="1" label="副高级" value="1"> </el-option>
                  <el-option key="2" label="中级" value="2"> </el-option>
                  <el-option key="3" label="初级" value="3"> </el-option>
                </el-select> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
      </el-collapse-item>
      <el-collapse-item title="从业信息" name="3">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          status-icon
          size="mini"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label=" 从业单位:" prop="unit">
                <el-input
                  disabled
                  v-model="form.unit"
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="组织机构代码:" prop="organization_code">
                <el-input
                  disabled
                  v-model="form.organization_code"
                ></el-input> </el-form-item
            ></el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label=" 退休状态:" prop="retirement_status">
                <el-select
                  disabled
                  v-model="form.retirement_status"
                  placeholder="请选择"
                >
                  <el-option key="0" label="正常" value="0"> </el-option>
                  <el-option key="1" label="返聘" value="1"> </el-option>
                  <el-option key="2" label="退休" value="2"> </el-option>
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="参加工作时间:" prop="work_time">
                <el-date-picker
                  disabled
                  v-model="form.work_time"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期"
                ></el-date-picker> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
      </el-collapse-item>
      <el-collapse-item title="联系方式" name="4">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          status-icon
          size="mini"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label=" 手机号码:" prop="phone">
                <el-input
                  disabled
                  v-model="form.phone"
                ></el-input> </el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="电子邮箱:" prop="email">
                <el-input
                  disabled
                  v-model="form.email"
                ></el-input> </el-form-item
            ></el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="通讯地址:" prop="mail_address">
                <el-input
                  disabled
                  v-model="form.mail_address"
                ></el-input> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
      </el-collapse-item>
      <el-collapse-item title="附件信息" name="5">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px"
          status-icon
          size="mini"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label=" 职业证书:" prop="professional_certificate">
                <el-upload
                  ref="upload1"
                  :file-list="form.professional_certificate"
                  :on-preview="handlePreview"
                  :before-remove="handleRemove"
                >
                </el-upload></el-form-item
            ></el-col>
            <el-col :span="12">
              <el-form-item label="入驻证明:" prop="certificate_occupancy">
                <el-upload
                  ref="upload2"
                  :file-list="form.certificate_occupancy"
                  :on-preview="handlePreview"
                  :before-remove="handleRemove"
                >
                </el-upload> </el-form-item
            ></el-col>
          </el-row>
        </el-form>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { getExpertInfo } from "@/api/expert/index";
export default {
  name: "AddForm",
  props: {},
  watch: {},
  data() {
    return {
      collapse: ["1", "2", "3", "4", "5"],
      form: {
        status: 0,
        professional_certificate: [],
        certificate_occupancy: [],
      },
      addressProps: {
        label: "name",
        value: "id",
      },
      expertProps: {
        label: "name",
        value: "id",
        multiple: true,
      },
      loading: false,
      rules: {},
      form: {},
    };
  },
  created() {
    getExpertInfo().then((res) => {
      this.form = { ...res.data[0] };
      console.log(res);
    });
  },
  methods: {
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          this.$emit("close");
          //   done();
        })
        .catch((_) => {});
    },
    handleSave() {
      console.log("save", this.form);
      this.$emit("save", this.form);
    },
    handleSubmit() {
      console.log("submit", this.form);
      this.$emit("submit", this.form);
    },
    handlePreview(file, fileList) {
      console.log(file);
      let url = `http://bidding.senmoio.cn/${file.path}`;
      window.open(url, "_blank");
    },
    handleRemove() {
      return false;
    },
  },
};
</script>

<style scoped>
.main {
  padding: 10px;
}
/* 这里可以添加一些CSS样式来美化表单 */
.el-input,
.el-select,
.el-cascader,
.el-date-editor {
  width: 100%;
}
</style>
