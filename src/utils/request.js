/*
 * @Author: wzc
 * @Date: 2022-04-11 11:32:06
 * @LastEditors: wzc
 * @LastEditTime: 2024-11-09 16:08:14
 * @FilePath: /bid/src/utils/request.js
 * @copyright: sunkaisens
 * @Description: 
 */
import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import router from '../router'
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 5000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent

    if (store.getters.token) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = "Bearer " + getToken()
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    console.log(response.data);

    const res = response.data

    // if the custom code is not 20000, it is judged as an error.
    // if (res.code !== 20000) {
    //   Message({
    //     message: res.message || 'Error',
    //     type: 'error',
    //     duration: 5 * 1000
    //   })

    //   // 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
    //   if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
    //     // to re-login
    //     MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again', 'Confirm logout', {
    //       confirmButtonText: 'Re-Login',
    //       cancelButtonText: 'Cancel',
    //       type: 'warning'
    //     }).then(() => {
    //       store.dispatch('user/resetToken').then(() => {
    //         location.reload()
    //       })
    //     })
    //   }
    //   return Promise.reject(new Error(res.message || 'Error'))
    // } else {
    return res
    // }
  },
  async error => {
    console.log(error.response.data.status_code);
    if (error.response.config.responseType === 'blob') {
      const data = await handleBlobResponse(error.response);
      console.log('data', data);
      Message({
        message: data.message,
        type: 'error',
        duration: 5 * 1000
      })
    }
    let messageObj = error.response.data.message
    let message = ""
    console.log(error.response);
    if (typeof messageObj == 'string') {
      message = error.response.data.message
    } else {
      // 获取第一个键
      const firstKey = Object.keys(messageObj)[0];
      // 获取第一个键对应的值
      message = messageObj[firstKey][0];
    }
    console.log(Object.keys(messageObj));
    Message({
      message: message,
      type: 'error',
      duration: 5 * 1000
    })
    if (message.indexOf('401') !== -1 || error.response.data.status_code == 401) {
      window.location.reload()
    }
    return Promise.reject(error)
  }
)

function handleBlobResponse(res) {
  return new Promise((resolve, reject) => {
    if (res.data.type === 'application/json') {
      const reader = new FileReader();
      reader.onload = () => {
        try {
          resolve(JSON.parse(reader.result));
        } catch (error) {
          reject(new Error('解析 JSON 失败'));
        }
      };
      reader.onerror = () => reject(new Error('读取 Blob 失败'));
      reader.readAsText(res.data, 'utf-8');
    } else {
      resolve(res.data); // 直接返回非 JSON 的 Blob（如文件）
    }
  });
}

export default service
