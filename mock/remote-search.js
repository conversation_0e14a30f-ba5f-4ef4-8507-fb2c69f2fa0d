/*
 * @Author: wzc
 * @Date: 2022-04-11 11:32:06
 * @LastEditors: wzc
 * @LastEditTime: 2024-10-11 22:51:16
 * @FilePath: /bid/mock/remote-search.js
 * @copyright: sunkaisens
 * @Description: 
 */
const Mock = require('mockjs')

const NameList = []
const count = 100

for (let i = 0; i < count; i++) {
  NameList.push(Mock.mock({
    name: '@first'
  }))
}
NameList.push({ name: 'mock-Pan' })

module.exports = [
  // username search
  {
    url: '/vue-element-admin/search/user',
    type: 'get',
    response: config => {
      const { name } = config.query
      const mockNameList = NameList.filter(item => {
        const lowerCaseName = item.name.toLowerCase()
        return !(name && lowerCaseName.indexOf(name.toLowerCase()) < 0)
      })
      return {
        code: 20000,
        data: { items: mockNameList }
      }
    }
  },

  // transaction list
  {
    url: '/vue-element-admin/transaction/list',
    type: 'get',
    response: _ => {
      return {
        code: 20000,
        data: {
          total: 20,
          'items|20': [{
            order_no: '@guid()',
            timestamp: +Mock.Random.date('T'),
            username: '@name()',
            price: '@float(1000, 15000, 0, 2)',
            'status|1': ['success', 'pending']
          }]
        }
      }
    }
  }
],

  [
    {
      name: '招标管理',
      id: 1,
      isCheck: true,
      chirend: [
        {
          id: 2,
          parentId: 1,
          menu_name: '项目信息',
          isCheck: true
        }
      ]
    },
    {
      name: '招标管理',
      id: 3,
      isCheck: true,
      chirend: [
        {
          id: 4,
          parentId: 3,
          menu_name: '专家库抽取',
          isCheck: true
        },
        {
          id: 5,
          parentId: 3,
          menu_name: '异议/质疑',
          isCheck: true
        },
        {
          id: 5,
          parentId: 3,
          menu_name: '邀请项目监控',
          isCheck: true
        }
      ]
    },
  ]
